# ElyPOS - Microservices Point of Sale & Loan Management System

A modern, scalable microservices-based Point of Sale and Loan Management System built with **Bun**, **Elysia**, **MongoDB**, and **TypeScript**.

## 🏗️ Architecture Overview

```
elypos/
├── gateway/                    # API Gateway (Port 3000)
│   ├── src/
│   │   ├── index.ts           # Main Elysia app
│   │   ├── routes/            # Proxy routes to services
│   │   ├── plugins/           # Auth & RBAC middleware
│   │   ├── config/            # Environment configuration
│   │   └── utils/             # Service forwarding utilities
│   ├── .env
│   └── package.json
├── services/
│   ├── core/                  # Authentication & Company Service (Port 3001)
│   │   ├── src/
│   │   │   ├── modules/
│   │   │   │   ├── auth/      # User authentication
│   │   │   │   ├── company/   # Company management
│   │   │   │   ├── subscription/ # Subscription handling
│   │   │   │   └── payment/   # Payment processing
│   │   │   ├── db/            # Database client & utilities
│   │   │   └── config/        # Environment configuration
│   │   └── .env
│   │
│   ├── pos/                   # Point of Sale Service (Port 3002)
│   │   ├── src/
│   │   │   ├── modules/
│   │   │   │   ├── inventory/ # Product & stock management
│   │   │   │   ├── sales/     # Sales processing
│   │   │   │   └── invoices/  # Invoice generation
│   │   │   └── db/            # Database connection
│   │   └── .env
│   │
│   └── loan/                  # Loan Management Service (Port 3003)
│       ├── src/
│       │   ├── modules/
│       │   │   ├── loan-applications/ # Loan applications
│       │   │   ├── repayments/        # Payment tracking
│       │   │   └── schedules/         # Payment schedules
│       │   └── db/            # Database connection
│       └── .env
├── shared/                    # Shared utilities & types
│   ├── types/                 # TypeScript type definitions
│   ├── middlewares/           # Reusable middleware
│   ├── libs/                  # HTTP client & utilities
│   └── utils/                 # Logger & ID generation
├── docker-compose.yml         # Multi-service Docker setup
└── package.json              # Monorepo management
```

## ✨ Key Features

### 🔐 **Authentication & Authorization**
- JWT-based authentication with access/refresh tokens
- Role-based access control (super_admin, company_admin, manager, employee)
- Multi-tenant architecture with company isolation

### 🏪 **Point of Sale (POS)**
- Inventory management with stock tracking
- Sales processing with multiple payment methods
- Invoice generation and management
- Customer management with credit limits

### 💰 **Loan Management**
- Loan application processing
- Payment schedule generation
- Repayment tracking with status monitoring
- Document management for loan applications

### 🔧 **Technical Features**
- **Hex String IDs**: Custom 24-character hex IDs instead of MongoDB ObjectIds
- **No Default Values**: Strict environment validation - all variables required
- **Structured Logging**: Comprehensive logging across all services
- **Service Discovery**: Gateway-based routing with health checks
- **API Documentation**: Auto-generated Swagger/OpenAPI docs for each service

## 🚀 Quick Start

### Prerequisites
- [Bun](https://bun.sh/) (v1.0.0+)
- [MongoDB](https://www.mongodb.com/) (v5.0+)
- Node.js (v18+) - for compatibility

### 1. Clone & Install Dependencies
```bash
git clone <repository-url>
cd elypos
bun install
```

### 2. Install Service Dependencies
```bash
# Install all service dependencies at once
bun run install:all

# Or install individually
cd gateway && bun install
cd services/core && bun install
cd services/pos && bun install
cd services/loan && bun install
```

### 3. Set Up Environment Variables
Copy and configure environment files for each service:

```bash
# Copy environment templates
cp gateway/.env.example gateway/.env
cp services/core/.env.example services/core/.env
cp services/pos/.env.example services/pos/.env
cp services/loan/.env.example services/loan/.env
```

**Required Environment Variables:**
- `JWT_SECRET` - Secret key for JWT tokens
- `MONGODB_URI` - MongoDB connection string
- `LOG_LEVEL` - Logging level (INFO, DEBUG, WARN, ERROR)
- Service-specific ports and URLs

### 4. Start MongoDB
```bash
# Using Docker
docker run -d -p 27017:27017 --name mongodb mongo:7.0

# Or start local MongoDB instance
mongod
```

### 5. Run All Services
```bash
# Start all services in development mode
bun run dev

# Or start individual services
bun run dev:gateway    # Port 3000
bun run dev:core      # Port 3001  
bun run dev:pos       # Port 3002
bun run dev:loan      # Port 3003
```

## 📡 API Endpoints

### Gateway (Port 3000)
- `GET /` - Gateway status
- `GET /api/health` - Gateway health check
- `ALL /api/core/*` - Proxy to Core service
- `ALL /api/pos/*` - Proxy to POS service  
- `ALL /api/loan/*` - Proxy to Loan service
- `GET /swagger` - API documentation

### Core Service (Port 3001)
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Refresh tokens
- `GET /auth/validate/:token` - Validate token
- `GET /auth/users/:id` - Get user by ID
- `GET /auth/users/company/:companyId` - Get company users
- `PATCH /auth/users/:id` - Update user
- `DELETE /auth/users/:id` - Deactivate user

### POS Service (Port 3002)
- `GET /health` - Service health check
- *(Additional endpoints to be implemented)*

### Loan Service (Port 3003)
- `GET /health` - Service health check
- *(Additional endpoints to be implemented)*

## 🧪 Testing the API

### 1. Register a New User
```bash
curl -X POST http://localhost:3000/api/core/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "John",
    "lastName": "Doe",
    "companyId": "507f1f77bcf86cd799439011"
  }'
```

### 2. Login User
```bash
curl -X POST http://localhost:3000/api/core/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 3. Check Service Health
```bash
# Gateway health
curl http://localhost:3000/api/health

# Core service health (via gateway)
curl http://localhost:3000/api/core/health

# POS service health (via gateway)
curl http://localhost:3000/api/pos/health

# Loan service health (via gateway)
curl http://localhost:3000/api/loan/health
```

## 🐳 Docker Deployment

### Start with Docker Compose
```bash
# Start all services including MongoDB
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Environment Variables for Docker
Update `docker-compose.yml` with your environment-specific values:
- Database credentials
- JWT secrets
- Service URLs

## 📊 Monitoring & Logging

### Structured Logging
All services use structured JSON logging with:
- **Timestamp** - ISO 8601 format
- **Service Name** - Identifies the source service
- **Log Level** - ERROR, WARN, INFO, DEBUG
- **Context Data** - Relevant metadata for debugging

### Health Checks
Each service exposes a `/health` endpoint for monitoring:
- Service status
- Database connectivity
- Timestamp
- Version information

## 🔍 API Documentation

Interactive API documentation is available at:
- **Gateway**: http://localhost:3000/swagger
- **Core Service**: http://localhost:3001/swagger
- **POS Service**: http://localhost:3002/swagger
- **Loan Service**: http://localhost:3003/swagger

## 🛠️ Development

### Available Scripts
```bash
# Development (all services)
bun run dev

# Production build
bun run build

# Start production
bun run start

# Clean build files
bun run clean

# Install all dependencies
bun run install:all
```

### Project Structure Guidelines
- **Shared Code**: Place reusable utilities in `/shared`
- **Type Definitions**: Define interfaces in `/shared/types`
- **Environment Config**: Validate all env vars in `config/env.ts`
- **Database Operations**: Use helper methods in database client
- **Error Handling**: Implement consistent error responses
- **Logging**: Use structured logging throughout

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes
4. Add tests for new functionality
5. Commit your changes: `git commit -am 'Add new feature'`
6. Push to the branch: `git push origin feature/new-feature`
7. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔮 Roadmap

### Phase 1 (Current)
- ✅ Core authentication service
- ✅ API Gateway with routing
- ✅ Basic POS service structure
- ✅ Basic Loan service structure
- ✅ MongoDB integration with hex IDs
- ✅ Comprehensive logging system

### Phase 2 (Next)
- [ ] Complete POS inventory management
- [ ] Sales processing with payment integration
- [ ] Invoice generation and PDF export
- [ ] Company management and multi-tenancy
- [ ] Subscription and billing system

### Phase 3 (Future)
- [ ] Complete loan application workflow
- [ ] Payment schedule automation
- [ ] Reporting and analytics
- [ ] Real-time notifications
- [ ] Mobile app support
- [ ] Advanced security features

---

**Built with ❤️ using Bun, Elysia, MongoDB, and TypeScript** 