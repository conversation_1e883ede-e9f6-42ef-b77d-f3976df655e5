export interface LoanApplication {
  _id: string;
  companyId: string;
  customerId: string;
  amount: number;
  amountUSD?: number;
  currency: 'KHR' | 'USD';
  term: number; // in months
  interestRate: number;
  loanType: 'microfinance' | 'agricultural' | 'business' | 'personal' | 'education' | 'housing' | 'vehicle' | 'equipment';
  purpose: string;
  collateral?: {
    type: 'property' | 'vehicle' | 'equipment' | 'gold' | 'certificate' | 'guarantee' | 'group_guarantee';
    description: string;
    estimatedValue: number;
    location?: string;
  };
  guarantor?: {
    name: string;
    phone: string;
    relationship: string;
    address: string;
  };
  status: 'pending' | 'under_review' | 'approved' | 'rejected' | 'disbursed' | 'completed' | 'defaulted';
  applicationDate: Date;
  approvalDate?: Date;
  disbursementDate?: Date;
  rejectionReason?: string;
  documents: LoanDocument[];
  createdAt: Date;
  updatedAt: Date;
}

export interface LoanDocument {
  _id: string;
  type: 'identity_card' | 'passport' | 'family_book' | 'business_license' | 'income_statement' | 'bank_statement' | 'salary_certificate' | 'property_title' | 'vehicle_registration' | 'collateral_certificate' | 'guarantor_id' | 'agreement' | 'other';
  filename: string;
  url: string;
  description?: string;
  expiryDate?: Date;
  uploadedAt: Date;
}

export interface Loan {
  _id: string;
  companyId: string;
  applicationId: string;
  customerId: string;
  principalAmount: number;
  principalAmountUSD?: number;
  outstandingAmount: number;
  outstandingAmountUSD?: number;
  currency: 'KHR' | 'USD';
  exchangeRateAtDisbursement?: number;
  interestRate: number;
  term: number;
  monthlyPayment: number;
  monthlyPaymentUSD?: number;
  paymentFrequency: 'weekly' | 'bi_weekly' | 'monthly' | 'quarterly';
  gracePeriod?: number; // Grace period in days
  penaltyRate?: number; // Late payment penalty rate
  status: 'active' | 'completed' | 'defaulted' | 'restructured' | 'written_off' | 'suspended';
  disbursementDate: Date;
  maturityDate: Date;
  nextPaymentDate: Date;
  lastPaymentDate?: Date;
  daysInArrears?: number;
  totalPaidAmount?: number;
  totalPaidAmountUSD?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Repayment {
  _id: string;
  companyId: string;
  loanId: string;
  amount: number;
  amountUSD?: number;
  principalAmount: number;
  interestAmount: number;
  penaltyAmount?: number;
  currency: 'KHR' | 'USD';
  exchangeRate?: number;
  paymentMethod: 'cash' | 'aba_pay' | 'wing' | 'true_money' | 'pi_pay' | 'acleda_pay' | 'bank_transfer' | 'check' | 'mobile_banking';
  paymentDetails?: {
    transactionId?: string;
    bankName?: string;
    accountNumber?: string;
    checkNumber?: string;
  };
  paymentDate: Date;
  dueDate: Date;
  status: 'on_time' | 'late' | 'partial' | 'missed';
  reference?: string;
  collectedBy?: string; // Staff member who collected payment
  receiptNumber?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentSchedule {
  _id: string;
  loanId: string;
  installmentNumber: number;
  dueDate: Date;
  principalAmount: number;
  interestAmount: number;
  penaltyAmount?: number;
  totalAmount: number;
  totalAmountUSD?: number;
  currency: 'KHR' | 'USD';
  status: 'pending' | 'paid' | 'overdue' | 'partially_paid' | 'waived';
  paidAmount?: number;
  paidAmountUSD?: number;
  paidDate?: Date;
  daysPastDue?: number;
  penaltyAccrued?: number;
  lastReminderDate?: Date;
  createdAt: Date;
  updatedAt: Date;
} 