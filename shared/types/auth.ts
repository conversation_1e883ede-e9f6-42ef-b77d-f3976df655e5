export interface User {
  _id: string;
  email: string;
  password?: string;
  firstName: string;
  lastName: string;
  currentCompanyId?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthToken {
  _id: string;
  userId: string;
  token: string;
  type: 'access' | 'refresh';
  expiresAt: Date;
  createdAt: Date;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'password'>;
  accessToken: string;
  refreshToken: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  companyName: string;
  role?: 'company_admin' | 'manager' | 'employee';
}

export interface UserCompany {
  _id: string;
  userId: string;
  companyId: string;
  role: 'super_admin' | 'company_admin' | 'manager' | 'employee';
  isActive: boolean;
  joinedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface SwitchCompanyRequest {
  companyId: string;
}

export interface AuthContext {
  user: Omit<User, 'password'>;
  currentCompany?: {
    companyId: string;
    role: string;
  };
} 