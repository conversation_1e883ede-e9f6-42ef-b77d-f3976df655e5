export interface Company {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  ownerId: string; // Current owner of the company
  foundedDate?: Date;
  businessType: 'sole_proprietorship' | 'partnership' | 'private_limited' | 'public_limited' | 'branch_office' | 'representative_office' | 'ngo' | 'cooperative';
  businessLicense?: string; // Business license number
  taxId?: string; // Tax identification number
  vatNumber?: string; // VAT registration number
  address?: {
    street?: string;
    commune?: string; // Commune
    district?: string; // District
    province?: string;
    postalCode?: string;
    country?: string;
  };
  bankInfo?: {
    bankName: string;
    accountNumber: string;
    accountName: string;
    swift?: string;
  };

  isActive: boolean;
  settings: {
    timezone?: string;
    currency?: 'KHR' | 'USD';
    language?: 'en' | 'km' | 'both';
    taxRate?: number; // VAT rate (usually 10% in Cambodia)
    witholdingTaxRate?: number; // Withholding tax rate
    dateFormat?: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
    numberFormat?: 'US' | 'EU' | 'KH';
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface Investor {
  _id: string;
  companyId: string;
  userId: string;
  investmentAmount: number;
  investmentAmountUSD?: number;
  currency: 'KHR' | 'USD';
  exchangeRate?: number;
  equityPercentage: number; // Percentage of ownership
  investmentDate: Date;
  investmentType: 'initial' | 'additional' | 'bridge' | 'series_a' | 'series_b' | 'other';
  status: 'active' | 'divested' | 'transferred' | 'liquidated';
  vestingSchedule?: {
    totalMonths: number;
    cliffMonths: number;
    vestedMonths: number;
  };
  dividendRights: boolean;
  votingRights: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}



export interface OwnershipTransfer {
  _id: string;
  companyId: string;
  fromOwnerId: string;
  toOwnerId: string;
  transferDate: Date;
  transferType: 'sale' | 'gift' | 'inheritance' | 'merger' | 'other';
  transferAmount?: number;
  transferAmountUSD?: number;
  currency?: 'KHR' | 'USD';
  status: 'pending' | 'approved' | 'completed' | 'rejected' | 'cancelled';
  approvedBy?: string;
  approvedAt?: Date;
  completedAt?: Date;
  reason?: string;
  documents?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export type PosBusinessType =
  | 'RESTAURANT'
  | 'BAR'
  | 'BAKERY'
  | 'RETAIL_SHOP'
  | 'CLOTHING_STORE'
  | 'FURNITURE_STORE'
  | 'PHARMACY'
  | 'ELECTRONICS_STORE'
  | 'GROCERY_STORE'
  | 'BEAUTY_SALON'
  | 'SERVICE'
  | 'HOTEL'
  | 'GENERIC';

export type CouponType = 
  | 'PERCENTAGE_DISCOUNT'     // e.g., 20% off
  | 'FIXED_AMOUNT_DISCOUNT'   // e.g., $10 off
  | 'FREE_TRIAL_EXTENSION'    // e.g., +15 days to trial
  | 'FIRST_MONTH_FREE'        // First billing cycle free
  | 'BUY_X_GET_Y_FREE';       // Buy 2 modules get 1 free

export type CouponApplicability = 
  | 'ALL_MODULES'
  | 'SPECIFIC_MODULE'
  | 'POS_ONLY'
  | 'NON_POS_MODULES';

export interface Coupon {
  _id: string;
  code: string;                    // Unique coupon code (e.g., "SAVE20")
  name: string;                    // Human-readable name
  description: string;             // Description for users
  type: CouponType;
  value: number;                   // Discount amount or percentage
  applicability: CouponApplicability;
  applicableModules?: string[];    // Specific modules if SPECIFIC_MODULE
  
  // Usage restrictions
  maxUses: number;                 // 0 = unlimited, >0 = limited uses
  currentUses: number;             // How many times it's been used
  maxUsesPerCompany: number;       // 0 = unlimited per company
  
  // Validity period
  startDate: Date;
  endDate: Date;
  
  // Purchase requirements
  minimumPurchaseAmount?: number;  // Minimum spend to use coupon
  
  // Status
  isActive: boolean;
  
  // Metadata
  createdBy: string;               // Admin user who created it
  createdAt: Date;
  updatedAt: Date;
}

export interface CouponUsage {
  _id: string;
  couponId: string;
  couponCode: string;
  companyId: string;
  subscriptionId: string;
  
  // Usage details
  discountAmount: number;          // Actual discount applied
  originalAmount: number;          // Pre-discount amount
  finalAmount: number;             // Post-discount amount
  
  // Context
  appliedToModule: string;
  appliedAt: Date;
  
  // Status
  isActive: boolean;               // If discount is still being applied
  
  createdAt: Date;
  updatedAt: Date;
}

export interface CouponValidationResult {
  isValid: boolean;
  coupon?: Coupon;
  error?: string;
  discountAmount?: number;
  finalAmount?: number;
}

export interface AppliedCoupon {
  couponId: string;
  couponCode: string;
  discountAmount: number;
  appliedAt: Date;
}

export interface CompanySubscription {
  _id: string;
  companyId: string;
  module: 'POS' | 'LOAN' | 'ACCOUNTING' | 'ERP';
  active: boolean;
  price: number; // Calculated price per month
  originalPrice: number; // Price before any discounts
  currency: 'KHR' | 'USD';
  nextBillingDate: Date;
  posType?: PosBusinessType; // required if module === 'POS'
  subscribedAt: Date;
  lastBilledAt?: Date;
  // Trial system
  isTrialMode: boolean;
  trialStartDate?: Date;
  trialEndDate?: Date;
  trialDuration: number; // days
  // Coupon system
  appliedCoupons: AppliedCoupon[];
  createdAt: Date;
  updatedAt: Date;
}

export interface CompanyTrialStatus {
  _id: string;
  companyId: string;
  trialsUsed: number;
  maxTrials: number;
  availableTrials: number;
  trialHistory: Array<{
    module: string;
    posType?: PosBusinessType;
    startDate: Date;
    endDate: Date;
    status: 'active' | 'expired' | 'converted';
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface TrialInfo {
  companyId: string;
  module: string;
  posType?: PosBusinessType;
  isInTrial: boolean;
  trialStartDate?: Date;
  trialEndDate?: Date;
  daysRemaining?: number;
  canStartTrial: boolean;
  trialsUsed: number;
  trialsRemaining: number;
}

export interface BillingInfo {
  companyId: string;
  totalMonthlyAmount: number;
  totalMonthlyAmountUSD?: number;
  originalAmount: number; // Before coupons
  totalDiscount: number;  // Total coupon discounts
  currency: 'KHR' | 'USD';
  activeModules: Array<{
    module: string;
    price: number;
    originalPrice: number;
    discount: number;
    appliedCoupons: AppliedCoupon[];
    posType?: PosBusinessType;
  }>;
  nextBillingDate: Date;
  lastBilledAmount?: number;
  lastBilledDate?: Date;
}

export interface ModulePricing {
  ERP: number;
  LOAN: number;
  ACCOUNTING: number;
}

export interface PosPricing {
  RESTAURANT: number;
  BAR: number;
  BAKERY: number;
  RETAIL_SHOP: number;
  CLOTHING_STORE: number;
  FURNITURE_STORE: number;
  PHARMACY: number;
  ELECTRONICS_STORE: number;
  GROCERY_STORE: number;
  BEAUTY_SALON: number;
  SERVICE: number;
  HOTEL: number;
  GENERIC: number;
} 