// Price Level System
export interface PriceLevel {
  _id: string;
  companyId: string;
  name: string;
  description?: string;
  code: string; // Short code like 'RETAIL', 'WHOLESALE', 'VIP'
  priority: number; // Lower number = higher priority (0 = default)
  discountPercentage?: number; // Optional percentage discount from base price
  color?: string; // UI color for visual identification
  isDefault: boolean; // Only one default price level per company
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Product pricing for different price levels
export interface ProductPrice {
  _id: string;
  priceLevelId: string;
  priceKHR: number;
  priceUSD: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Restaurant-specific product features
export interface RestaurantProductFeatures {
  // Khmer cuisine specific
  spiceLevel?: 'mild' | 'medium' | 'hot' | 'extra_hot';
  isTraditionalKhmer?: boolean;
  region?: 'phnom_penh' | 'siem_reap' | 'battambang' | 'kampot' | 'other';
  
  // Meal categorization
  mealTimes?: ('breakfast' | 'lunch' | 'dinner' | 'snack' | 'dessert')[];
  
  // Dietary information
  dietaryRestrictions?: ('vegetarian' | 'vegan' | 'gluten_free' | 'dairy_free' | 'halal' | 'kosher')[];
  allergens?: ('nuts' | 'seafood' | 'dairy' | 'eggs' | 'soy' | 'wheat' | 'sesame')[];
  
  // Kitchen operations
  preparationTime?: number; // in minutes
  cookingMethod?: 'grilled' | 'fried' | 'steamed' | 'boiled' | 'raw' | 'baked' | 'stir_fried';
  kitchenNotes?: string;
  
  // Service options
  availableForTakeout?: boolean;
  availableForDelivery?: boolean;
  availableForDineIn?: boolean;
  
  // BBQ unlimited mode
  isBBQItem?: boolean;
  bbqCategory?: 'meat' | 'seafood' | 'vegetable' | 'side' | 'sauce';
  
  // Serving information
  servingSize?: string;
  servingUnit?: 'person' | 'plate' | 'bowl' | 'cup' | 'piece';
  portionSizes?: {
    small?: { price: number; priceUSD?: number };
    medium?: { price: number; priceUSD?: number };
    large?: { price: number; priceUSD?: number };
  };
}

export interface Product {
  _id: string;
  companyId: string;
  name: string;
  nameKhmer?: string; // Khmer name for bilingual menus
  description?: string;
  descriptionKhmer?: string;
  sku: string;
  barcode?: string;
  
  // Legacy price fields (kept for backward compatibility)
  price: number;
  priceUSD?: number;
  
  // New price level system
  prices?: ProductPrice[]; // Array of prices for different levels
  defaultPriceLevelId?: string; // Default price level for this product
  
  cost: number;
  costUSD?: number;
  stock: number;
  minStock: number;
  categoryId: string;
  unit: 'piece' | 'kg' | 'gram' | 'liter' | 'meter' | 'box' | 'pack' | 'dozen' | 'bottle' | 'can' | 'bag' | 'plate' | 'bowl' | 'cup' | 'serving';
  supplier?: string;
  importDate?: Date;
  expiryDate?: Date;
  batchNumber?: string;
  
  // Restaurant-specific features
  restaurantFeatures?: RestaurantProductFeatures;
  
  // Media
  images?: string[];
  thumbnail?: string;
  
  // Availability
  isAvailable?: boolean;
  availableFrom?: string; // Time format "HH:MM"
  availableUntil?: string; // Time format "HH:MM"
  daysAvailable?: ('monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday')[];
  
  // Modifiers and customizations
  modifiers?: ProductModifier[];
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductModifier {
  _id: string;
  name: string;
  nameKhmer?: string;
  type: 'single_select' | 'multi_select' | 'text_input' | 'number_input';
  required: boolean;
  options?: ModifierOption[];
  minSelections?: number;
  maxSelections?: number;
  textPlaceholder?: string;
  numberMin?: number;
  numberMax?: number;
  isActive: boolean;
}

export interface ModifierOption {
  _id: string;
  name: string;
  nameKhmer?: string;
  priceAdjustment: number; // Can be negative for discounts
  priceAdjustmentUSD?: number;
  isDefault?: boolean;
  isActive: boolean;
}

export interface Category {
  _id: string;
  companyId: string;
  name: string;
  nameKhmer?: string;
  description?: string;
  descriptionKhmer?: string;
  parentId?: string;
  icon?: string;
  color?: string;
  sortOrder: number;
  isActive: boolean;
  
  // Restaurant-specific category features
  restaurantFeatures?: {
    isKhmerTraditional?: boolean;
    mealTimeCategory?: 'breakfast' | 'lunch' | 'dinner' | 'snack' | 'dessert' | 'beverage';
    showInQRMenu?: boolean;
    qrMenuOrder?: number;
    kitchenPrintOrder?: number;
  };
  
  createdAt: Date;
  updatedAt: Date;
}

export interface CategoryTree {
  categories: Category[];
  totalCount: number;
  maxDepth: number;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
  parentId?: string;
  icon?: string;
  color?: string;
  order?: number;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  parentId?: string;
  icon?: string;
  color?: string;
  order?: number;
  isActive?: boolean;
}

export interface CategoryMoveRequest {
  targetParentId?: string;
  newOrder: number;
}

export interface Sale {
  _id: string;
  companyId: string;
  invoiceNumber: string;
  customerId?: string;
  items: SaleItem[];
  currency: 'KHR' | 'USD';
  exchangeRate?: number; // Exchange rate if converted
  subtotal: number;
  tax: number;
  discount: number;
  total: number;
  totalUSD?: number; // USD equivalent
  paymentMethod: 'cash' | 'aba_pay' | 'wing' | 'true_money' | 'pi_pay' | 'acleda_pay' | 'visa_card' | 'master_card' | 'bank_transfer' | 'credit' | 'installment';
  paymentDetails?: {
    transactionId?: string;
    bankName?: string;
    accountNumber?: string;
    paymentApp?: string;
  };
  status: 'pending' | 'completed' | 'cancelled' | 'refunded';
  cashierId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaleItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  unitPriceUSD?: number;
  totalPrice: number;
  totalPriceUSD?: number;
}

export interface Customer {
  _id: string;
  companyId: string;
  name: string;
  email?: string;
  phone?: string;
  address?: {
    street?: string;
    commune?: string;
    district?: string;
    province?: string;
    postalCode?: string;
  };
  customerType: 'individual' | 'business' | 'government' | 'ngo';
  taxId?: string; // For business customers
  creditLimit?: number;
  creditLimitUSD?: number;
  currentCredit?: number;
  currentCreditUSD?: number;
  preferredCurrency?: 'KHR' | 'USD';
  paymentTerms?: number; // Days for credit payment
  discountRate?: number; // Customer-specific discount
  createdAt: Date;
  updatedAt: Date;
}

export interface Invoice {
  _id: string;
  companyId: string;
  saleId: string;
  invoiceNumber: string;
  vatInvoiceNumber?: string; // VAT invoice number for tax compliance
  customerId?: string;
  items: SaleItem[];
  currency: 'KHR' | 'USD';
  exchangeRate?: number;
  subtotal: number;
  tax: number;
  witholdingTax?: number; // Withholding tax for certain transactions
  discount: number;
  total: number;
  totalUSD?: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  dueDate: Date;
  paymentStatus: 'unpaid' | 'partial' | 'paid' | 'overpaid';
  paidAmount?: number;
  paidAmountUSD?: number;
  lastPaymentDate?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
} 