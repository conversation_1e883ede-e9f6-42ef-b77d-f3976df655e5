export type PaymentMethod = 'KHQR' | 'ACLEDA_POS' | 'ACLEDA_MOBILE' | 'BANK_TRANSFER' | 'ACLEDA_ECOMMERCE' | 'ACLEDA_REDIRECT';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
export type Currency = 'USD' | 'KHR';

export interface PaymentRequest {
  amount: number;
  currency: Currency;
  description: string;
  companyId: string;
  module: string;
  paymentMethod: PaymentMethod;
  customerInfo: {
    name: string;
    email: string;
    phone?: string;
  };
  subscriptionId?: string;
  trialConversion?: boolean;
  // Enhanced tracking fields
  userId?: string;
  userAgent?: string;
  ipAddress?: string;
}

export interface PaymentResponse {
  paymentId: string;
  status: PaymentStatus;
  amount: number;
  currency: Currency;
  paymentMethod: PaymentMethod;
  expiresAt: Date;
  // KHQR specific fields
  qrCode?: string;
  // POS specific fields
  posTerminalId?: string;
  // Mobile specific fields
  redirectUrl?: string;
  // Bank transfer specific fields
  bankReference?: string;
  // ACLEDA Bank e-commerce specific fields
  sessionId?: string;
  paymentTokenId?: string;
  // Common fields
  instructions?: string;
}

export interface PaymentCallback {
  paymentId: string;
  status: PaymentStatus;
  transactionId: string;
  amount: number;
  currency: Currency;
  paidAt?: Date;
  bankReference?: string;
  metadata?: Record<string, any>;
}

export interface ACLEDAPaymentConfig {
  merchantId: string;
  apiKey: string;
  environment: 'sandbox' | 'production';
  webhookSecret: string;
  baseUrl: string;
}

export interface KHQRPayment {
  qrCode: string;
  qrData: string;
  amount: number;
  currency: Currency;
  expiresAt: Date;
  instructions: string;
}

export interface POSPayment {
  terminalId: string;
  amount: number;
  currency: Currency;
  reference: string;
  instructions: string;
}

export interface PaymentHistory {
  _id: string;
  companyId: string;
  paymentId: string;
  subscriptionId?: string;
  module: string;
  amount: number;
  originalAmount: number;
  currency: Currency;
  paymentMethod: PaymentMethod;
  status: PaymentStatus;
  description: string;
  transactionId?: string;
  bankReference?: string;
  paidAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
  // Additional tracking fields
  userId?: string;
  customerInfo?: {
    name: string;
    email: string;
    phone?: string;
  };
  subscriptionDetails?: {
    module: string;
    extensionDays?: number;
    wasTrialConversion?: boolean;
  };
  refunds?: PaymentRefund[];
}

export interface PaymentHistoryFilter {
  companyId?: string;
  userId?: string;
  module?: string;
  status?: PaymentStatus | PaymentStatus[];
  paymentMethod?: PaymentMethod | PaymentMethod[];
  currency?: Currency;
  dateFrom?: Date;
  dateTo?: Date;
  amountMin?: number;
  amountMax?: number;
  searchTerm?: string; // Search in description, paymentId, transactionId
}

export interface PaymentHistoryQuery {
  filter?: PaymentHistoryFilter;
  sort?: {
    field: 'createdAt' | 'amount' | 'status' | 'paidAt' | 'module';
    order: 'asc' | 'desc';
  };
  pagination?: {
    page: number;
    limit: number;
  };
}

export interface PaymentHistoryResponse {
  payments: PaymentHistory[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNext: boolean;
  hasPrev: boolean;
  summary: PaymentSummary;
}

export interface PaymentSummary {
  totalAmount: number;
  totalCount: number;
  completedAmount: number;
  completedCount: number;
  pendingAmount: number;
  pendingCount: number;
  failedCount: number;
  cancelledCount: number;
  averageAmount: number;
  currencyBreakdown: Record<Currency, {
    amount: number;
    count: number;
  }>;
  moduleBreakdown: Record<string, {
    amount: number;
    count: number;
  }>;
  methodBreakdown: Record<PaymentMethod, {
    amount: number;
    count: number;
  }>;
}

export interface PaymentAnalytics {
  dateRange: {
    from: Date;
    to: Date;
  };
  totalRevenue: number;
  totalTransactions: number;
  averageTransactionValue: number;
  successRate: number;
  topModules: Array<{
    module: string;
    revenue: number;
    transactions: number;
  }>;
  topPaymentMethods: Array<{
    method: PaymentMethod;
    revenue: number;
    transactions: number;
  }>;
  dailyRevenue: Array<{
    date: string;
    revenue: number;
    transactions: number;
  }>;
  monthlyRevenue: Array<{
    month: string;
    revenue: number;
    transactions: number;
  }>;
  revenueByModule: Record<string, number>;
  revenueByMethod: Record<PaymentMethod, number>;
  revenueByCurrency: Record<Currency, number>;
}

export interface PaymentRefund {
  refundId: string;
  amount: number;
  currency: Currency;
  reason: string;
  refundDate: Date;
  status: 'pending' | 'completed' | 'failed';
  metadata?: Record<string, any>;
}

export interface PaymentExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  filter?: PaymentHistoryFilter;
  columns?: string[];
  includeAnalytics?: boolean;
}

export interface TrialToPaymentConversion {
  subscriptionId: string;
  trialEndDate: Date;
  paymentRequired: boolean;
  gracePeriodDays: number;
  finalWarningDate: Date;
  suspensionDate: Date;
} 