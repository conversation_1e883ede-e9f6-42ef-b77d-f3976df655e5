import { createLogger } from '../utils/logger';

const logger = createLogger('HTTP_CLIENT');

export interface HttpClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export interface HttpResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export class HttpClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor(config: HttpClientConfig) {
    this.baseURL = config.baseURL;
    this.timeout = config.timeout || 5000;
    this.defaultHeaders = config.headers || {};
  }

  private async request<T>(
    method: string,
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<HttpResponse<T>> {
    const fullUrl = `${this.baseURL}${url}`;
    const requestHeaders = { ...this.defaultHeaders, ...headers };

    logger.debug(`Making ${method} request to ${fullUrl}`, { data, headers: requestHeaders });

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(fullUrl, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...requestHeaders,
        },
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const responseData = await response.json();

      const headersObj: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headersObj[key] = value;
      });

      const result: HttpResponse<T> = {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: headersObj,
      };

      if (!response.ok) {
        logger.error(`HTTP request failed: ${method} ${fullUrl}`, {
          status: response.status,
          statusText: response.statusText,
          data: responseData,
        });
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      logger.debug(`HTTP request successful: ${method} ${fullUrl}`, {
        status: response.status,
      });

      return result;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        logger.error(`HTTP request error: ${method} ${fullUrl}`, {
          error: error.message,
        });
      }
      
      throw error;
    }
  }

  async get<T>(url: string, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>('GET', url, undefined, headers);
  }

  async post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>('POST', url, data, headers);
  }

  async put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>('PUT', url, data, headers);
  }

  async patch<T>(url: string, data?: any, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>('PATCH', url, data, headers);
  }

  async delete<T>(url: string, headers?: Record<string, string>): Promise<HttpResponse<T>> {
    return this.request<T>('DELETE', url, undefined, headers);
  }
}

// Factory function to create HTTP clients for different services
export const createHttpClient = (config: HttpClientConfig): HttpClient => {
  return new HttpClient(config);
}; 