interface LogLevel {
  ERROR: 0;
  WARN: 1;
  INFO: 2;
  DEBUG: 3;
}

const LOG_LEVELS: LogLevel = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
} as const;

type LogLevelKey = keyof LogLevel;

export class Logger {
  private serviceName: string;
  private logLevel: number;

  constructor(serviceName: string, logLevel: LogLevelKey = 'INFO') {
    this.serviceName = serviceName;
    this.logLevel = LOG_LEVELS[logLevel];
  }

  private formatMessage(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString();
    const baseLog = `[${timestamp}] [${level}] [${this.serviceName}] ${message}`;
    
    if (meta) {
      return `${baseLog} ${JSON.stringify(meta)}`;
    }
    
    return baseLog;
  }

  private log(level: LogLevelKey, message: string, meta?: any): void {
    if (LOG_LEVELS[level] <= this.logLevel) {
      const formattedMessage = this.formatMessage(level, message, meta);
      
      switch (level) {
        case 'ERROR':
          console.error(formattedMessage);
          break;
        case 'WARN':
          console.warn(formattedMessage);
          break;
        case 'INFO':
          console.info(formattedMessage);
          break;
        case 'DEBUG':
          console.debug(formattedMessage);
          break;
      }
    }
  }

  error(message: string, meta?: any): void {
    this.log('ERROR', message, meta);
  }

  warn(message: string, meta?: any): void {
    this.log('WARN', message, meta);
  }

  info(message: string, meta?: any): void {
    this.log('INFO', message, meta);
  }

  debug(message: string, meta?: any): void {
    this.log('DEBUG', message, meta);
  }
}

// Export a factory function to create loggers for each service
export const createLogger = (serviceName: string, logLevel?: LogLevelKey): Logger => {
  return new Logger(serviceName, logLevel);
}; 