/**
 * Generates a hex string ID to replace MongoDB ObjectId
 * Returns a 24-character hex string similar to ObjectId format
 */
export const generateHexId = (): string => {
  // Generate 12 random bytes and convert to hex (24 chars)
  const array = new Uint8Array(12);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Validates if a string is a valid hex ID
 */
export const isValidHexId = (id: string): boolean => {
  return /^[a-fA-F0-9]{24}$/.test(id);
};

/**
 * Creates a timestamp-based hex ID with random suffix
 * First 8 chars represent timestamp, remaining 16 are random
 */
export const generateTimestampHexId = (): string => {
  const timestamp = Math.floor(Date.now() / 1000);
  const timestampHex = timestamp.toString(16).padStart(8, '0');
  const array = new Uint8Array(8);
  crypto.getRandomValues(array);
  const randomHex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  return timestampHex + randomHex;
};

/**
 * Extracts timestamp from timestamp-based hex ID
 */
export const extractTimestampFromHexId = (id: string): Date | null => {
  if (!isValidHexId(id)) {
    return null;
  }
  
  const timestampHex = id.substring(0, 8);
  const timestamp = parseInt(timestampHex, 16);
  return new Date(timestamp * 1000);
}; 