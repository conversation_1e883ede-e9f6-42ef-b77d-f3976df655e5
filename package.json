{"name": "elypos", "version": "1.0.0", "description": "ElyPOS - A microservices-based Point of Sale and Loan Management System", "private": true, "workspaces": ["gateway", "services/*", "shared"], "scripts": {"dev": "concurrently \"npm run dev:gateway\" \"npm run dev:core\" \"npm run dev:pos\" \"npm run dev:loan\"", "dev:gateway": "cd gateway && bun run dev", "dev:core": "cd services/core && bun run dev", "dev:pos": "cd services/pos && bun run dev", "dev:loan": "cd services/loan && bun run dev", "start": "concurrently \"npm run start:gateway\" \"npm run start:core\" \"npm run start:pos\" \"npm run start:loan\"", "start:gateway": "cd gateway && bun run start", "start:core": "cd services/core && bun run start", "start:pos": "cd services/pos && bun run start", "start:loan": "cd services/loan && bun run start", "build": "npm run build:gateway && npm run build:core && npm run build:pos && npm run build:loan", "build:gateway": "cd gateway && bun run build", "build:core": "cd services/core && bun run build", "build:pos": "cd services/pos && bun run build", "build:loan": "cd services/loan && bun run build", "clean": "npm run clean:gateway && npm run clean:core && npm run clean:pos && npm run clean:loan", "clean:gateway": "cd gateway && bun run clean", "clean:core": "cd services/core && bun run clean", "clean:pos": "cd services/pos && bun run clean", "clean:loan": "cd services/loan && bun run clean", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "install:all": "bun install && cd gateway && bun install && cd ../services/core && bun install && cd ../pos && bun install && cd ../loan && bun install"}, "devDependencies": {"concurrently": "^9.2.0"}, "keywords": ["pos", "point-of-sale", "loan-management", "microservices", "elysia", "bun", "mongodb", "typescript"], "author": "ElyPOS Team", "license": "MIT"}