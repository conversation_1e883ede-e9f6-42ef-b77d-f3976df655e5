# Core Service Environment Variables

# Database Configuration
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=elypos_core

# Server Configuration
NODE_ENV=development
PORT=3001
LOG_LEVEL=INFO

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# ACLEDA Bank Payment Integration
# UAT (User Acceptance Testing) Configuration
ACLEDA_ENVIRONMENT=UAT
ACLEDA_MERCHANT_ID=/wRUtOaUXhK1l9JkMygbMW44ms0=
ACLEDA_LOGIN_ID=cosmouser
ACLEDA_PASSWORD=cosmouser
ACLEDA_SIGNATURE=your_acleda_signature_from_email
ACLEDA_MERCHANT_NAME=COSMOSDIGITAL

# ACLEDA Bank UAT URLs
ACLEDA_BASE_URL=https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL
ACLEDA_PAYMENT_PAGE_URL=https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/paymentPage.jsp
ACLEDA_OPEN_SESSION_URL=https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/openSessionV2
ACLEDA_GET_STATUS_URL=https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/getTxnStatus

# Payment Callback URLs
ACLEDA_SUCCESS_URL=http://localhost:5173/payment/success
ACLEDA_ERROR_URL=http://localhost:5173/payment/failed
ACLEDA_CALLBACK_URL=http://localhost:3001/payments/callback

# CORS Configuration
CORS_ORIGIN=http://localhost:5173,http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100 