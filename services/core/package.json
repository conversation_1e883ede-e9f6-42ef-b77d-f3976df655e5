{"name": "elypos-core", "version": "1.0.0", "description": "Core service for authentication, companies, and subscriptions", "main": "src/index.ts", "scripts": {"dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts", "build": "bun build src/index.ts --outdir=dist", "clean": "rm -rf dist"}, "dependencies": {"elysia": "^1.0.0", "@elysiajs/cors": "^1.0.0", "@elysiajs/swagger": "^1.0.0", "@elysiajs/jwt": "^1.0.0", "mongodb": "^6.3.0", "bcryptjs": "^2.4.3"}, "devDependencies": {"@types/bun": "latest", "@types/bcryptjs": "^2.4.0", "typescript": "^5.0.0"}, "module": "src/index.ts"}