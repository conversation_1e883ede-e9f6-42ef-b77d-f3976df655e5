# Core Service Production Environment Variables

# Database Configuration
MONGODB_URI=mongodb://your-production-mongodb-cluster
MONGODB_DB_NAME=elypos_core_production

# Server Configuration
NODE_ENV=production
PORT=3001
LOG_LEVEL=WARN

# JWT Configuration (Use strong secrets in production)
JWT_SECRET=your_super_secure_jwt_secret_key_production
JWT_REFRESH_SECRET=your_super_secure_jwt_refresh_secret_production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# ACLEDA Bank Payment Integration - PRODUCTION
ACLEDA_ENVIRONMENT=PRODUCTION
ACLEDA_MERCHANT_ID=your_production_merchant_id
ACLEDA_LOGIN_ID=your_production_login_id
ACLEDA_PASSWORD=your_production_password
ACLEDA_SIGNATURE=your_production_signature_from_acleda
ACLEDA_MERCHANT_NAME=YOUR_MERCHANT_ALIAS

# ACLEDA Bank Production URLs (Replace with actual production URLs)
ACLEDA_BASE_URL=https://epayment.acledabank.com.kh/YOUR_MERCHANT_ALIAS
ACLEDA_PAYMENT_PAGE_URL=https://epayment.acledabank.com.kh/YOUR_MERCHANT_ALIAS/paymentPage.jsp
ACLEDA_OPEN_SESSION_URL=https://epayment.acledabank.com.kh/YOUR_MERCHANT_ALIAS/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/openSessionV2
ACLEDA_GET_STATUS_URL=https://epayment.acledabank.com.kh/YOUR_MERCHANT_ALIAS/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/getTxnStatus

# Production Payment Callback URLs
ACLEDA_SUCCESS_URL=https://yourdomain.com/payment/success
ACLEDA_ERROR_URL=https://yourdomain.com/payment/failed
ACLEDA_CALLBACK_URL=https://yourdomain.com/api/payments/callback

# CORS Configuration (Add your production domains)
CORS_ORIGIN=https://yourdomain.com,https://app.yourdomain.com

# Rate Limiting (More restrictive for production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50 