import { getDatabase } from '../../db/client';
import { createLogger } from '../../../../../shared/utils/logger';
import { Coupon, CouponUsage, CouponValidationResult, CouponType, CouponApplicability, AppliedCoupon } from '../../../../../shared/types/company';

const logger = createLogger('COUPON_SERVICE');

export class CouponService {
  private get db() {
    return getDatabase();
  }

  private get couponsCollection() {
    return this.db.getCollection('coupons');
  }

  private get couponUsageCollection() {
    return this.db.getCollection('coupon_usage');
  }

  // Admin functions for managing coupons
  async createCoupon(data: {
    code: string;
    name: string;
    description: string;
    type: CouponType;
    value: number;
    applicability: CouponApplicability;
    applicableModules?: string[];
    maxUses: number;
    maxUsesPerCompany: number;
    startDate: Date;
    endDate: Date;
    minimumPurchaseAmount?: number;
    createdBy: string;
  }): Promise<Coupon> {
    logger.info('Creating new coupon', {
      code: data.code,
      type: data.type,
      value: data.value,
    });

    // Check if coupon code already exists
    const existingCoupon = await this.couponsCollection.findOne({ code: data.code });
    if (existingCoupon) {
      throw new Error(`Coupon code "${data.code}" already exists`);
    }

    // Validate coupon data
    this.validateCouponData(data);

    const coupon = this.db.createDocument({
      code: data.code.toUpperCase(),
      name: data.name,
      description: data.description,
      type: data.type,
      value: data.value,
      applicability: data.applicability,
      applicableModules: data.applicableModules || [],
      maxUses: data.maxUses,
      currentUses: 0,
      maxUsesPerCompany: data.maxUsesPerCompany,
      startDate: data.startDate,
      endDate: data.endDate,
      minimumPurchaseAmount: data.minimumPurchaseAmount,
      isActive: true,
      createdBy: data.createdBy,
    });

    await this.couponsCollection.insertOne(coupon as any);

    logger.info('Coupon created successfully', {
      couponId: coupon._id,
      code: coupon.code,
    });

    return coupon as unknown as Coupon;
  }

  async updateCoupon(couponId: string, updates: Partial<Coupon>): Promise<Coupon | null> {
    logger.info('Updating coupon', { couponId, updates });

    // Don't allow updating certain fields
    const { _id, currentUses, createdAt, createdBy, ...allowedUpdates } = updates;

    const success = await this.db.updateById(this.couponsCollection, couponId, {
      ...allowedUpdates,
      updatedAt: new Date(),
    });

    if (!success) {
      return null;
    }

    const updatedCoupon = await this.couponsCollection.findOne({ _id: couponId } as any);
    return updatedCoupon as unknown as Coupon;
  }

  async deactivateCoupon(couponId: string): Promise<boolean> {
    logger.info('Deactivating coupon', { couponId });

    const success = await this.db.updateById(this.couponsCollection, couponId, {
      isActive: false,
      updatedAt: new Date(),
    });

    return success;
  }

  async getAllCoupons(includeInactive: boolean = false): Promise<Coupon[]> {
    const filter = includeInactive ? {} : { isActive: true };
    const coupons = await this.couponsCollection.find(filter).toArray();
    return coupons as unknown as Coupon[];
  }

  async getCouponByCode(code: string): Promise<Coupon | null> {
    const coupon = await this.couponsCollection.findOne({ 
      code: code.toUpperCase(),
      isActive: true,
    });
    return coupon as unknown as Coupon;
  }

  // Coupon validation and application
  async validateCoupon(
    code: string, 
    companyId: string, 
    module: string, 
    amount: number
  ): Promise<CouponValidationResult> {
    logger.info('Validating coupon', { code, companyId, module, amount });

    const coupon = await this.getCouponByCode(code);
    if (!coupon) {
      return { isValid: false, error: 'Coupon code not found' };
    }

    // Check if coupon is active
    if (!coupon.isActive) {
      return { isValid: false, error: 'Coupon is no longer active' };
    }

    // Check validity period
    const now = new Date();
    if (now < coupon.startDate) {
      return { isValid: false, error: 'Coupon is not yet valid' };
    }
    if (now > coupon.endDate) {
      return { isValid: false, error: 'Coupon has expired' };
    }

    // Check usage limits
    if (coupon.maxUses > 0 && coupon.currentUses >= coupon.maxUses) {
      return { isValid: false, error: 'Coupon usage limit reached' };
    }

    // Check per-company usage limit
    if (coupon.maxUsesPerCompany > 0) {
      const companyUsageCount = await this.couponUsageCollection.countDocuments({
        couponId: coupon._id,
        companyId,
        isActive: true,
      });

      if (companyUsageCount >= coupon.maxUsesPerCompany) {
        return { isValid: false, error: 'Coupon usage limit reached for this company' };
      }
    }

    // Check module applicability
    if (!this.isModuleApplicable(coupon, module)) {
      return { isValid: false, error: `Coupon is not applicable to ${module} module` };
    }

    // Check minimum purchase amount
    if (coupon.minimumPurchaseAmount && amount < coupon.minimumPurchaseAmount) {
      return { 
        isValid: false, 
        error: `Minimum purchase amount of $${coupon.minimumPurchaseAmount} required` 
      };
    }

    // Calculate discount
    const discountAmount = this.calculateDiscount(coupon, amount);
    const finalAmount = Math.max(0, amount - discountAmount);

    return {
      isValid: true,
      coupon,
      discountAmount,
      finalAmount,
    };
  }

  async applyCouponToSubscription(
    subscriptionId: string,
    couponCode: string,
    companyId: string,
    module: string,
    originalAmount: number
  ): Promise<AppliedCoupon> {
    logger.info('Applying coupon to subscription', {
      subscriptionId,
      couponCode,
      companyId,
      module,
    });

    const validation = await this.validateCoupon(couponCode, companyId, module, originalAmount);
    if (!validation.isValid || !validation.coupon) {
      throw new Error(validation.error || 'Invalid coupon');
    }

    const coupon = validation.coupon;
    const discountAmount = validation.discountAmount!;

    // Create usage record
    const usage = this.db.createDocument({
      couponId: coupon._id,
      couponCode: coupon.code,
      companyId,
      subscriptionId,
      discountAmount,
      originalAmount,
      finalAmount: validation.finalAmount!,
      appliedToModule: module,
      appliedAt: new Date(),
      isActive: true,
    });

    await this.couponUsageCollection.insertOne(usage as any);

    // Update coupon usage count
    await this.couponsCollection.updateOne(
      { _id: coupon._id } as any,
      { 
        $inc: { currentUses: 1 },
        $set: { updatedAt: new Date() },
      }
    );

    logger.info('Coupon applied successfully', {
      couponId: coupon._id,
      subscriptionId,
      discountAmount,
    });

    return {
      couponId: coupon._id,
      couponCode: coupon.code,
      discountAmount,
      appliedAt: new Date(),
    };
  }

  async removeCouponFromSubscription(
    subscriptionId: string,
    couponCode: string
  ): Promise<boolean> {
    logger.info('Removing coupon from subscription', { subscriptionId, couponCode });

    // Deactivate usage record
    const result = await this.couponUsageCollection.updateOne(
      { subscriptionId, couponCode, isActive: true },
      { 
        $set: { 
          isActive: false,
          updatedAt: new Date(),
        } 
      }
    );

    if (result.modifiedCount > 0) {
      // Decrease coupon usage count
      await this.couponsCollection.updateOne(
        { code: couponCode } as any,
        { 
          $inc: { currentUses: -1 },
          $set: { updatedAt: new Date() },
        }
      );

      logger.info('Coupon removed successfully', { subscriptionId, couponCode });
      return true;
    }

    return false;
  }

  async getCouponUsage(couponId: string): Promise<CouponUsage[]> {
    const usage = await this.couponUsageCollection
      .find({ couponId, isActive: true })
      .sort({ appliedAt: -1 })
      .toArray();

    return usage as unknown as CouponUsage[];
  }

  async getCompanyCouponUsage(companyId: string): Promise<CouponUsage[]> {
    const usage = await this.couponUsageCollection
      .find({ companyId, isActive: true })
      .sort({ appliedAt: -1 })
      .toArray();

    return usage as unknown as CouponUsage[];
  }

  // Helper methods
  private validateCouponData(data: any): void {
    if (!data.code || data.code.length < 2) {
      throw new Error('Coupon code must be at least 2 characters long');
    }

    if (data.value <= 0) {
      throw new Error('Coupon value must be greater than 0');
    }

    if (data.type === 'PERCENTAGE_DISCOUNT' && data.value > 100) {
      throw new Error('Percentage discount cannot exceed 100%');
    }

    if (data.startDate >= data.endDate) {
      throw new Error('Start date must be before end date');
    }

    if (data.maxUses < 0 || data.maxUsesPerCompany < 0) {
      throw new Error('Usage limits cannot be negative');
    }

    if (data.applicability === 'SPECIFIC_MODULE' && (!data.applicableModules || data.applicableModules.length === 0)) {
      throw new Error('Applicable modules must be specified for SPECIFIC_MODULE type');
    }
  }

  private isModuleApplicable(coupon: Coupon, module: string): boolean {
    switch (coupon.applicability) {
      case 'ALL_MODULES':
        return true;
      case 'SPECIFIC_MODULE':
        return coupon.applicableModules?.includes(module) || false;
      case 'POS_ONLY':
        return module === 'POS';
      case 'NON_POS_MODULES':
        return module !== 'POS';
      default:
        return false;
    }
  }

  private calculateDiscount(coupon: Coupon, amount: number): number {
    switch (coupon.type) {
      case 'PERCENTAGE_DISCOUNT':
        return Math.round((amount * coupon.value / 100) * 100) / 100; // Round to 2 decimal places
      case 'FIXED_AMOUNT_DISCOUNT':
        return Math.min(coupon.value, amount); // Don't exceed the original amount
      case 'FIRST_MONTH_FREE':
        return amount; // Full discount for first month
      default:
        return 0;
    }
  }

  // Analytics and reporting
  async getCouponStats(couponId: string): Promise<{
    totalUses: number;
    totalDiscount: number;
    uniqueCompanies: number;
    moduleBreakdown: Record<string, number>;
  }> {
    const usage = await this.getCouponUsage(couponId);

    const totalUses = usage.length;
    const totalDiscount = usage.reduce((sum, u) => sum + u.discountAmount, 0);
    const uniqueCompanies = new Set(usage.map(u => u.companyId)).size;
    
    const moduleBreakdown: Record<string, number> = {};
    usage.forEach(u => {
      moduleBreakdown[u.appliedToModule] = (moduleBreakdown[u.appliedToModule] || 0) + 1;
    });

    return {
      totalUses,
      totalDiscount,
      uniqueCompanies,
      moduleBreakdown,
    };
  }

  async generateCouponCode(prefix: string = ''): Promise<string> {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = prefix.toUpperCase();
    
    // Generate random part
    for (let i = 0; i < (8 - prefix.length); i++) {
      code += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    // Check if code already exists
    const existing = await this.getCouponByCode(code);
    if (existing) {
      return this.generateCouponCode(prefix); // Recursive retry
    }

    return code;
  }
} 