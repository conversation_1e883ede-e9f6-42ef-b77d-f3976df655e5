import { getDatabase } from '../../db/client';
import { createLogger } from '../../../../../shared/utils/logger';
import { Company, Investor, OwnershipTransfer } from '../../../../../shared/types/company';
import { UserCompany } from '../../../../../shared/types/auth';
import { generateHexId } from '../../../../../shared/utils/id-generator';

const logger = createLogger('COMPANY_SERVICE');

export class CompanyService {
  private get db() {
    return getDatabase();
  }

  private get companiesCollection() {
    return this.db.getCollection('companies');
  }

  private get userCompaniesCollection() {
    return this.db.getCollection('user_companies');
  }

  private get investorsCollection() {
    return this.db.getCollection('investors');
  }

  private get ownershipTransfersCollection() {
    return this.db.getCollection('ownership_transfers');
  }

  async createCompany(data: {
    name: string;
    email: string;
    ownerId: string;
  }): Promise<Company> {
    logger.info('Creating new company', { name: data.name, ownerId: data.ownerId });

    // Create company document
    const company = this.db.createDocument({
      name: data.name,
      email: data.email,
      ownerId: data.ownerId,
      foundedDate: new Date(),
      businessType: 'private_limited',
      isActive: true,
      address: {
        country: 'Cambodia',
        province: 'Phnom Penh',
        district: '',
        commune: '',
        street: '',
      },
      settings: {
        timezone: 'Asia/Phnom_Penh',
        currency: 'USD',
        language: 'both',
        taxRate: 10, // 10% VAT in Cambodia
        witholdingTaxRate: 14, // 14% withholding tax
        dateFormat: 'DD/MM/YYYY',
        numberFormat: 'US',
      },
    });

    // Insert company
    await this.companiesCollection.insertOne(company);

    // Create user-company relationship with company_admin role
    const userCompany = this.db.createDocument({
      userId: data.ownerId,
      companyId: company._id,
      role: 'company_admin',
      isActive: true,
      joinedAt: new Date(),
    });

    await this.userCompaniesCollection.insertOne(userCompany);

    logger.info('Company created successfully', { 
      companyId: company._id, 
      name: data.name,
      ownerId: data.ownerId 
    });

    return company as unknown as Company;
  }

  async getCompanyById(id: string): Promise<Company | null> {
    const company = await this.companiesCollection.findOne({ _id: id } as any);
    if (!company) {
      return null;
    }
    return company as unknown as Company;
  }

  async getUserCompanies(userId: string): Promise<Array<Company & { role: string; joinedAt: Date }>> {
    // Get all user-company relationships for this user
    const userCompanies = await this.userCompaniesCollection
      .find({ userId, isActive: true })
      .toArray();

    if (userCompanies.length === 0) {
      return [];
    }

    // Get company details
    const companyIds = userCompanies.map(uc => uc.companyId);
    const companies = await this.companiesCollection
      .find({ _id: { $in: companyIds } } as any)
      .toArray();

    // Combine company data with user role
    return companies.map(company => {
      const userCompany = userCompanies.find(uc => uc.companyId === company._id);
      return {
        ...company,
        role: userCompany?.role || 'employee',
        joinedAt: userCompany?.joinedAt || company.createdAt,
      };
    }) as unknown as Array<Company & { role: string; joinedAt: Date }>;
  }

  async getUserCompanyRole(userId: string, companyId: string): Promise<string | null> {
    const userCompany = await this.userCompaniesCollection.findOne({
      userId,
      companyId,
      isActive: true,
    });

    return userCompany?.role || null;
  }

  async addUserToCompany(data: {
    userId: string;
    companyId: string;
    role: 'company_admin' | 'manager' | 'employee';
    addedBy: string;
  }): Promise<UserCompany> {
    logger.info('Adding user to company', {
      userId: data.userId,
      companyId: data.companyId,
      role: data.role,
    });

    // Check if user is already in company
    const existingRelation = await this.userCompaniesCollection.findOne({
      userId: data.userId,
      companyId: data.companyId,
    });

    if (existingRelation) {
      if (existingRelation.isActive) {
        throw new Error('User is already a member of this company');
      } else {
        // Reactivate existing relationship
        await this.db.updateById(this.userCompaniesCollection, existingRelation._id as any, {
          role: data.role,
          isActive: true,
          joinedAt: new Date(),
        });
        
        const updated = await this.userCompaniesCollection.findOne({ _id: existingRelation._id } as any);
        return updated as unknown as UserCompany;
      }
    }

    // Create new user-company relationship
    const userCompany = this.db.createDocument({
      userId: data.userId,
      companyId: data.companyId,
      role: data.role,
      isActive: true,
      joinedAt: new Date(),
    });

    await this.userCompaniesCollection.insertOne(userCompany);

    logger.info('User added to company successfully', {
      userId: data.userId,
      companyId: data.companyId,
      role: data.role,
    });

    return userCompany as unknown as UserCompany;
  }

  async removeUserFromCompany(userId: string, companyId: string): Promise<boolean> {
    logger.info('Removing user from company', { userId, companyId });

    const result = await this.userCompaniesCollection.updateOne(
      { userId, companyId },
      { $set: { isActive: false, updatedAt: new Date() } }
    );

    const success = result.modifiedCount > 0;
    
    if (success) {
      logger.info('User removed from company successfully', { userId, companyId });
    }

    return success;
  }

  async updateCompany(id: string, data: Partial<Company>): Promise<Company | null> {
    const success = await this.db.updateById(this.companiesCollection, id, data);
    if (!success) {
      return null;
    }

    return this.getCompanyById(id);
  }

  async deactivateCompany(id: string): Promise<boolean> {
    logger.info('Deactivating company', { companyId: id });

    // Deactivate company
    const companyResult = await this.db.updateById(this.companiesCollection, id, { 
      isActive: false 
    });

    // Deactivate all user-company relationships
    await this.userCompaniesCollection.updateMany(
      { companyId: id },
      { $set: { isActive: false, updatedAt: new Date() } }
    );

    return companyResult;
  }

  // Ownership Transfer Methods
  async initiateOwnershipTransfer(data: {
    companyId: string;
    fromOwnerId: string;
    toOwnerId: string;
    transferType: string;
    transferAmount?: number;
    currency?: string;
    reason?: string;
  }): Promise<OwnershipTransfer> {
    logger.info('Initiating ownership transfer', {
      companyId: data.companyId,
      fromOwnerId: data.fromOwnerId,
      toOwnerId: data.toOwnerId,
    });

    // Verify the current owner
    const company = await this.getCompanyById(data.companyId);
    if (!company || company.ownerId !== data.fromOwnerId) {
      throw new Error('Only the current owner can initiate ownership transfer');
    }

    // Check if target user exists in the company
    const targetUserCompany = await this.userCompaniesCollection.findOne({
      userId: data.toOwnerId,
      companyId: data.companyId,
      isActive: true,
    });

    if (!targetUserCompany) {
      throw new Error('Target user must be a member of the company');
    }

    const ownershipTransfer = this.db.createDocument({
      companyId: data.companyId,
      fromOwnerId: data.fromOwnerId,
      toOwnerId: data.toOwnerId,
      transferDate: new Date(),
      transferType: data.transferType,
      transferAmount: data.transferAmount,
      currency: data.currency,
      status: 'pending',
      reason: data.reason,
    });

    await this.ownershipTransfersCollection.insertOne(ownershipTransfer);

    logger.info('Ownership transfer initiated', {
      transferId: ownershipTransfer._id,
      companyId: data.companyId,
    });

    return ownershipTransfer as unknown as OwnershipTransfer;
  }

  async approveOwnershipTransfer(transferId: string, approvedBy: string): Promise<OwnershipTransfer | null> {
    logger.info('Approving ownership transfer', { transferId, approvedBy });

    const transfer = await this.ownershipTransfersCollection.findOne({ _id: transferId } as any);
    if (!transfer || transfer.status !== 'pending') {
      throw new Error('Transfer not found or not in pending status');
    }

    // Update transfer status
    await this.db.updateById(this.ownershipTransfersCollection, transferId, {
      status: 'approved',
      approvedBy,
      approvedAt: new Date(),
    });

    // Transfer company ownership
    await this.db.updateById(this.companiesCollection, transfer.companyId, {
      ownerId: transfer.toOwnerId,
    });

    // Update user roles - make new owner company_admin, demote old owner to manager
    await this.userCompaniesCollection.updateOne(
      { userId: transfer.toOwnerId, companyId: transfer.companyId },
      { $set: { role: 'company_admin', updatedAt: new Date() } }
    );

    await this.userCompaniesCollection.updateOne(
      { userId: transfer.fromOwnerId, companyId: transfer.companyId },
      { $set: { role: 'manager', updatedAt: new Date() } }
    );

    // Mark transfer as completed
    await this.db.updateById(this.ownershipTransfersCollection, transferId, {
      status: 'completed',
      completedAt: new Date(),
    });

    logger.info('Ownership transfer completed', {
      transferId,
      companyId: transfer.companyId,
      newOwnerId: transfer.toOwnerId,
    });

    return this.ownershipTransfersCollection.findOne({ _id: transferId } as any) as unknown as OwnershipTransfer;
  }

  // Investor Management Methods
  async addInvestor(data: {
    companyId: string;
    userId: string;
    investmentAmount: number;
    currency: string;
    equityPercentage: number;
    investmentType: string;
    dividendRights?: boolean;
    votingRights?: boolean;
    vestingSchedule?: any;
    notes?: string;
  }): Promise<Investor> {
    logger.info('Adding investor to company', {
      companyId: data.companyId,
      userId: data.userId,
      investmentAmount: data.investmentAmount,
      equityPercentage: data.equityPercentage,
    });

    // Check if user is already an investor
    const existingInvestor = await this.investorsCollection.findOne({
      companyId: data.companyId,
      userId: data.userId,
      status: 'active',
    });

    if (existingInvestor) {
      throw new Error('User is already an active investor in this company');
    }

    // Add user to company if not already a member
    const userCompany = await this.userCompaniesCollection.findOne({
      userId: data.userId,
      companyId: data.companyId,
    });

    if (!userCompany) {
      await this.addUserToCompany({
        userId: data.userId,
        companyId: data.companyId,
        role: 'employee',
        addedBy: 'system',
      });
    }

    const investor = this.db.createDocument({
      companyId: data.companyId,
      userId: data.userId,
      investmentAmount: data.investmentAmount,
      currency: data.currency,
      equityPercentage: data.equityPercentage,
      investmentDate: new Date(),
      investmentType: data.investmentType,
      status: 'active',
      dividendRights: data.dividendRights !== false,
      votingRights: data.votingRights !== false,
      vestingSchedule: data.vestingSchedule,
      notes: data.notes,
    });

    await this.investorsCollection.insertOne(investor);

    logger.info('Investor added successfully', {
      investorId: investor._id,
      companyId: data.companyId,
      userId: data.userId,
    });

    return investor as unknown as Investor;
  }

  async getCompanyInvestors(companyId: string): Promise<Investor[]> {
    const investors = await this.investorsCollection
      .find({ companyId, status: 'active' })
      .toArray();

    return investors as unknown as Investor[];
  }

  async updateInvestor(investorId: string, data: Partial<Investor>): Promise<Investor | null> {
    const success = await this.db.updateById(this.investorsCollection, investorId, data);
    if (!success) {
      return null;
    }

    const investor = await this.investorsCollection.findOne({ _id: investorId } as any);
    return investor as unknown as Investor;
  }



  async getOwnershipTransfers(companyId: string): Promise<OwnershipTransfer[]> {
    const transfers = await this.ownershipTransfersCollection
      .find({ companyId })
      .sort({ createdAt: -1 })
      .toArray();

    return transfers as unknown as OwnershipTransfer[];
  }
} 