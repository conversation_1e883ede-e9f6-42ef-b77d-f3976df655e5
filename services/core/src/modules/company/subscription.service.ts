import { getDatabase } from '../../db/client';
import { createLogger } from '../../../../../shared/utils/logger';
import { CompanySubscription, BillingInfo, PosBusinessType, ModulePricing, PosPricing, CompanyTrialStatus, TrialInfo, AppliedCoupon } from '../../../../../shared/types/company';
import { CouponService } from './coupon.service';

const logger = createLogger('SUBSCRIPTION_SERVICE');

// Trial Configuration
export const TRIAL_CONFIG = {
  MAX_TRIALS: 1,
  TRIAL_DURATION_DAYS: 3,
};

// Pricing Configuration
export const MODULE_PRICING: ModulePricing = {
  ERP: 50,
  LOAN: 25,
  ACCOUNTING: 15,
};

export const POS_PRICING: PosPricing = {
  RESTAURANT: 35,      // Tables, menus, kitchen display, QR code e-menu, BBQ unlimited order mode, online ordering
  BAR: 30,             // Floor plan, tips, QR code e-menu for table ordering, drink recipes, online drink ordering
  RETAIL_SHOP: 25,     // Barcode scanning, inventory focus, general merchandise, e-commerce website
  CLOTHING_STORE: 30,  // Size/color variants, seasonal inventory, fashion trends, online catalog with virtual try-on
  FURNITURE_STORE: 40, // Custom orders, delivery tracking, room configurator, online showroom with 3D visualization
  BAKERY: 25,          // Fresh daily items, expiry tracking, traditional Khmer sweets, online pre-orders with pickup scheduling
  PHARMACY: 45,        // Medicine tracking, prescription management, expiry alerts, online prescription ordering and delivery
  ELECTRONICS_STORE: 35, // Warranty tracking, technical specifications, online electronics store with product comparisons
  GROCERY_STORE: 30,   // Fresh produce, bulk items, local suppliers, online grocery shopping with scheduled delivery
  BEAUTY_SALON: 20,    // Service appointments, treatment packages, staff scheduling, online booking and service catalog
  SERVICE: 15,         // Service booking, scheduling, and management, online service requests and appointment booking
  HOTEL: 60,           // Room booking, check-in/out, guest management, housekeeping, online room booking and guest services
  GENERIC: 20,         // Customizable for any business, flexible online ordering system for any business type
};

export class SubscriptionService {
  private couponService: CouponService;

  constructor() {
    this.couponService = new CouponService();
  }

  private get db() {
    return getDatabase();
  }

  private get subscriptionsCollection() {
    return this.db.getCollection('company_subscriptions');
  }

  private get trialStatusCollection() {
    return this.db.getCollection('company_trial_status');
  }

  private getNextMonthDate(): Date {
    const nextMonth = new Date();
    nextMonth.setMonth(nextMonth.getMonth() + 1);
    return nextMonth;
  }

  private getTrialEndDate(trialDuration: number = TRIAL_CONFIG.TRIAL_DURATION_DAYS): Date {
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + trialDuration);
    return endDate;
  }

  private getDaysRemaining(endDate: Date): number {
    const now = new Date();
    const diffTime = endDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  // Trial Management Methods
  async getOrCreateTrialStatus(companyId: string): Promise<CompanyTrialStatus> {
    let trialStatus = await this.trialStatusCollection.findOne({ companyId });

    if (!trialStatus) {
      const newTrialStatus = this.db.createDocument({
        companyId,
        trialsUsed: 0,
        maxTrials: TRIAL_CONFIG.MAX_TRIALS,
        availableTrials: TRIAL_CONFIG.MAX_TRIALS,
        trialHistory: [],
      });

      await this.trialStatusCollection.insertOne(newTrialStatus as any);
      trialStatus = newTrialStatus as any;
      logger.info('Created trial status for company', { companyId });
    }

    return trialStatus as any as CompanyTrialStatus;
  }

  async canStartTrial(companyId: string, module: string): Promise<boolean> {
    // Check if already subscribed to this module or has an active trial for this module
    const existingSubscription = await this.subscriptionsCollection.findOne({
      companyId,
      module,
      active: true,
    });

    // Allow one trial per module (not global limit per company)
    return !existingSubscription;
  }

  async startTrial(data: {
    companyId: string;
    module: 'POS' | 'LOAN' | 'ACCOUNTING' | 'ERP';
    posType?: PosBusinessType;
    trialDuration?: number;
  }): Promise<CompanySubscription> {
    logger.info('Starting trial for company', {
      companyId: data.companyId,
      module: data.module,
      posType: data.posType,
    });

    const canStart = await this.canStartTrial(data.companyId, data.module);
    if (!canStart) {
      throw new Error('Cannot start trial: already subscribed to this module or trial already used for this module');
    }

    // Calculate price (for reference, but not charged during trial)
    let price = 0;
    if (data.module === 'POS') {
      if (!data.posType) {
        throw new Error('POS type is required for POS module trial');
      }
      price = POS_PRICING[data.posType];
    } else {
      price = MODULE_PRICING[data.module];
    }

    const trialDuration = data.trialDuration || TRIAL_CONFIG.TRIAL_DURATION_DAYS;
    const trialStartDate = new Date();
    const trialEndDate = this.getTrialEndDate(trialDuration);

    // Create trial subscription
    const subscription = this.db.createDocument({
      companyId: data.companyId,
      module: data.module,
      active: true,
      price,
      originalPrice: price, // Same as price for trials
      currency: 'USD',
      nextBillingDate: trialEndDate, // Billing starts after trial
      posType: data.posType,
      subscribedAt: new Date(),
      isTrialMode: true,
      trialStartDate,
      trialEndDate,
      trialDuration,
      appliedCoupons: [], // No coupons for trials
    });

    await this.subscriptionsCollection.insertOne(subscription as any);

    // Update trial status - track trial history per module
    const trialStatus = await this.getOrCreateTrialStatus(data.companyId);
    await this.trialStatusCollection.updateOne(
      { _id: trialStatus._id } as any,
      {
        $set: { 
          updatedAt: new Date(),
        },
        $push: {
          trialHistory: {
            module: data.module,
            posType: data.posType,
            startDate: trialStartDate,
            endDate: trialEndDate,
            status: 'active',
          },
        },
      } as any
    );

    logger.info('Trial started successfully', {
      subscriptionId: subscription._id,
      companyId: data.companyId,
      module: data.module,
      trialEndDate,
    });

    return subscription as unknown as CompanySubscription;
  }

  async getTrialInfo(companyId: string, module: string): Promise<TrialInfo> {
    const subscription = await this.subscriptionsCollection.findOne({
      companyId,
      module,
      active: true,
    });

    const isInTrial = !!(subscription?.isTrialMode && subscription?.trialEndDate);
    const daysRemaining = isInTrial && subscription?.trialEndDate 
      ? this.getDaysRemaining(subscription.trialEndDate) 
      : undefined;

    return {
      companyId,
      module,
      posType: subscription?.posType,
      isInTrial,
      trialStartDate: subscription?.trialStartDate,
      trialEndDate: subscription?.trialEndDate,
      daysRemaining,
      canStartTrial: await this.canStartTrial(companyId, module),
      trialsUsed: 0, // Not applicable anymore since each module can have its own trial
      trialsRemaining: isInTrial ? 0 : 1, // Either has trial or can start one
    };
  }

  async convertTrialToPaid(companyId: string, module: string): Promise<CompanySubscription> {
    logger.info('Converting trial to paid subscription', { companyId, module });

    const subscription = await this.subscriptionsCollection.findOne({
      companyId,
      module,
      active: true,
      isTrialMode: true,
    });

    if (!subscription) {
      throw new Error('No active trial found for this module');
    }

    // Update subscription to paid mode
    const nextBillingDate = this.getNextMonthDate();
    await this.subscriptionsCollection.updateOne(
      { _id: subscription._id },
      {
        $set: {
          isTrialMode: false,
          nextBillingDate,
          updatedAt: new Date(),
        },
        $unset: {
          trialStartDate: '',
          trialEndDate: '',
        },
      }
    );

    // Update trial history
    const trialStatus = await this.getOrCreateTrialStatus(companyId);
    await this.trialStatusCollection.updateOne(
      { companyId },
      {
        $set: {
          'trialHistory.$[elem].status': 'converted',
          updatedAt: new Date(),
        },
      },
      {
        arrayFilters: [{ 'elem.module': module, 'elem.status': 'active' }],
      }
    );

    logger.info('Trial converted to paid subscription', {
      subscriptionId: subscription._id,
      companyId,
      module,
      nextBillingDate,
    });

    const updatedSubscription = await this.subscriptionsCollection.findOne({ _id: subscription._id } as any);
    return updatedSubscription as unknown as CompanySubscription;
  }

  async checkAndExpireTrials(): Promise<number> {
    logger.info('Checking for expired trials');

    const now = new Date();
    const expiredTrials = await this.subscriptionsCollection
      .find({
        isTrialMode: true,
        active: true,
        trialEndDate: { $lt: now },
      })
      .toArray();

    for (const trial of expiredTrials) {
      // Deactivate expired trial
      await this.subscriptionsCollection.updateOne(
        { _id: trial._id },
        {
          $set: {
            active: false,
            updatedAt: new Date(),
          },
        }
      );

      // Update trial history
      await this.trialStatusCollection.updateOne(
        { companyId: trial.companyId },
        {
          $set: {
            'trialHistory.$[elem].status': 'expired',
            updatedAt: new Date(),
          },
        },
        {
          arrayFilters: [{ 'elem.module': trial.module, 'elem.status': 'active' }],
        }
      );

      logger.info('Trial expired and deactivated', {
        subscriptionId: trial._id,
        companyId: trial.companyId,
        module: trial.module,
      });
    }

    return expiredTrials.length;
  }

  async getCompanyTrialStatus(companyId: string): Promise<CompanyTrialStatus> {
    return await this.getOrCreateTrialStatus(companyId);
  }

  // Updated existing methods to handle trials and coupons

  async subscribeToModule(data: {
    companyId: string;
    module: 'POS' | 'LOAN' | 'ACCOUNTING' | 'ERP';
    currency?: 'KHR' | 'USD';
    posType?: PosBusinessType;
    startTrial?: boolean;
    couponCode?: string;
  }): Promise<CompanySubscription> {
    logger.info('Subscribing company to module', {
      companyId: data.companyId,
      module: data.module,
      posType: data.posType,
      startTrial: data.startTrial,
      couponCode: data.couponCode,
    });

    // If starting trial, use trial method
    if (data.startTrial) {
      return this.startTrial({
        companyId: data.companyId,
        module: data.module,
        posType: data.posType,
      });
    }

    // Check if already subscribed
    const existingSubscription = await this.subscriptionsCollection.findOne({
      companyId: data.companyId,
      module: data.module,
      active: true,
    });

    if (existingSubscription) {
      throw new Error(`Company is already subscribed to ${data.module} module`);
    }

    // Calculate original price
    let originalPrice = 0;
    if (data.module === 'POS') {
      if (!data.posType) {
        throw new Error('POS type is required for POS module subscription');
      }
      originalPrice = POS_PRICING[data.posType];
    } else {
      originalPrice = MODULE_PRICING[data.module];
    }

    let finalPrice = originalPrice;
    const appliedCoupons: AppliedCoupon[] = [];

    // Apply coupon if provided
    if (data.couponCode) {
      try {
        const appliedCoupon = await this.couponService.applyCouponToSubscription(
          '', // Will be set after subscription creation
          data.couponCode,
          data.companyId,
          data.module,
          originalPrice
        );
        
        finalPrice = originalPrice - appliedCoupon.discountAmount;
        appliedCoupons.push(appliedCoupon);
        
        logger.info('Coupon applied to subscription', {
          couponCode: data.couponCode,
          discount: appliedCoupon.discountAmount,
          finalPrice,
        });
      } catch (error) {
        logger.warn('Failed to apply coupon, proceeding without discount', {
          couponCode: data.couponCode,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Create paid subscription
    const subscription = this.db.createDocument({
      companyId: data.companyId,
      module: data.module,
      active: true,
      price: Math.max(0, finalPrice), // Ensure price is not negative
      originalPrice,
      currency: data.currency || 'USD',
      nextBillingDate: this.getNextMonthDate(),
      posType: data.posType,
      subscribedAt: new Date(),
      isTrialMode: false,
      trialDuration: 0,
      appliedCoupons,
    });

    await this.subscriptionsCollection.insertOne(subscription as any);

    // Update coupon usage with actual subscription ID if coupon was applied
    if (data.couponCode && appliedCoupons.length > 0) {
      // The coupon service already handled the usage tracking
    }

    logger.info('Paid subscription created successfully', {
      subscriptionId: subscription._id,
      companyId: data.companyId,
      module: data.module,
      originalPrice,
      finalPrice,
    });

    return subscription as unknown as CompanySubscription;
  }

  async subscribeToPOS(data: {
    companyId: string;
    posType: PosBusinessType;
    currency?: 'KHR' | 'USD';
    startTrial?: boolean;
  }): Promise<CompanySubscription> {
    return this.subscribeToModule({
      companyId: data.companyId,
      module: 'POS',
      posType: data.posType,
      currency: data.currency,
      startTrial: data.startTrial,
    });
  }

  async unsubscribeFromModule(companyId: string, module: string): Promise<boolean> {
    logger.info('Unsubscribing company from module', { companyId, module });

    const result = await this.subscriptionsCollection.updateOne(
      { companyId, module, active: true },
      { $set: { active: false, updatedAt: new Date() } }
    );

    const success = result.modifiedCount > 0;
    
    if (success) {
      logger.info('Module unsubscription successful', { companyId, module });
    }

    return success;
  }

  async getCompanySubscriptions(companyId: string): Promise<CompanySubscription[]> {
    const subscriptions = await this.subscriptionsCollection
      .find({ companyId, active: true })
      .toArray();

    return subscriptions as unknown as CompanySubscription[];
  }

  async getAllCompanySubscriptions(companyId: string): Promise<CompanySubscription[]> {
    const subscriptions = await this.subscriptionsCollection
      .find({ companyId })
      .sort({ createdAt: -1 })
      .toArray();

    return subscriptions as unknown as CompanySubscription[];
  }

  async calculateMonthlyBilling(companyId: string): Promise<BillingInfo> {
    const subscriptions = await this.getCompanySubscriptions(companyId);

    const totalMonthlyAmount = subscriptions.reduce((sum, sub) => sum + sub.price, 0);
    
    // Find next billing date (earliest among all subscriptions)
    const nextBillingDate = subscriptions.reduce((earliest, sub) => {
      return sub.nextBillingDate < earliest ? sub.nextBillingDate : earliest;
    }, new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)); // Default to 1 year from now

    const activeModules = subscriptions.map(sub => {
      const moduleDiscount = sub.appliedCoupons.reduce((sum, coupon) => sum + coupon.discountAmount, 0);
      return {
        module: sub.module,
        price: sub.price,
        originalPrice: sub.originalPrice,
        discount: moduleDiscount,
        appliedCoupons: sub.appliedCoupons,
        posType: sub.posType,
      };
    });

    const billingInfo: BillingInfo = {
      companyId,
      totalMonthlyAmount,
      originalAmount: subscriptions.reduce((sum, sub) => sum + sub.originalPrice, 0),
      totalDiscount: subscriptions.reduce((sum, sub) => 
        sum + sub.appliedCoupons.reduce((discount, coupon) => discount + coupon.discountAmount, 0), 0
      ),
      currency: subscriptions[0]?.currency || 'USD',
      activeModules,
      nextBillingDate: subscriptions.length > 0 ? nextBillingDate : new Date(),
    };

    return billingInfo;
  }

  async updateSubscriptionPrice(subscriptionId: string, newPrice: number): Promise<CompanySubscription | null> {
    const success = await this.db.updateById(this.subscriptionsCollection, subscriptionId, {
      price: newPrice,
    });

    if (!success) {
      return null;
    }

    const subscription = await this.subscriptionsCollection.findOne({ _id: subscriptionId } as any);
    return subscription as unknown as CompanySubscription;
  }

  async processMonthlyBilling(companyId: string): Promise<BillingInfo> {
    logger.info('Processing monthly billing', { companyId });

    const subscriptions = await this.getCompanySubscriptions(companyId);
    const billingInfo = await this.calculateMonthlyBilling(companyId);

    // Update next billing dates for all active subscriptions
    const nextBillingDate = this.getNextMonthDate();
    
    await this.subscriptionsCollection.updateMany(
      { companyId, active: true },
      { 
        $set: { 
          nextBillingDate,
          lastBilledAt: new Date(),
          updatedAt: new Date(),
        } 
      }
    );

    logger.info('Monthly billing processed', {
      companyId,
      totalAmount: billingInfo.totalMonthlyAmount,
      moduleCount: subscriptions.length,
    });

    return {
      ...billingInfo,
      lastBilledAmount: billingInfo.totalMonthlyAmount,
      lastBilledDate: new Date(),
      nextBillingDate,
    };
  }

  /**
   * Extend subscription period when payment is successful
   * This is called after a successful payment to extend the usage period
   */
  async extendSubscriptionPeriod(data: {
    companyId: string;
    module: string;
    paymentId: string;
    paidAmount: number;
    paymentDate: Date;
    extensionPeriod?: number; // in days, defaults to 30 for monthly
  }): Promise<CompanySubscription> {
    logger.info('Extending subscription period after successful payment', {
      companyId: data.companyId,
      module: data.module,
      paymentId: data.paymentId,
      paidAmount: data.paidAmount,
    });

    const subscription = await this.subscriptionsCollection.findOne({
      companyId: data.companyId,
      module: data.module,
      active: true,
    });

    if (!subscription) {
      throw new Error(`No active subscription found for module ${data.module}`);
    }

    const extensionDays = data.extensionPeriod || 30; // Default to 30 days (monthly)
    const currentBillingDate = subscription.nextBillingDate || new Date();
    
    // Calculate new billing date - extend from current billing date, not today
    // This ensures users don't lose remaining time when they pay early
    const newBillingDate = new Date(currentBillingDate);
    newBillingDate.setDate(newBillingDate.getDate() + extensionDays);

    // If this was a trial conversion, convert to paid subscription
    const updateData: any = {
      nextBillingDate: newBillingDate,
      lastBilledAt: data.paymentDate,
      lastPaidAmount: data.paidAmount,
      updatedAt: new Date(),
    };

    // Convert trial to paid subscription if applicable
    if (subscription.isTrialMode) {
      updateData.isTrialMode = false;
      // Remove trial-specific fields
      updateData.$unset = {
        trialStartDate: '',
        trialEndDate: '',
      };

      logger.info('Converting trial to paid subscription during extension', {
        subscriptionId: subscription._id,
        module: data.module,
      });
    }

    // Update subscription
    if (subscription.isTrialMode) {
      // For trial conversion, we need to handle $unset separately
      await this.subscriptionsCollection.updateOne(
        { _id: subscription._id },
        {
          $set: {
            nextBillingDate: newBillingDate,
            lastBilledAt: data.paymentDate,
            lastPaidAmount: data.paidAmount,
            updatedAt: new Date(),
            isTrialMode: false,
          },
          $unset: {
            trialStartDate: '',
            trialEndDate: '',
          }
        }
      );
    } else {
      // For regular subscription extension
      await this.subscriptionsCollection.updateOne(
        { _id: subscription._id },
        { $set: updateData }
      );
    }

    // Update trial history if this was a trial conversion
    if (subscription.isTrialMode) {
      await this.trialStatusCollection.updateOne(
        { companyId: data.companyId },
        {
          $set: {
            'trialHistory.$[elem].status': 'converted',
            updatedAt: new Date(),
          },
        },
        {
          arrayFilters: [{ 'elem.module': data.module, 'elem.status': 'active' }],
        }
      );
    }

    logger.info('Subscription period extended successfully', {
      subscriptionId: subscription._id,
      companyId: data.companyId,
      module: data.module,
      previousBillingDate: currentBillingDate,
      newBillingDate,
      extensionDays,
      wasTrialConversion: subscription.isTrialMode,
    });

    // Return updated subscription
    const updatedSubscription = await this.subscriptionsCollection.findOne({ _id: subscription._id } as any);
    return updatedSubscription as unknown as CompanySubscription;
  }

  /**
   * Get subscription renewal information
   */
  async getSubscriptionRenewalInfo(companyId: string, module: string): Promise<{
    currentPeriodEnd: Date;
    renewalAmount: number;
    currency: string;
    daysRemaining: number;
    autoRenew: boolean;
  }> {
    const subscription = await this.subscriptionsCollection.findOne({
      companyId,
      module,
      active: true,
    });

    if (!subscription) {
      throw new Error(`No active subscription found for module ${module}`);
    }

    const currentPeriodEnd = subscription.nextBillingDate || new Date();
    const daysRemaining = this.getDaysRemaining(currentPeriodEnd);

    return {
      currentPeriodEnd,
      renewalAmount: subscription.price,
      currency: subscription.currency,
      daysRemaining: Math.max(0, daysRemaining),
      autoRenew: true, // Can be made configurable later
    };
  }

  /**
   * Handle subscription renewal/extension for different payment scenarios
   */
  async handleSubscriptionRenewal(data: {
    companyId: string;
    module: string;
    paymentId: string;
    paidAmount: number;
    paymentDate: Date;
    renewalType: 'monthly' | 'quarterly' | 'yearly' | 'custom';
    customDays?: number;
  }): Promise<CompanySubscription> {
    let extensionDays: number;

    switch (data.renewalType) {
      case 'monthly':
        extensionDays = 30;
        break;
      case 'quarterly':
        extensionDays = 90;
        break;
      case 'yearly':
        extensionDays = 365;
        break;
      case 'custom':
        extensionDays = data.customDays || 30;
        break;
      default:
        extensionDays = 30;
    }

    return this.extendSubscriptionPeriod({
      ...data,
      extensionPeriod: extensionDays,
    });
  }

  async getAvailableModules(): Promise<{
    modules: Array<{ name: string; price: number }>;
    posTypes: Array<{ type: PosBusinessType; price: number }>;
  }> {
    const modules = Object.entries(MODULE_PRICING).map(([name, price]) => ({
      name,
      price,
    }));

    const posTypes = Object.entries(POS_PRICING).map(([type, price]) => ({
      type: type as PosBusinessType,
      price,
    }));

    return { modules, posTypes };
  }

  async isModuleActive(companyId: string, module: string): Promise<boolean> {
    const subscription = await this.subscriptionsCollection.findOne({
      companyId,
      module,
      active: true,
    });

    return !!subscription;
  }

  async getPosType(companyId: string): Promise<PosBusinessType | null> {
    const posSubscription = await this.subscriptionsCollection.findOne({
      companyId,
      module: 'POS',
      active: true,
    });

    return posSubscription?.posType || null;
  }
} 