import { Elysia, t } from 'elysia';
import { CompanyService } from './service';
import { createLogger } from '../../../../../shared/utils/logger';

const logger = createLogger('COMPANY_CONTROLLER');
const companyService = new CompanyService();

export const companyController = new Elysia({ prefix: '/companies' })
  .get('/detail/:companyId', async ({ params }) => {
    try {
      const company = await companyService.getCompanyById(params.companyId);
      if (!company) {
        return {
          success: false,
          message: 'Company not found',
        };
      }
      
      return {
        success: true,
        data: company,
      };
    } catch (error) {
      logger.error('Get company failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get company');
    }
  })

  .get('/user/:userId', async ({ params }) => {
    try {
      const companies = await companyService.getUserCompanies(params.userId);
      
      return {
        success: true,
        data: companies,
      };
    } catch (error) {
      logger.error('Get user companies failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: params.userId,
      });
      
      throw new Error('Failed to get user companies');
    }
  })

  .post('/', async ({ body }) => {
    try {
      const company = await companyService.createCompany(body);
      logger.info('Company creation successful', { companyId: company._id });
      
      return {
        success: true,
        data: company,
        message: 'Company created successfully',
      };
    } catch (error) {
      logger.error('Company creation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyName: body.name,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Company creation failed');
    }
  }, {
    body: t.Object({
      name: t.String({ minLength: 1 }),
      email: t.String({ format: 'email' }),
      ownerId: t.String({ minLength: 1 }),
    }),
  })

  .patch('/detail/:companyId', async ({ params, body }) => {
    try {
      const company = await companyService.updateCompany(params.companyId, body);
      if (!company) {
        return {
          success: false,
          message: 'Company not found',
        };
      }
      
      return {
        success: true,
        data: company,
        message: 'Company updated successfully',
      };
    } catch (error) {
      logger.error('Update company failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to update company');
    }
  }, {
    body: t.Object({
      name: t.Optional(t.String({ minLength: 1 })),
      email: t.Optional(t.String({ format: 'email' })),
      phone: t.Optional(t.String()),
      businessType: t.Optional(t.Union([
        t.Literal('sole_proprietorship'),
        t.Literal('partnership'),
        t.Literal('private_limited'),
        t.Literal('public_limited'),
        t.Literal('branch_office'),
        t.Literal('representative_office'),
        t.Literal('ngo'),
        t.Literal('cooperative')
      ])),
      businessLicense: t.Optional(t.String()),
      taxId: t.Optional(t.String()),
      vatNumber: t.Optional(t.String()),
      address: t.Optional(t.Object({
        street: t.Optional(t.String()),
        commune: t.Optional(t.String()),
        district: t.Optional(t.String()),
        province: t.Optional(t.String()),
        postalCode: t.Optional(t.String()),
        country: t.Optional(t.String()),
      })),
      bankInfo: t.Optional(t.Object({
        bankName: t.String(),
        accountNumber: t.String(),
        accountName: t.String(),
        swift: t.Optional(t.String()),
      })),
      settings: t.Optional(t.Object({
        timezone: t.Optional(t.String()),
        currency: t.Optional(t.Union([t.Literal('KHR'), t.Literal('USD')])),
        language: t.Optional(t.Union([t.Literal('en'), t.Literal('km'), t.Literal('both')])),
        taxRate: t.Optional(t.Number()),
        witholdingTaxRate: t.Optional(t.Number()),
        dateFormat: t.Optional(t.Union([
          t.Literal('DD/MM/YYYY'),
          t.Literal('MM/DD/YYYY'),
          t.Literal('YYYY-MM-DD')
        ])),
        numberFormat: t.Optional(t.Union([t.Literal('US'), t.Literal('EU'), t.Literal('KH')])),
      })),
    }),
  })

  .post('/:companyId/users', async ({ params, body }) => {
    try {
      const userCompany = await companyService.addUserToCompany({
        ...body,
        companyId: params.companyId,
      });
      
      return {
        success: true,
        data: userCompany,
        message: 'User added to company successfully',
      };
    } catch (error) {
      logger.error('Add user to company failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        userId: body.userId,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to add user to company');
    }
  }, {
    body: t.Object({
      userId: t.String({ minLength: 1 }),
      role: t.Union([
        t.Literal('company_admin'),
        t.Literal('manager'),
        t.Literal('employee')
      ]),
      addedBy: t.String({ minLength: 1 }),
    }),
  })

  .delete('/:companyId/users/:userId', async ({ params }) => {
    try {
      const success = await companyService.removeUserFromCompany(
        params.userId,
        params.companyId
      );
      
      if (!success) {
        return {
          success: false,
          message: 'User not found in company',
        };
      }
      
      return {
        success: true,
        message: 'User removed from company successfully',
      };
    } catch (error) {
      logger.error('Remove user from company failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        userId: params.userId,
      });
      
      throw new Error('Failed to remove user from company');
    }
  })

  .delete('/detail/:companyId', async ({ params }) => {
    try {
      const success = await companyService.deactivateCompany(params.companyId);
      if (!success) {
        return {
          success: false,
          message: 'Company not found',
        };
      }
      
      return {
        success: true,
        message: 'Company deactivated successfully',
      };
    } catch (error) {
      logger.error('Deactivate company failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to deactivate company');
    }
  })

  // Ownership Transfer Routes
  .post('/:companyId/transfer-ownership', async ({ params, body }) => {
    try {
      const transfer = await companyService.initiateOwnershipTransfer({
        companyId: params.companyId,
        ...body,
      });
      
      return {
        success: true,
        data: transfer,
        message: 'Ownership transfer initiated successfully',
      };
    } catch (error) {
      logger.error('Initiate ownership transfer failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to initiate ownership transfer');
    }
  }, {
    body: t.Object({
      fromOwnerId: t.String({ minLength: 1 }),
      toOwnerId: t.String({ minLength: 1 }),
      transferType: t.Union([
        t.Literal('sale'),
        t.Literal('gift'),
        t.Literal('inheritance'),
        t.Literal('merger'),
        t.Literal('other')
      ]),
      transferAmount: t.Optional(t.Number()),
      currency: t.Optional(t.Union([t.Literal('KHR'), t.Literal('USD')])),
      reason: t.Optional(t.String()),
    }),
  })

  .patch('/transfers/:transferId/approve', async ({ params, body }) => {
    try {
      const transfer = await companyService.approveOwnershipTransfer(params.transferId, body.approvedBy);
      if (!transfer) {
        return {
          success: false,
          message: 'Transfer not found',
        };
      }
      
      return {
        success: true,
        data: transfer,
        message: 'Ownership transfer approved successfully',
      };
    } catch (error) {
      logger.error('Approve ownership transfer failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transferId: params.transferId,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to approve ownership transfer');
    }
  }, {
    body: t.Object({
      approvedBy: t.String({ minLength: 1 }),
    }),
  })

  .get('/:companyId/transfers', async ({ params }) => {
    try {
      const transfers = await companyService.getOwnershipTransfers(params.companyId);
      
      return {
        success: true,
        data: transfers,
      };
    } catch (error) {
      logger.error('Get ownership transfers failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get ownership transfers');
    }
  })

  // Investor Management Routes
  .post('/:companyId/investors', async ({ params, body }) => {
    try {
      const investor = await companyService.addInvestor({
        companyId: params.companyId,
        ...body,
      });
      
      return {
        success: true,
        data: investor,
        message: 'Investor added successfully',
      };
    } catch (error) {
      logger.error('Add investor failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to add investor');
    }
  }, {
    body: t.Object({
      userId: t.String({ minLength: 1 }),
      investmentAmount: t.Number({ minimum: 0 }),
      currency: t.Union([t.Literal('KHR'), t.Literal('USD')]),
      equityPercentage: t.Number({ minimum: 0, maximum: 100 }),
      investmentType: t.Union([
        t.Literal('initial'),
        t.Literal('additional'),
        t.Literal('bridge'),
        t.Literal('series_a'),
        t.Literal('series_b'),
        t.Literal('other')
      ]),
      dividendRights: t.Optional(t.Boolean()),
      votingRights: t.Optional(t.Boolean()),
      vestingSchedule: t.Optional(t.Object({
        totalMonths: t.Number(),
        cliffMonths: t.Number(),
        vestedMonths: t.Number(),
      })),
      notes: t.Optional(t.String()),
    }),
  })

  .get('/:companyId/investors', async ({ params }) => {
    try {
      const investors = await companyService.getCompanyInvestors(params.companyId);
      
      return {
        success: true,
        data: investors,
      };
    } catch (error) {
      logger.error('Get company investors failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get company investors');
    }
  })

  .patch('/investors/:investorId', async ({ params, body }) => {
    try {
      const investor = await companyService.updateInvestor(params.investorId, body);
      if (!investor) {
        return {
          success: false,
          message: 'Investor not found',
        };
      }
      
      return {
        success: true,
        data: investor,
        message: 'Investor updated successfully',
      };
    } catch (error) {
      logger.error('Update investor failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        investorId: params.investorId,
      });
      
      throw new Error('Failed to update investor');
    }
  }, {
    body: t.Object({
      investmentAmount: t.Optional(t.Number({ minimum: 0 })),
      equityPercentage: t.Optional(t.Number({ minimum: 0, maximum: 100 })),
      status: t.Optional(t.Union([
        t.Literal('active'),
        t.Literal('divested'),
        t.Literal('transferred'),
        t.Literal('liquidated')
      ])),
      dividendRights: t.Optional(t.Boolean()),
      votingRights: t.Optional(t.Boolean()),
      notes: t.Optional(t.String()),
    }),
  })

 