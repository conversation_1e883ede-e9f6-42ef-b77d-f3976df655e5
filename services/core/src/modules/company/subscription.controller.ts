import { Elysia, t } from 'elysia';
import { SubscriptionService } from './subscription.service';
import { createLogger } from '../../../../../shared/utils/logger';

const logger = createLogger('SUBSCRIPTION_CONTROLLER');
const subscriptionService = new SubscriptionService();

export const subscriptionController = new Elysia({ prefix: '/subscriptions' })
  // Get available modules and pricing
  .get('/modules', async () => {
    try {
      const modules = await subscriptionService.getAvailableModules();
      
      return {
        success: true,
        data: modules,
      };
    } catch (error) {
      logger.error('Get available modules failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw new Error('Failed to get available modules');
    }
  })

  // Get company trial status
  .get('/company/:companyId/trial-status', async ({ params }) => {
    try {
      const trialStatus = await subscriptionService.getCompanyTrialStatus(params.companyId);
      
      return {
        success: true,
        data: trialStatus,
      };
    } catch (error) {
      logger.error('Get company trial status failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get company trial status');
    }
  })

  // Get trial info for specific module
  .get('/company/:companyId/trial/:module', async ({ params }) => {
    try {
      const trialInfo = await subscriptionService.getTrialInfo(params.companyId, params.module);
      
      return {
        success: true,
        data: trialInfo,
      };
    } catch (error) {
      logger.error('Get trial info failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        module: params.module,
      });
      
      throw new Error('Failed to get trial info');
    }
  })

  // Start trial for general module (ERP, LOAN, ACCOUNTING)
  .post('/company/:companyId/start-trial', async ({ params, body, set }) => {
    try {
      const subscription = await subscriptionService.startTrial({
        companyId: params.companyId,
        ...body,
      });
      
      return {
        success: true,
        data: subscription,
        message: `Successfully started ${body.module} trial (${subscription.trialDuration} days)`,
      };
    } catch (error) {
      logger.error('Start trial failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        module: body.module,
      });
      
      set.status = 400;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start trial',
        code: 'TRIAL_START_FAILED'
      };
    }
  }, {
    body: t.Object({
      module: t.Union([
        t.Literal('ERP'),
        t.Literal('LOAN'),
        t.Literal('ACCOUNTING')
      ]),
      trialDuration: t.Optional(t.Number({ minimum: 1, maximum: 90 })),
    }),
  })

  // Start trial for POS module with business type
  .post('/company/:companyId/start-trial-pos', async ({ params, body, set }) => {
    try {
      const subscription = await subscriptionService.startTrial({
        companyId: params.companyId,
        module: 'POS',
        ...body,
      });
      
      return {
        success: true,
        data: subscription,
        message: `Successfully started POS trial for ${body.posType} (${subscription.trialDuration} days)`,
      };
    } catch (error) {
      logger.error('Start POS trial failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        posType: body.posType,
      });
      
      set.status = 400;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to start POS trial',
        code: 'POS_TRIAL_START_FAILED'
      };
    }
  }, {
    body: t.Object({
      posType: t.Union([
        t.Literal('RESTAURANT'),
        t.Literal('BAR'),
        t.Literal('BAKERY'),
        t.Literal('RETAIL_SHOP'),
        t.Literal('CLOTHING_STORE'),
        t.Literal('FURNITURE_STORE'),
        t.Literal('PHARMACY'),
        t.Literal('ELECTRONICS_STORE'),
        t.Literal('GROCERY_STORE'),
        t.Literal('BEAUTY_SALON'),
        t.Literal('SERVICE'),
        t.Literal('HOTEL'),
        t.Literal('GENERIC'),
      ]),
      trialDuration: t.Optional(t.Number({ minimum: 1, maximum: 90 })),
    }),
  })

  // Convert trial to paid subscription
  .post('/company/:companyId/convert-trial/:module', async ({ params }) => {
    try {
      const subscription = await subscriptionService.convertTrialToPaid(
        params.companyId,
        params.module
      );
      
      return {
        success: true,
        data: subscription,
        message: `Successfully converted ${params.module} trial to paid subscription`,
      };
    } catch (error) {
      logger.error('Convert trial to paid failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        module: params.module,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to convert trial to paid');
    }
  })

  // Check and expire trials (admin endpoint)
  .post('/admin/expire-trials', async () => {
    try {
      const expiredCount = await subscriptionService.checkAndExpireTrials();
      
      return {
        success: true,
        data: {
          expiredTrialsCount: expiredCount,
        },
        message: `${expiredCount} trials expired and deactivated`,
      };
    } catch (error) {
      logger.error('Expire trials failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw new Error('Failed to expire trials');
    }
  })

  // Get company subscriptions
  .get('/company/:companyId', async ({ params }) => {
    try {
      const subscriptions = await subscriptionService.getCompanySubscriptions(params.companyId);
      
      return {
        success: true,
        data: subscriptions,
      };
    } catch (error) {
      logger.error('Get company subscriptions failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get company subscriptions');
    }
  })

  // Get company billing info
  .get('/company/:companyId/billing', async ({ params }) => {
    try {
      const billing = await subscriptionService.calculateMonthlyBilling(params.companyId);
      
      return {
        success: true,
        data: billing,
      };
    } catch (error) {
      logger.error('Get company billing failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get company billing');
    }
  })

  // Subscribe to a general module (ERP, LOAN, ACCOUNTING)
  .post('/company/:companyId/subscribe', async ({ params, body }) => {
    try {
      const subscription = await subscriptionService.subscribeToModule({
        companyId: params.companyId,
        ...body,
      });
      
      return {
        success: true,
        data: subscription,
        message: body.startTrial 
          ? `Successfully started ${body.module} trial`
          : `Successfully subscribed to ${body.module} module`,
      };
    } catch (error) {
      logger.error('Module subscription failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        module: body.module,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to subscribe to module');
    }
  }, {
    body: t.Object({
      module: t.Union([
        t.Literal('ERP'),
        t.Literal('LOAN'),
        t.Literal('ACCOUNTING')
      ]),
      currency: t.Optional(t.Union([t.Literal('KHR'), t.Literal('USD')])),
      startTrial: t.Optional(t.Boolean()),
    }),
  })

  // Subscribe to POS module with business type
  .post('/company/:companyId/subscribe-pos', async ({ params, body }) => {
    try {
      const subscription = await subscriptionService.subscribeToPOS({
        companyId: params.companyId,
        ...body,
      });
      
      return {
        success: true,
        data: subscription,
        message: body.startTrial
          ? `Successfully started POS trial (${body.posType})`
          : `Successfully subscribed to POS module (${body.posType})`,
      };
    } catch (error) {
      logger.error('POS subscription failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        posType: body.posType,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to subscribe to POS module');
    }
  }, {
    body: t.Object({
      posType: t.Union([
        t.Literal('RESTAURANT'),
        t.Literal('BAR'),
        t.Literal('BAKERY'),
        t.Literal('RETAIL_SHOP'),
        t.Literal('CLOTHING_STORE'),
        t.Literal('FURNITURE_STORE'),
        t.Literal('PHARMACY'),
        t.Literal('ELECTRONICS_STORE'),
        t.Literal('GROCERY_STORE'),
        t.Literal('BEAUTY_SALON'),
        t.Literal('SERVICE'),
        t.Literal('HOTEL'),
        t.Literal('GENERIC'),
      ]),
      currency: t.Optional(t.Union([t.Literal('KHR'), t.Literal('USD')])),
      startTrial: t.Optional(t.Boolean()),
    }),
  })

  // Unsubscribe from a module
  .delete('/company/:companyId/unsubscribe/:module', async ({ params }) => {
    try {
      const success = await subscriptionService.unsubscribeFromModule(
        params.companyId,
        params.module
      );
      
      if (!success) {
        return {
          success: false,
          message: 'Subscription not found or already inactive',
        };
      }
      
      return {
        success: true,
        message: `Successfully unsubscribed from ${params.module} module`,
      };
    } catch (error) {
      logger.error('Module unsubscription failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        module: params.module,
      });
      
      throw new Error('Failed to unsubscribe from module');
    }
  })

  // Process monthly billing
  .post('/company/:companyId/process-billing', async ({ params }) => {
    try {
      const billing = await subscriptionService.processMonthlyBilling(params.companyId);
      
      return {
        success: true,
        data: billing,
        message: 'Monthly billing processed successfully',
      };
    } catch (error) {
      logger.error('Process billing failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to process monthly billing');
    }
  })

  // Check if module is active
  .get('/company/:companyId/module/:module/status', async ({ params }) => {
    try {
      const isActive = await subscriptionService.isModuleActive(
        params.companyId,
        params.module
      );
      
      return {
        success: true,
        data: {
          companyId: params.companyId,
          module: params.module,
          active: isActive,
        },
      };
    } catch (error) {
      logger.error('Check module status failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        module: params.module,
      });
      
      throw new Error('Failed to check module status');
    }
  })

  // Get company's POS type
  .get('/company/:companyId/pos-type', async ({ params }) => {
    try {
      const posType = await subscriptionService.getPosType(params.companyId);
      
      return {
        success: true,
        data: {
          companyId: params.companyId,
          posType: posType,
          hasPosSubscription: !!posType,
        },
      };
    } catch (error) {
      logger.error('Get POS type failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get POS type');
    }
  })

  // Update subscription price (admin function)
  .patch('/subscription/:subscriptionId/price', async ({ params, body }) => {
    try {
      const subscription = await subscriptionService.updateSubscriptionPrice(
        params.subscriptionId,
        body.newPrice
      );
      
      if (!subscription) {
        return {
          success: false,
          message: 'Subscription not found',
        };
      }
      
      return {
        success: true,
        data: subscription,
        message: 'Subscription price updated successfully',
      };
    } catch (error) {
      logger.error('Update subscription price failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        subscriptionId: params.subscriptionId,
      });
      
      throw new Error('Failed to update subscription price');
    }
  }, {
    body: t.Object({
      newPrice: t.Number({ minimum: 0 }),
    }),
  })

  // Get all subscriptions (including inactive) for a company
  .get('/company/:companyId/all', async ({ params }) => {
    try {
      const subscriptions = await subscriptionService.getAllCompanySubscriptions(params.companyId);
      
      return {
        success: true,
        data: subscriptions,
      };
    } catch (error) {
      logger.error('Get all company subscriptions failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get all company subscriptions');
    }
  })

  // Get subscription renewal information
  .get('/company/:companyId/module/:module/renewal-info', async ({ params }) => {
    try {
      const renewalInfo = await subscriptionService.getSubscriptionRenewalInfo(
        params.companyId,
        params.module
      );
      
      return {
        success: true,
        data: renewalInfo,
      };
    } catch (error) {
      logger.error('Get subscription renewal info failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        module: params.module,
      });
      
      throw new Error('Failed to get subscription renewal information');
    }
  })

  // Manually extend subscription period (admin)
  .post('/company/:companyId/module/:module/extend', async ({ params, body }) => {
    try {
      const extendedSubscription = await subscriptionService.extendSubscriptionPeriod({
        companyId: params.companyId,
        module: params.module,
        paymentId: body.paymentId || `MANUAL_${Date.now()}`,
        paidAmount: body.paidAmount,
        paymentDate: new Date(),
        extensionPeriod: body.extensionDays,
      });
      
      return {
        success: true,
        data: extendedSubscription,
        message: `Successfully extended ${params.module} subscription by ${body.extensionDays} days`,
      };
    } catch (error) {
      logger.error('Manual subscription extension failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
        module: params.module,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to extend subscription');
    }
  }, {
    body: t.Object({
      extensionDays: t.Number({ minimum: 1, maximum: 730 }), // Max 2 years
      paidAmount: t.Number({ minimum: 0 }),
      paymentId: t.Optional(t.String()),
    }),
  }); 