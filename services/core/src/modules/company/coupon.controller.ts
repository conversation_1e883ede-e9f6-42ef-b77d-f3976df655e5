import { Elysia, t } from 'elysia';
import { CouponService } from './coupon.service';
import { createLogger } from '../../../../../shared/utils/logger';

const logger = createLogger('COUPON_CONTROLLER');
const couponService = new CouponService();

export const couponController = new Elysia({ prefix: '/coupons' })
  // Admin endpoints for coupon management

  // Create new coupon
  .post('/admin/create', async ({ body }) => {
    try {
      const couponData = {
        ...body,
        startDate: new Date(body.startDate),
        endDate: new Date(body.endDate),
      };
      
      const coupon = await couponService.createCoupon(couponData);
      
      return {
        success: true,
        data: coupon,
        message: 'Coupon created successfully',
      };
    } catch (error) {
      logger.error('Create coupon failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        body,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to create coupon');
    }
  }, {
    body: t.Object({
      code: t.String({ minLength: 2, maxLength: 20 }),
      name: t.String({ minLength: 1, maxLength: 100 }),
      description: t.String({ maxLength: 500 }),
      type: t.Union([
        t.Literal('PERCENTAGE_DISCOUNT'),
        t.Literal('FIXED_AMOUNT_DISCOUNT'),
        t.Literal('FREE_TRIAL_EXTENSION'),
        t.Literal('FIRST_MONTH_FREE'),
        t.Literal('BUY_X_GET_Y_FREE'),
      ]),
      value: t.Number({ minimum: 0 }),
      applicability: t.Union([
        t.Literal('ALL_MODULES'),
        t.Literal('SPECIFIC_MODULE'),
        t.Literal('POS_ONLY'),
        t.Literal('NON_POS_MODULES'),
      ]),
      applicableModules: t.Optional(t.Array(t.String())),
      maxUses: t.Number({ minimum: 0 }),
      maxUsesPerCompany: t.Number({ minimum: 0 }),
      startDate: t.String(), // ISO date string
      endDate: t.String(),   // ISO date string
      minimumPurchaseAmount: t.Optional(t.Number({ minimum: 0 })),
      createdBy: t.String(),
    }),
  })

  // Update existing coupon
  .patch('/admin/:couponId', async ({ params, body }) => {
    try {
      const updateData: any = { ...body };
      
      // Convert date strings to Date objects if present
      if (body.startDate) updateData.startDate = new Date(body.startDate);
      if (body.endDate) updateData.endDate = new Date(body.endDate);
      
      const coupon = await couponService.updateCoupon(params.couponId, updateData);
      
      if (!coupon) {
        return {
          success: false,
          message: 'Coupon not found',
        };
      }
      
      return {
        success: true,
        data: coupon,
        message: 'Coupon updated successfully',
      };
    } catch (error) {
      logger.error('Update coupon failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        couponId: params.couponId,
      });
      
      throw new Error('Failed to update coupon');
    }
  }, {
    body: t.Partial(t.Object({
      name: t.String({ minLength: 1, maxLength: 100 }),
      description: t.String({ maxLength: 500 }),
      value: t.Number({ minimum: 0 }),
      applicableModules: t.Array(t.String()),
      maxUses: t.Number({ minimum: 0 }),
      maxUsesPerCompany: t.Number({ minimum: 0 }),
      startDate: t.String(),
      endDate: t.String(),
      minimumPurchaseAmount: t.Number({ minimum: 0 }),
      isActive: t.Boolean(),
    })),
  })

  // Deactivate coupon
  .delete('/admin/:couponId', async ({ params }) => {
    try {
      const success = await couponService.deactivateCoupon(params.couponId);
      
      if (!success) {
        return {
          success: false,
          message: 'Coupon not found',
        };
      }
      
      return {
        success: true,
        message: 'Coupon deactivated successfully',
      };
    } catch (error) {
      logger.error('Deactivate coupon failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        couponId: params.couponId,
      });
      
      throw new Error('Failed to deactivate coupon');
    }
  })

  // Get all coupons (admin)
  .get('/admin/all', async ({ query }) => {
    try {
      const includeInactive = query.includeInactive === 'true';
      const coupons = await couponService.getAllCoupons(includeInactive);
      
      return {
        success: true,
        data: coupons,
      };
    } catch (error) {
      logger.error('Get all coupons failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw new Error('Failed to get coupons');
    }
  }, {
    query: t.Object({
      includeInactive: t.Optional(t.String()),
    }),
  })

  // Get coupon statistics (admin)
  .get('/admin/:couponId/stats', async ({ params }) => {
    try {
      const stats = await couponService.getCouponStats(params.couponId);
      
      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      logger.error('Get coupon stats failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        couponId: params.couponId,
      });
      
      throw new Error('Failed to get coupon statistics');
    }
  })

  // Generate coupon code (admin)
  .post('/admin/generate-code', async ({ body }) => {
    try {
      const code = await couponService.generateCouponCode(body.prefix || '');
      
      return {
        success: true,
        data: { code },
        message: 'Coupon code generated successfully',
      };
    } catch (error) {
      logger.error('Generate coupon code failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw new Error('Failed to generate coupon code');
    }
  }, {
    body: t.Object({
      prefix: t.Optional(t.String({ maxLength: 4 })),
    }),
  })

  // User endpoints for coupon usage

  // Validate coupon code
  .post('/validate', async ({ body }) => {
    try {
      const validation = await couponService.validateCoupon(
        body.code,
        body.companyId,
        body.module,
        body.amount
      );
      
      return {
        success: validation.isValid,
        data: validation.isValid ? {
          coupon: validation.coupon,
          discountAmount: validation.discountAmount,
          finalAmount: validation.finalAmount,
        } : null,
        error: validation.error,
      };
    } catch (error) {
      logger.error('Validate coupon failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        code: body.code,
        companyId: body.companyId,
      });
      
      throw new Error('Failed to validate coupon');
    }
  }, {
    body: t.Object({
      code: t.String(),
      companyId: t.String(),
      module: t.String(),
      amount: t.Number({ minimum: 0 }),
    }),
  })

  // Apply coupon to subscription
  .post('/apply', async ({ body }) => {
    try {
      const appliedCoupon = await couponService.applyCouponToSubscription(
        body.subscriptionId,
        body.couponCode,
        body.companyId,
        body.module,
        body.originalAmount
      );
      
      return {
        success: true,
        data: appliedCoupon,
        message: 'Coupon applied successfully',
      };
    } catch (error) {
      logger.error('Apply coupon failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        subscriptionId: body.subscriptionId,
        couponCode: body.couponCode,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to apply coupon');
    }
  }, {
    body: t.Object({
      subscriptionId: t.String(),
      couponCode: t.String(),
      companyId: t.String(),
      module: t.String(),
      originalAmount: t.Number({ minimum: 0 }),
    }),
  })

  // Remove coupon from subscription
  .delete('/remove', async ({ body }) => {
    try {
      const success = await couponService.removeCouponFromSubscription(
        body.subscriptionId,
        body.couponCode
      );
      
      if (!success) {
        return {
          success: false,
          message: 'Coupon usage not found or already removed',
        };
      }
      
      return {
        success: true,
        message: 'Coupon removed successfully',
      };
    } catch (error) {
      logger.error('Remove coupon failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        subscriptionId: body.subscriptionId,
        couponCode: body.couponCode,
      });
      
      throw new Error('Failed to remove coupon');
    }
  }, {
    body: t.Object({
      subscriptionId: t.String(),
      couponCode: t.String(),
    }),
  })

  // Get company's coupon usage history
  .get('/company/:companyId/usage', async ({ params }) => {
    try {
      const usage = await couponService.getCompanyCouponUsage(params.companyId);
      
      return {
        success: true,
        data: usage,
      };
    } catch (error) {
      logger.error('Get company coupon usage failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get coupon usage');
    }
  })

  // Get coupon by code (public endpoint for verification)
  .get('/code/:code', async ({ params }) => {
    try {
      const coupon = await couponService.getCouponByCode(params.code);
      
      if (!coupon) {
        return {
          success: false,
          message: 'Coupon not found',
        };
      }
      
      // Return limited information for security
      return {
        success: true,
        data: {
          code: coupon.code,
          name: coupon.name,
          description: coupon.description,
          type: coupon.type,
          value: coupon.value,
          applicability: coupon.applicability,
          minimumPurchaseAmount: coupon.minimumPurchaseAmount,
          endDate: coupon.endDate,
        },
      };
    } catch (error) {
      logger.error('Get coupon by code failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        code: params.code,
      });
      
      throw new Error('Failed to get coupon information');
    }
  })

  // Get usage statistics for a specific coupon
  .get('/:couponId/usage', async ({ params }) => {
    try {
      const usage = await couponService.getCouponUsage(params.couponId);
      
      return {
        success: true,
        data: usage,
      };
    } catch (error) {
      logger.error('Get coupon usage failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        couponId: params.couponId,
      });
      
      throw new Error('Failed to get coupon usage');
    }
  }); 