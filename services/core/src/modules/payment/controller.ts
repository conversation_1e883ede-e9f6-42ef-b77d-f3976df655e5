import { Elysia, t } from 'elysia';
import { PaymentService } from './service';
import { createLogger } from '../../../../../shared/utils/logger';

const logger = createLogger('PAYMENT_CONTROLLER');
const paymentService = new PaymentService();

export const paymentController = new Elysia({ prefix: '/payments' })
  // Create payment (ACLEDA Bank integration)
  .post('/', async ({ body, set }) => {
    try {
      const response = await paymentService.createPayment(body);
      
      logger.info('Payment created successfully', { 
        paymentId: response.paymentId,
        method: response.paymentMethod,
        amount: response.amount
      });
      
      return {
        success: true,
        data: response,
        message: 'Payment created successfully'
      };
    } catch (error) {
      logger.error('Payment creation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        body
      });
      
      set.status = 400;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create payment',
        code: 'PAYMENT_CREATION_FAILED'
      };
    }
  }, {
    body: t.Object({
      amount: t.Number({ minimum: 0.01 }),
      currency: t.Union([t.Literal('USD'), t.Literal('KHR')]),
      description: t.String({ minLength: 1 }),
      companyId: t.String({ minLength: 1 }),
      module: t.String({ minLength: 1 }),
      paymentMethod: t.Union([
        t.Literal('ACLEDA_ECOMMERCE'),
        t.Literal('ACLEDA_REDIRECT'),
        t.Literal('BANK_TRANSFER')
      ]),
      customerInfo: t.Object({
        name: t.String({ minLength: 1 }),
        email: t.String({ format: 'email' }),
        phone: t.Optional(t.String())
      }),
      subscriptionId: t.Optional(t.String()),
      trialConversion: t.Optional(t.Boolean()),
      userId: t.Optional(t.String()),
      userAgent: t.Optional(t.String()),
      ipAddress: t.Optional(t.String())
    })
  })

  // Get payment by ID
  .get('/:paymentId', async ({ params, set }) => {
    try {
      const payment = await paymentService.getPayment(params.paymentId);
      
      if (!payment) {
        set.status = 404;
        return {
          success: false,
          error: 'Payment not found',
          code: 'PAYMENT_NOT_FOUND'
        };
      }
      
      return {
        success: true,
        data: payment
      };
    } catch (error) {
      logger.error('Get payment failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        paymentId: params.paymentId
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get payment',
        code: 'GET_PAYMENT_FAILED'
      };
    }
  })

  // Get payment history (general endpoint)
  .get('/history', async ({ query, set }) => {
    try {
      // For general history, we'll need a companyId in query params
      const companyId = query.companyId as string;
      
      if (!companyId) {
        set.status = 400;
        return {
          success: false,
          error: 'Company ID is required',
          code: 'MISSING_COMPANY_ID'
        };
      }

      const options = {
        page: query.page ? parseInt(query.page as string) : 1,
        limit: query.limit ? parseInt(query.limit as string) : 25,
        status: query.status as any,
        module: query.module as string,
        dateFrom: query.dateFrom ? new Date(query.dateFrom as string) : undefined,
        dateTo: query.dateTo ? new Date(query.dateTo as string) : undefined
      };

      const result = await paymentService.getPaymentHistory(companyId, options);
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('Get payment history failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        query
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get payment history',
        code: 'GET_HISTORY_FAILED'
      };
    }
  })

  // Get payment history for a company (specific endpoint)
  .get('/company/:companyId/history', async ({ params, query, set }) => {
    try {
      const options = {
        page: query.page ? parseInt(query.page as string) : 1,
        limit: query.limit ? parseInt(query.limit as string) : 25,
        status: query.status as any,
        module: query.module as string,
        dateFrom: query.dateFrom ? new Date(query.dateFrom as string) : undefined,
        dateTo: query.dateTo ? new Date(query.dateTo as string) : undefined
      };

      const result = await paymentService.getPaymentHistory(params.companyId, options);
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('Get payment history failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get payment history',
        code: 'GET_HISTORY_FAILED'
      };
    }
  }, {
    query: t.Object({
      page: t.Optional(t.String()),
      limit: t.Optional(t.String()),
      status: t.Optional(t.String()),
      module: t.Optional(t.String()),
      dateFrom: t.Optional(t.String()),
      dateTo: t.Optional(t.String())
    })
  })

  // Check ACLEDA payment status
  .get('/:paymentId/status', async ({ params, set }) => {
    try {
      const payment = await paymentService.getPayment(params.paymentId);
      
      if (!payment) {
        set.status = 404;
        return {
          success: false,
          error: 'Payment not found',
          code: 'PAYMENT_NOT_FOUND'
        };
      }

      // If payment has ACLEDA token, check status with ACLEDA
      if (payment.acledaPaymentTokenId && payment.status === 'pending') {
        try {
          const acledaStatus = await paymentService.checkACLEDAPaymentStatus(payment.acledaPaymentTokenId);
          
          // Update payment status based on ACLEDA response
          if (acledaStatus.code === 0 && acledaStatus.xTran.confirmDate > 0) {
            await paymentService.updatePaymentStatus(params.paymentId, 'completed', {
              transactionId: acledaStatus.coreRefNum,
              bankReference: acledaStatus.payerAccountNo
            });
          }
        } catch (error) {
          logger.warn('ACLEDA status check failed', { 
            paymentId: params.paymentId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Return updated payment
      const updatedPayment = await paymentService.getPayment(params.paymentId);
      
      return {
        success: true,
        data: {
          paymentId: updatedPayment.paymentId,
          status: updatedPayment.status,
          amount: updatedPayment.amount,
          currency: updatedPayment.currency,
          paidAt: updatedPayment.paidAt,
          transactionId: updatedPayment.transactionId,
          bankReference: updatedPayment.bankReference
        }
      };
    } catch (error) {
      logger.error('Check payment status failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        paymentId: params.paymentId
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to check payment status',
        code: 'STATUS_CHECK_FAILED'
      };
    }
  })

  // ACLEDA Bank payment callback
  .post('/callback', async ({ body, set }) => {
    try {
      logger.info('Received ACLEDA payment callback', { body });

      await paymentService.handlePaymentCallback(body);
      
      return {
        success: true,
        message: 'Callback processed successfully'
      };
    } catch (error) {
      logger.error('Payment callback processing failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        body
      });
      
      set.status = 400;
      return {
        success: false,
        error: 'Failed to process callback',
        code: 'CALLBACK_FAILED'
      };
    }
  }, {
    body: t.Object({
      paymentId: t.String(),
      status: t.Union([
        t.Literal('pending'),
        t.Literal('processing'),
        t.Literal('completed'),
        t.Literal('failed'),
        t.Literal('cancelled')
      ]),
      transactionId: t.String(),
      amount: t.Number(),
      currency: t.Union([t.Literal('USD'), t.Literal('KHR')]),
      paidAt: t.Optional(t.Date()),
      bankReference: t.Optional(t.String()),
      metadata: t.Optional(t.Record(t.String(), t.Any()))
    })
  })

  // Simulate payment success (for testing)
  .post('/:paymentId/simulate-success', async ({ params, set }) => {
    try {
      await paymentService.simulatePaymentSuccess(params.paymentId);
      
      return {
        success: true,
        message: 'Payment success simulated'
      };
    } catch (error) {
      logger.error('Payment simulation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        paymentId: params.paymentId
      });
      
      set.status = 400;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to simulate payment success',
        code: 'SIMULATION_FAILED'
      };
    }
  })

  // Payment success redirect handler (for ACLEDA redirects)
  .get('/success', async ({ query, set }) => {
    try {
      const paymentId = query.paymentId as string;
      
      if (!paymentId) {
        set.status = 400;
        return {
          success: false,
          error: 'Payment ID is required',
          code: 'MISSING_PAYMENT_ID'
        };
      }

      // Redirect to frontend success page
      set.redirect = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/payment/success?paymentId=${paymentId}`;
      
    } catch (error) {
      logger.error('Payment success redirect failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        query
      });
      
      // Redirect to error page
      set.redirect = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/payment/failed`;
    }
  })

  // Payment error redirect handler (for ACLEDA redirects)
  .get('/failed', async ({ query, set }) => {
    try {
      const paymentId = query.paymentId as string;
      
      if (paymentId) {
        // Mark payment as failed
        await paymentService.updatePaymentStatus(paymentId, 'failed');
      }

      // Redirect to frontend error page
      set.redirect = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/payment/failed?paymentId=${paymentId || ''}`;
      
    } catch (error) {
      logger.error('Payment error redirect failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        query
      });
      
      // Redirect to error page anyway
      set.redirect = `${process.env.FRONTEND_URL || 'http://localhost:5173'}/payment/failed`;
    }
  })

  // Simple test endpoint for debugging
  .post('/test', async ({ body, set }) => {
    try {
      const paymentId = `TEST_${Date.now()}`;
      
      return {
        success: true,
        data: {
          paymentId,
          message: 'Test payment endpoint working',
          receivedData: body
        }
      };
    } catch (error) {
      set.status = 500;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Test failed',
        code: 'TEST_FAILED'
      };
    }
  })

  // Simplified payment creation for debugging
  .post('/simple', async ({ body, set }) => {
    try {
      logger.info('Simple payment creation started', { body });
      
      const result = await paymentService.createSimplePayment(body);
      
      logger.info('Simple payment created successfully', { paymentId: result.paymentId });
      
      return {
        success: true,
        data: result,
        message: 'Simple payment created successfully'
      };
    } catch (error) {
      logger.error('Simple payment creation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      
      set.status = 400;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create simple payment',
        code: 'SIMPLE_PAYMENT_FAILED'
      };
    }
  })

  // Health check
  .get('/health', () => {
    return {
      status: 'ok',
      service: 'payment-service',
      timestamp: new Date().toISOString(),
      acleda: {
        environment: process.env.ACLEDA_ENVIRONMENT || 'UAT',
        configured: !!(process.env.ACLEDA_MERCHANT_ID && process.env.ACLEDA_SIGNATURE)
      }
    };
  }); 