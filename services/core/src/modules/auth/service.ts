import bcrypt from 'bcryptjs';
import { getDatabase } from '../../db/client';
import { createLogger } from '../../../../../shared/utils/logger';
import { User, LoginRequest, RegisterRequest, LoginResponse, AuthToken, SwitchCompanyRequest } from '../../../../../shared/types/auth';
import { generateHexId } from '../../../../../shared/utils/id-generator';
import { CompanyService } from '../company/service';

const logger = createLogger('AUTH_SERVICE');

// Custom error class for authentication failures
export class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthService {
  private companyService = new CompanyService();
  
  private get db() {
    return getDatabase();
  }

  private get usersCollection() {
    return this.db.getCollection('users');
  }

  private get tokensCollection() {
    return this.db.getCollection('auth_tokens');
  }

  async register(data: RegisterRequest): Promise<LoginResponse> {
    logger.info('Registering new user', { email: data.email });

    // Check if user already exists
    const existingUser = await this.usersCollection.findOne({ email: data.email });
    if (existingUser) {
      throw new Error('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(data.password, 12);

    // Create user document
    const user = this.db.createDocument({
      email: data.email,
      password: hashedPassword,
      firstName: data.firstName,
      lastName: data.lastName,
      isActive: true,
    });

    // Insert user
    await this.usersCollection.insertOne(user);

    // Create company and add user to it
    const company = await this.companyService.createCompany({
      name: data.companyName,
      email: data.email,
      ownerId: user._id,
    });

    // Update user with current company
    await this.db.updateById(this.usersCollection, user._id, {
      currentCompanyId: company._id,
    });

    // Generate authentication tokens (same as login)
    const accessToken = generateHexId(); // In production, use proper JWT
    const refreshToken = generateHexId();

    // Store tokens
    const accessTokenDoc = this.db.createDocument({
      userId: user._id,
      token: accessToken,
      type: 'access',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    });

    const refreshTokenDoc = this.db.createDocument({
      userId: user._id,
      token: refreshToken,
      type: 'refresh',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    await this.tokensCollection.insertMany([accessTokenDoc, refreshTokenDoc]);

    logger.info('User registered successfully with company and tokens', { 
      userId: user._id, 
      email: data.email,
      companyId: company._id,
      companyName: data.companyName,
    });

    // Return user without password and with tokens (same format as login)
    const { password, ...userWithoutPassword } = user;
    return {
      user: {
        ...userWithoutPassword,
        currentCompanyId: company._id,
      } as unknown as User,
      accessToken,
      refreshToken,
    };
  }

  async login(data: LoginRequest): Promise<LoginResponse> {
    logger.info('User login attempt', { email: data.email });

    // Find user by email
    const user = await this.usersCollection.findOne({ email: data.email });
    if (!user) {
      throw new AuthenticationError('Invalid email or password');
    }

    if (!user.isActive) {
      throw new AuthenticationError('Account is deactivated');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(data.password, user.password);
    if (!isValidPassword) {
      throw new AuthenticationError('Invalid email or password');
    }

    // Generate tokens
    const accessToken = generateHexId(); // In production, use proper JWT
    const refreshToken = generateHexId();

    // Store tokens
    const accessTokenDoc = this.db.createDocument({
      userId: user._id,
      token: accessToken,
      type: 'access',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    });

    const refreshTokenDoc = this.db.createDocument({
      userId: user._id,
      token: refreshToken,
      type: 'refresh',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    await this.tokensCollection.insertMany([accessTokenDoc, refreshTokenDoc]);

    logger.info('User logged in successfully', { 
      userId: user._id, 
      email: data.email 
    });

    const { password, ...userWithoutPassword } = user;
    return {
      user: userWithoutPassword as unknown as User,
      accessToken,
      refreshToken,
    };
  }

  async validateToken(token: string): Promise<User | null> {
    const tokenDoc = await this.tokensCollection.findOne({
      token,
      type: 'access',
      expiresAt: { $gt: new Date() },
    });

    if (!tokenDoc) {
      return null;
    }

    const user = await this.usersCollection.findOne({ _id: tokenDoc.userId } as any);
    if (!user || !user.isActive) {
      return null;
    }

    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword as unknown as User;
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string } | null> {
    const tokenDoc = await this.tokensCollection.findOne({
      token: refreshToken,
      type: 'refresh',
      expiresAt: { $gt: new Date() },
    });

    if (!tokenDoc) {
      return null;
    }

    const user = await this.usersCollection.findOne({ _id: tokenDoc.userId } as any);
    if (!user || !user.isActive) {
      return null;
    }

    // Generate new tokens
    const newAccessToken = generateHexId();
    const newRefreshToken = generateHexId();

    // Remove old tokens
    await this.tokensCollection.deleteMany({ userId: user._id });

    // Create new tokens
    const accessTokenDoc = this.db.createDocument({
      userId: user._id,
      token: newAccessToken,
      type: 'access',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    });

    const refreshTokenDoc = this.db.createDocument({
      userId: user._id,
      token: newRefreshToken,
      type: 'refresh',
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });

    await this.tokensCollection.insertMany([accessTokenDoc, refreshTokenDoc]);

    logger.info('Token refreshed successfully', { userId: user._id });

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
    };
  }

  async logout(token: string): Promise<void> {
    await this.tokensCollection.deleteOne({ token });
    logger.info('User logged out successfully');
  }

  async getUserById(id: string): Promise<User | null> {
    const user = await this.usersCollection.findOne({ _id: id } as any);
    if (!user) {
      return null;
    }

    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword as unknown as User;
  }

  async getUsersByCompany(companyId: string): Promise<User[]> {
    const users = await this.usersCollection.find({ companyId }).toArray();
    return users.map(user => {
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword as unknown as User;
    });
  }

  async updateUser(id: string, data: Partial<User>): Promise<User | null> {
    const success = await this.db.updateById(this.usersCollection, id, data);
    if (!success) {
      return null;
    }

    return this.getUserById(id);
  }

  async deactivateUser(id: string): Promise<boolean> {
    return this.db.updateById(this.usersCollection, id, { isActive: false });
  }

  async switchCompany(userId: string, data: SwitchCompanyRequest): Promise<User | null> {
    logger.info('User switching company', { userId, companyId: data.companyId });

    // Check if user has access to this company
    const userRole = await this.companyService.getUserCompanyRole(userId, data.companyId);
    if (!userRole) {
      throw new Error('User does not have access to this company');
    }

    // Update user's current company
    const success = await this.db.updateById(this.usersCollection, userId, {
      currentCompanyId: data.companyId,
    });

    if (!success) {
      throw new Error('Failed to switch company');
    }

    logger.info('User switched company successfully', { 
      userId, 
      companyId: data.companyId,
      role: userRole,
    });

    return this.getUserById(userId);
  }

  async getUserWithCompanies(userId: string): Promise<(User & { companies: Array<{ _id: string; name: string; role: string; joinedAt: Date }> }) | null> {
    const user = await this.getUserById(userId);
    if (!user) {
      return null;
    }

    const companies = await this.companyService.getUserCompanies(userId);
    
    return {
      ...user,
      companies: companies.map(company => ({
        _id: company._id,
        name: company.name,
        role: company.role,
        joinedAt: company.joinedAt,
      })),
    } as User & { companies: Array<{ _id: string; name: string; role: string; joinedAt: Date }> };
  }
} 