import { Elysia, t } from 'elysia';
import { AuthService } from './service';
import { createLogger } from '../../../../../shared/utils/logger';

const logger = createLogger('AUTH_CONTROLLER');
const authService = new AuthService();

export const authController = new Elysia({ prefix: '/auth' })
  .post('/register', async ({ body }) => {
    try {
      const result = await authService.register(body);
      logger.info('User registration successful', { userId: result.user._id });
      
      return {
        success: true,
        data: {
          user: result.user,
          tokens: {
            accessToken: result.accessToken,
            refreshToken: result.refreshToken,
          },
        },
        message: 'User registered successfully',
      };
    } catch (error) {
      logger.error('User registration failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        email: body.email,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Registration failed');
    }
  }, {
    body: t.Object({
      email: t.String({ format: 'email' }),
      password: t.String({ minLength: 6 }),
      firstName: t.String({ minLength: 1 }),
      lastName: t.String({ minLength: 1 }),
      companyName: t.String({ minLength: 1 }),
      role: t.Optional(t.Union([
        t.Literal('company_admin'),
        t.Literal('manager'),
        t.Literal('employee')
      ])),
    }),
  })

  .post('/login', async ({ body }) => {
    try {
      const result = await authService.login(body);
      logger.info('User login successful', { userId: result.user._id });
      
      return {
        success: true,
        data: result,
        message: 'Login successful',
      };
    } catch (error) {
      logger.error('User login failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        email: body.email,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Login failed');
    }
  }, {
    body: t.Object({
      email: t.String({ format: 'email' }),
      password: t.String({ minLength: 1 }),
    }),
  })

  .post('/refresh', async ({ body }) => {
    try {
      const result = await authService.refreshToken(body.refreshToken);
      if (!result) {
        throw new Error('Invalid refresh token');
      }
      
      logger.info('Token refresh successful');
      
      return {
        success: true,
        data: result,
        message: 'Token refreshed successfully',
      };
    } catch (error) {
      logger.error('Token refresh failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw new Error(error instanceof Error ? error.message : 'Token refresh failed');
    }
  }, {
    body: t.Object({
      refreshToken: t.String({ minLength: 1 }),
    }),
  })

  .post('/logout', async ({ body }) => {
    try {
      await authService.logout(body.token);
      logger.info('User logout successful');
      
      return {
        success: true,
        message: 'Logout successful',
      };
    } catch (error) {
      logger.error('User logout failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      throw new Error(error instanceof Error ? error.message : 'Logout failed');
    }
  }, {
    body: t.Object({
      token: t.String({ minLength: 1 }),
    }),
  })

  .get('/validate/:token', async ({ params }) => {
    try {
      const user = await authService.validateToken(params.token);
      if (!user) {
        return {
          success: false,
          message: 'Invalid token',
        };
      }
      
      return {
        success: true,
        data: user,
        message: 'Token is valid',
      };
    } catch (error) {
      logger.error('Token validation failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      
      return {
        success: false,
        message: 'Token validation failed',
      };
    }
  })

  .get('/users/:id', async ({ params }) => {
    try {
      const user = await authService.getUserById(params.id);
      if (!user) {
        return {
          success: false,
          message: 'User not found',
        };
      }
      
      return {
        success: true,
        data: user,
      };
    } catch (error) {
      logger.error('Get user failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: params.id,
      });
      
      throw new Error('Failed to get user');
    }
  })

  .get('/users/company/:companyId', async ({ params }) => {
    try {
      const users = await authService.getUsersByCompany(params.companyId);
      
      return {
        success: true,
        data: users,
      };
    } catch (error) {
      logger.error('Get company users failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: params.companyId,
      });
      
      throw new Error('Failed to get company users');
    }
  })

  .patch('/users/:id', async ({ params, body }) => {
    try {
      const user = await authService.updateUser(params.id, body);
      if (!user) {
        return {
          success: false,
          message: 'User not found',
        };
      }
      
      return {
        success: true,
        data: user,
        message: 'User updated successfully',
      };
    } catch (error) {
      logger.error('Update user failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: params.id,
      });
      
      throw new Error('Failed to update user');
    }
  }, {
    body: t.Object({
      firstName: t.Optional(t.String({ minLength: 1 })),
      lastName: t.Optional(t.String({ minLength: 1 })),
      role: t.Optional(t.Union([
        t.Literal('super_admin'),
        t.Literal('company_admin'),
        t.Literal('manager'),
        t.Literal('employee')
      ])),
      isActive: t.Optional(t.Boolean()),
    }),
  })

  .delete('/users/:id', async ({ params }) => {
    try {
      const success = await authService.deactivateUser(params.id);
      if (!success) {
        return {
          success: false,
          message: 'User not found',
        };
      }
      
      return {
        success: true,
        message: 'User deactivated successfully',
      };
    } catch (error) {
      logger.error('Deactivate user failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: params.id,
      });
      
      throw new Error('Failed to deactivate user');
    }
  })

  .post('/switch-company', async ({ body }) => {
    try {
      const user = await authService.switchCompany(body.userId, body);
      if (!user) {
        return {
          success: false,
          message: 'User not found',
        };
      }
      
      return {
        success: true,
        data: user,
        message: 'Company switched successfully',
      };
    } catch (error) {
      logger.error('Switch company failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: body.userId,
        companyId: body.companyId,
      });
      
      throw new Error(error instanceof Error ? error.message : 'Failed to switch company');
    }
  }, {
    body: t.Object({
      userId: t.String({ minLength: 1 }),
      companyId: t.String({ minLength: 1 }),
    }),
  })

  .get('/users/:id/companies', async ({ params }) => {
    try {
      const userWithCompanies = await authService.getUserWithCompanies(params.id);
      if (!userWithCompanies) {
        return {
          success: false,
          message: 'User not found',
        };
      }
      
      return {
        success: true,
        data: userWithCompanies,
      };
    } catch (error) {
      logger.error('Get user companies failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: params.id,
      });
      
      throw new Error('Failed to get user companies');
    }
  }); 