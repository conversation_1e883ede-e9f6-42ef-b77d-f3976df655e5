import { MongoClient, Db, Collection } from 'mongodb';
import { createLogger } from '../../../../shared/utils/logger';
import { generateHexId } from '../../../../shared/utils/id-generator';

const logger = createLogger('CORE_DB');

class DatabaseClient {
  private client: MongoClient | null = null;
  private db: Db | null = null;
  private uri: string;
  private dbName: string;

  constructor(uri: string, dbName: string = 'elypos') {
    this.uri = uri;
    this.dbName = dbName;
  }

  async connect(): Promise<void> {
    try {
      this.client = new MongoClient(this.uri);
      await this.client.connect();
      this.db = this.client.db(this.dbName);
      
      logger.info('Successfully connected to MongoDB', {
        dbName: this.dbName,
      });
    } catch (error) {
      logger.error('Failed to connect to MongoDB', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.db = null;
      logger.info('Disconnected from MongoDB');
    }
  }

  getDb(): Db {
    if (!this.db) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.db;
  }

  getCollection(name: string): Collection {
    return this.getDb().collection(name);
  }

  // Helper method to create documents with hex ID as _id
  createDocument(data: Record<string, any>): Record<string, any> {
    const now = new Date();
    return {
      _id: generateHexId(),
      ...data,
      createdAt: now,
      updatedAt: now,
    };
  }

  // Helper method to update documents with updatedAt  
  updateDocument(data: Record<string, any>): Record<string, any> {
    return {
      ...data,
      updatedAt: new Date(),
    };
  }

  // Helper method to find by hex ID
  async findById(collection: Collection, id: string): Promise<any> {
    return await collection.findOne({ _id: id } as any);
  }

  // Helper method to update by hex ID
  async updateById(
    collection: Collection, 
    id: string, 
    update: Record<string, any>
  ): Promise<boolean> {
    const result = await collection.updateOne(
      { _id: id } as any,
      { $set: this.updateDocument(update) }
    );
    return result.modifiedCount > 0;
  }

  // Helper method to delete by hex ID
  async deleteById(collection: Collection, id: string): Promise<boolean> {
    const result = await collection.deleteOne({ _id: id } as any);
    return result.deletedCount > 0;
  }
}

let dbClient: DatabaseClient | null = null;

export function initializeDatabase(uri: string, dbName?: string): DatabaseClient {
  if (!dbClient) {
    dbClient = new DatabaseClient(uri, dbName);
  }
  return dbClient;
}

export function getDatabase(): DatabaseClient {
  if (!dbClient) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return dbClient;
}

export { DatabaseClient };