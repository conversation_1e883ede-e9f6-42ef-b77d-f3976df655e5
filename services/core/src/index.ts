import { swagger } from '@elysiajs/swagger';
import { Elysia } from 'elysia';
import { env } from './config/env';
import { authController } from './modules/auth/controller';
import { companyController } from './modules/company/controller';
import { subscriptionController } from './modules/company/subscription.controller';
import { couponController } from './modules/company/coupon.controller';
import { paymentController } from './modules/payment/controller';
import { initializeDatabase } from './db/client';
import { createLogger } from '../../../shared/utils/logger';

const logger = createLogger('CORE_SERVICE');

// Initialize database connection
async function startServer() {
  try {
    logger.info('Starting Core Service...', { port: env.PORT });
    
    // Initialize database
    const dbClient = initializeDatabase(env.MONGODB_URI, env.MONGODB_DB_NAME);
    await dbClient.connect();
    logger.info('Database connected successfully');

    // Start the server
    startElysia();
  } catch (error) {
    logger.error('Failed to start server', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    process.exit(1);
  }
}

function startElysia() {
const app = new Elysia()
  .use(swagger({
    documentation: {
      info: {
        title: 'ElyPOS Core API',
        version: '1.0.0',
        description: 'Core authentication and company management services',
      },
      servers: [
        {
          url: 'http://localhost:3001',
          description: 'Development server',
        },
      ],
    },
    path: '/swagger',
  }))
  .get('/', () => ({
    message: 'ElyPOS Core Service',
    version: '1.0.0',
    status: 'running',
  }))
  .get('/health', () => ({
    status: 'ok',
    service: 'elypos-core',
    database: 'connected',
    timestamp: new Date().toISOString(),
  }))
  .use(authController)
  .use(companyController)
  .use(subscriptionController)
  .use(couponController)
  .use(paymentController)
  .onError(({ error, code }) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Request error', { error: errorMessage, code });
    
    return {
      success: false,
      error: errorMessage,
      code,
    };
  })
  .listen(env.PORT);

logger.info(`🚀 Core service is running on port ${env.PORT}`, {
  port: env.PORT,
  environment: env.NODE_ENV,
  database: env.MONGODB_DB_NAME,
});
}

// Start the server
startServer();

logger.info('Available routes:', {
  routes: [
    'GET /',
    'GET /health',
    'GET /swagger',
    'POST /auth/register',
    'POST /auth/login',
    'POST /auth/refresh',
    'POST /auth/logout',
    'POST /auth/switch-company',
    'GET /auth/validate/:token',
    'GET /auth/users/:id',
    'GET /auth/users/:id/companies',
    'GET /auth/users/company/:companyId',
    'PATCH /auth/users/:id',
    'DELETE /auth/users/:id',
    'GET /companies/detail/:companyId',
    'GET /companies/user/:userId',
    'POST /companies',
    'PATCH /companies/detail/:companyId',
    'POST /companies/:companyId/users',
    'DELETE /companies/:companyId/users/:userId',
    'DELETE /companies/detail/:companyId',
    'POST /companies/:companyId/transfer-ownership',
    'PATCH /companies/transfers/:transferId/approve',
    'GET /companies/:companyId/transfers',
    'POST /companies/:companyId/investors',
    'GET /companies/:companyId/investors',
    'PATCH /companies/investors/:investorId',
    'GET /subscriptions/modules',
    'GET /subscriptions/company/:companyId/trial-status',
    'GET /subscriptions/company/:companyId/trial/:module',
    'POST /subscriptions/company/:companyId/start-trial',
    'POST /subscriptions/company/:companyId/start-trial-pos',
    'POST /subscriptions/company/:companyId/convert-trial/:module',
    'POST /subscriptions/admin/expire-trials',
    'GET /subscriptions/company/:companyId',
    'GET /subscriptions/company/:companyId/billing',
    'POST /subscriptions/company/:companyId/subscribe',
    'POST /subscriptions/company/:companyId/subscribe-pos',
    'DELETE /subscriptions/company/:companyId/unsubscribe/:module',
    'POST /subscriptions/company/:companyId/process-billing',
    'GET /subscriptions/company/:companyId/module/:module/status',
    'GET /subscriptions/company/:companyId/pos-type',
    'PATCH /subscriptions/subscription/:subscriptionId/price',
    'GET /subscriptions/company/:companyId/all',
    'POST /coupons/admin/create',
    'PATCH /coupons/admin/:couponId',
    'DELETE /coupons/admin/:couponId',
    'GET /coupons/admin/all',
    'GET /coupons/admin/:couponId/stats',
    'POST /coupons/admin/generate-code',
    'POST /coupons/validate',
    'POST /coupons/apply',
    'DELETE /coupons/remove',
    'GET /coupons/company/:companyId/usage',
    'GET /coupons/code/:code',
    'GET /coupons/:couponId/usage',
    'POST /payments',
    'GET /payments/:paymentId',
    'GET /payments/history',
    'GET /payments/company/:companyId/history',
    'GET /payments/:paymentId/status',
    'POST /payments/callback',
    'POST /payments/:paymentId/simulate-success',
    'GET /payments/success',
    'GET /payments/error',
    'GET /payments/health',
  ],
});

// Initialize database connection
const dbClient = initializeDatabase(env.MONGODB_URI, env.MONGODB_DB_NAME);
dbClient.connect()
  .then(() => {
    logger.info('Database connection established');
  })
  .catch((error: any) => {
    logger.error('Database connection failed', { error: error.message });
    process.exit(1);
  }); 