import { createLogger } from '../../../../shared/utils/logger';

const logger = createLogger('CORE_ENV');

export interface CoreEnv {
  NODE_ENV: string;
  PORT: number;
  LOG_LEVEL: string;
  MONGODB_URI: string;
  MONGODB_DB_NAME: string;
  JWT_SECRET: string;
  JWT_REFRESH_SECRET: string;
  JWT_EXPIRES_IN: string;
  JWT_REFRESH_EXPIRES_IN: string;
  CORS_ORIGIN: string;
  RATE_LIMIT_WINDOW_MS: number;
  RATE_LIMIT_MAX_REQUESTS: number;
  // ACLEDA Bank Configuration
  ACLEDA_ENVIRONMENT: string;
  ACLEDA_MERCHANT_ID: string;
  ACLEDA_LOGIN_ID: string;
  ACLEDA_PASSWORD: string;
  ACLEDA_SIGNATURE: string;
  ACLEDA_MERCHANT_NAME: string;
  ACLEDA_BASE_URL: string;
  ACLEDA_PAYMENT_PAGE_URL: string;
  ACLEDA_OPEN_SESSION_URL: string;
  ACLEDA_GET_STATUS_URL: string;
  ACLEDA_SUCCESS_URL: string;
  ACLEDA_ERROR_URL: string;
  ACLEDA_CALLBACK_URL: string;
}

const loadEnv = (): CoreEnv => {
  const env: CoreEnv = {
    NODE_ENV: process.env.NODE_ENV || 'development',
    PORT: parseInt(process.env.PORT || '3001', 10),
    LOG_LEVEL: process.env.LOG_LEVEL || 'INFO',
    MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017',
    MONGODB_DB_NAME: process.env.MONGODB_DB_NAME || 'elypos_core',
    JWT_SECRET: process.env.JWT_SECRET || 'default_jwt_secret',
    JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET || 'default_refresh_secret',
    JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '24h',
    JWT_REFRESH_EXPIRES_IN: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
    CORS_ORIGIN: process.env.CORS_ORIGIN || 'http://localhost:5174,http://localhost:3000',
    RATE_LIMIT_WINDOW_MS: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10),
    RATE_LIMIT_MAX_REQUESTS: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
    // ACLEDA Bank Configuration
    ACLEDA_ENVIRONMENT: process.env.ACLEDA_ENVIRONMENT || 'UAT',
    ACLEDA_MERCHANT_ID: process.env.ACLEDA_MERCHANT_ID || '/wRUtOaUXhK1l9JkMygbMW44ms0=',
    ACLEDA_LOGIN_ID: process.env.ACLEDA_LOGIN_ID || 'cosmouser',
    ACLEDA_PASSWORD: process.env.ACLEDA_PASSWORD || 'cosmouser',
    ACLEDA_SIGNATURE: process.env.ACLEDA_SIGNATURE || 'demo_signature',
    ACLEDA_MERCHANT_NAME: process.env.ACLEDA_MERCHANT_NAME || 'COSMOSDIGITAL',
    ACLEDA_BASE_URL: process.env.ACLEDA_BASE_URL || 'https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL',
    ACLEDA_PAYMENT_PAGE_URL: process.env.ACLEDA_PAYMENT_PAGE_URL || 'https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/paymentPage.jsp',
    ACLEDA_OPEN_SESSION_URL: process.env.ACLEDA_OPEN_SESSION_URL || 'https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/openSessionV2',
    ACLEDA_GET_STATUS_URL: process.env.ACLEDA_GET_STATUS_URL || 'https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/getTxnStatus',
    ACLEDA_SUCCESS_URL: process.env.ACLEDA_SUCCESS_URL || 'http://localhost:5174/payment/success',
    ACLEDA_ERROR_URL: process.env.ACLEDA_ERROR_URL || 'http://localhost:5174/payment/failed',
    ACLEDA_CALLBACK_URL: process.env.ACLEDA_CALLBACK_URL || 'http://localhost:3001/payments/callback'
  };

  // Validate critical environment variables
  const requiredEnvVars = ['JWT_SECRET', 'MONGODB_URI'];
  const missingEnvVars = requiredEnvVars.filter(varName => 
    !process.env[varName] || process.env[varName] === `default_${varName.toLowerCase()}`
  );

  if (missingEnvVars.length > 0 && env.NODE_ENV === 'production') {
    logger.error('Missing required environment variables for production:', { missingEnvVars });
    throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
  }

  // Validate ACLEDA Bank configuration for production
  const acledaRequiredVars = ['ACLEDA_MERCHANT_ID', 'ACLEDA_SIGNATURE'];
  const missingACLEDAVars = acledaRequiredVars.filter(varName => 
    !process.env[varName] || process.env[varName]?.includes('demo_') || process.env[varName]?.includes('default_')
  );

  if (missingACLEDAVars.length > 0 && env.NODE_ENV === 'production') {
    logger.error('Missing required ACLEDA Bank environment variables for production:', { missingACLEDAVars });
    throw new Error(`Missing required ACLEDA Bank variables: ${missingACLEDAVars.join(', ')}`);
  }

  logger.info('Core service environment variables loaded successfully', {
    NODE_ENV: env.NODE_ENV,
    PORT: env.PORT,
    LOG_LEVEL: env.LOG_LEVEL,
    MONGODB_DB_NAME: env.MONGODB_DB_NAME,
    ACLEDA_ENVIRONMENT: env.ACLEDA_ENVIRONMENT,
    ACLEDA_MERCHANT_NAME: env.ACLEDA_MERCHANT_NAME
  });

  return env;
};

export const env = loadEnv(); 