import { Elysia } from 'elysia';
import { cors } from '@elysiajs/cors';
import { swagger } from '@elysiajs/swagger';
import { createLogger } from '../../../shared/utils/logger';

const logger = createLogger('LOAN_SERVICE');

const PORT = process.env.PORT || '3003';

const app = new Elysia()
  .use(cors({
    origin: true,
    credentials: true,
  }))
  .use(swagger({
    documentation: {
      info: {
        title: 'ElyPOS Loan Service API',
        description: 'Loan management service for applications, repayments, and schedules',
        version: '1.0.0',
      },
      tags: [
        { name: 'Applications', description: 'Loan application endpoints' },
        { name: 'Repayments', description: 'Loan repayment endpoints' },
        { name: 'Schedules', description: 'Payment schedule endpoints' },
      ],
    },
  }))
  .get('/', () => {
    return {
      service: 'elypos-loan',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
    };
  })
  .get('/health', () => {
    return {
      status: 'ok',
      service: 'elypos-loan',
      timestamp: new Date().toISOString(),
    };
  })
  .onError(({ error, code }) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    logger.error('Loan service error occurred', {
      code,
      error: errorMessage,
    });

    if (code === 'NOT_FOUND') {
      return {
        success: false,
        error: 'Route not found',
        message: 'The requested endpoint does not exist',
        timestamp: new Date().toISOString(),
      };
    }

    return {
      success: false,
      error: 'Internal server error',
      message: errorMessage,
      timestamp: new Date().toISOString(),
    };
  })
  .listen(parseInt(PORT));

logger.info(`🚀 Loan service is running on port ${PORT}`, {
  port: PORT,
  environment: process.env.NODE_ENV || 'development',
});

logger.info('Available routes:', {
  routes: [
    'GET /',
    'GET /health',
    'GET /swagger',
  ],
}); 