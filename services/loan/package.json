{"name": "elypos-loan", "version": "1.0.0", "description": "Loan management service for applications, repayments, and schedules", "main": "src/index.ts", "scripts": {"dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts", "build": "bun build src/index.ts --outdir=dist", "clean": "rm -rf dist"}, "dependencies": {"elysia": "^1.0.0", "@elysiajs/cors": "^1.0.0", "@elysiajs/swagger": "^1.0.0", "mongodb": "^6.3.0"}, "devDependencies": {"@types/bun": "latest", "typescript": "^5.0.0"}, "module": "src/index.ts"}