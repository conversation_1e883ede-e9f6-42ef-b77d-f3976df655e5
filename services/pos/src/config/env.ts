import { createLogger } from '../../../../shared/utils/logger';

const logger = createLogger('POS_ENV');

interface PosEnv {
  PORT: string;
  NODE_ENV: string;
  MONGODB_URI: string;
  MONGODB_DB_NAME: string;
  CORE_SERVICE_URL: string;
  LOG_LEVEL: string;
}

function validateEnv(): PosEnv {
  const requiredEnvVars = [
    'PORT',
    'NODE_ENV',
    'MONGODB_URI',
    'MONGODB_DB_NAME',
    'CORE_SERVICE_URL',
    'LOG_LEVEL'
  ];

  const missingVars: string[] = [];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    }
  }

  if (missingVars.length > 0) {
    logger.error('Missing required environment variables', { missingVars });
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  return {
    PORT: process.env.PORT!,
    NODE_ENV: process.env.NODE_ENV!,
    MONGODB_URI: process.env.MONGODB_URI!,
    MONGODB_DB_NAME: process.env.MONGODB_DB_NAME!,
    CORE_SERVICE_URL: process.env.CORE_SERVICE_URL!,
    LOG_LEVEL: process.env.LOG_LEVEL!,
  };
}

export const env = validateEnv();

logger.info('POS service environment variables loaded successfully', {
  NODE_ENV: env.NODE_ENV,
  PORT: env.PORT,
  LOG_LEVEL: env.LOG_LEVEL,
  MONGODB_DB_NAME: env.MONGODB_DB_NAME,
}); 