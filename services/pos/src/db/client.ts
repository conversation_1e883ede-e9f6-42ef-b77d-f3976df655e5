import { MongoClient, Db } from 'mongodb';

let client: MongoClient;
let db: Db;

export async function connectToDatabase(): Promise<void> {
  const mongoUrl = process.env.MONGODB_URI || 'mongodb://localhost:27017';
  const dbName = process.env.MONGODB_DB_NAME || 'elypos';

  try {
    client = new MongoClient(mongoUrl);
    await client.connect();
    db = client.db(dbName);
    console.log('✅ Connected to MongoDB (POS Service)');
  } catch (error) {
    console.error('❌ Failed to connect to MongoDB (POS Service):', error);
    throw error;
  }
}

export async function disconnectFromDatabase(): Promise<void> {
  if (client) {
    await client.close();
    console.log('✅ Disconnected from MongoDB (POS Service)');
  }
}

export { db }; 