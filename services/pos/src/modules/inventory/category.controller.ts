import { Elysia, t } from 'elysia';
import { CategoryService } from './category.service';

const categoryService = new CategoryService();

export const categoryController = new Elysia({ prefix: '/categories' })
  .get('/', async ({ query }) => {
    try {
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching categories for company:', companyId);
      
      const categories = await categoryService.getCategories(companyId);
      
      return {
        success: true,
        data: categories
      };
    } catch (err) {
      console.error('Failed to fetch categories:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch categories' 
      };
    }
  }, {
    query: t.Object({
      companyId: t.String()
    })
  })

  .get('/tree', async ({ query }) => {
    try {
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching category tree for company:', companyId);
      
      const categoryTree = await categoryService.getCategoryTree(companyId);
      
      return {
        success: true,
        data: categoryTree
      };
    } catch (err) {
      console.error('Failed to fetch category tree:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch category tree' 
      };
    }
  }, {
    query: t.Object({
      companyId: t.String()
    })
  })

  .post('/', async ({ body }) => {
    try {
      const { companyId, name, description, parentId, icon, color } = body;
      
      if (!companyId || !name) {
        return { success: false, error: 'Company ID and name are required' };
      }

      console.log('Creating category:', name, 'for company:', companyId);
      
      const newCategory = await categoryService.createCategory(companyId, {
        name,
        description,
        parentId,
        icon,
        color
      });
      
      return {
        success: true,
        data: newCategory
      };
    } catch (err) {
      console.error('Failed to create category:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create category' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      name: t.String(),
      description: t.Optional(t.String()),
      parentId: t.Optional(t.String()),
      icon: t.Optional(t.String()),
      color: t.Optional(t.String())
    })
  })

  .put('/:categoryId', async ({ params, body }) => {
    try {
      const { categoryId } = params;
      const { companyId, name, description, icon, color } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Updating category:', categoryId, 'for company:', companyId);
      
      const updatedCategory = await categoryService.updateCategory(companyId, categoryId, {
        name,
        description,
        icon,
        color
      });
      
      return {
        success: true,
        data: updatedCategory
      };
    } catch (err) {
      console.error('Failed to update category:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update category' 
      };
    }
  }, {
    params: t.Object({
      categoryId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      name: t.Optional(t.String()),
      description: t.Optional(t.String()),
      icon: t.Optional(t.String()),
      color: t.Optional(t.String())
    })
  })

  .delete('/:categoryId', async ({ params, query }) => {
    try {
      const { categoryId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Deleting category:', categoryId, 'for company:', companyId);
      
      await categoryService.deleteCategory(companyId, categoryId);
      
      return {
        success: true,
        message: 'Category deleted successfully'
      };
    } catch (err) {
      console.error('Failed to delete category:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to delete category' 
      };
    }
  }, {
    params: t.Object({
      categoryId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  .get('/:categoryId', async ({ params, query }) => {
    try {
      const { categoryId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching category:', categoryId, 'for company:', companyId);
      
      const category = await categoryService.getCategoryById(companyId, categoryId);
      
      if (!category) {
        return { success: false, error: 'Category not found' };
      }
      
      return {
        success: true,
        data: category
      };
    } catch (err) {
      console.error('Failed to fetch category:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch category' 
      };
    }
  }, {
    params: t.Object({
      categoryId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  .post('/:categoryId/move', async ({ params, body }) => {
    try {
      const { categoryId } = params;
      const { companyId, targetParentId, newOrder } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Moving category:', categoryId, 'for company:', companyId);
      
      const movedCategory = await categoryService.moveCategory(companyId, categoryId, {
        targetParentId,
        newOrder: newOrder ?? 1
      });
      
      return {
        success: true,
        data: movedCategory
      };
    } catch (err) {
      console.error('Failed to move category:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to move category' 
      };
    }
  }, {
    params: t.Object({
      categoryId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      targetParentId: t.Optional(t.String()),
      newOrder: t.Optional(t.Number())
    })
  })

  .get('/level/:level', async ({ params, query }) => {
    try {
      const { level } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching categories at level:', level, 'for company:', companyId);
      
      const levelNum = parseInt(level, 10);
      if (isNaN(levelNum)) {
        return { success: false, error: 'Invalid level parameter' };
      }
      
      const categories = await categoryService.getCategoriesByLevel(companyId, levelNum);
      
      return {
        success: true,
        data: categories
      };
    } catch (err) {
      console.error('Failed to fetch categories by level:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch categories by level' 
      };
    }
  }, {
    params: t.Object({
      level: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  }); 