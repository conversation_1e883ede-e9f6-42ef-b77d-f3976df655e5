import { db } from '../../db/client';
import type { 
  Category, 
  CategoryTree, 
  CreateCategoryRequest, 
  UpdateCategoryRequest,
  CategoryMoveRequest 
} from '../../../../../shared/types/pos';

const CATEGORIES_COLLECTION = 'pos_categories';

// Generate a unique string ID
function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export class CategoryService {
  private get categoriesCollection() {
    return db.collection(CATEGORIES_COLLECTION);
  }

  async createCategory(companyId: string, data: CreateCategoryRequest): Promise<Category> {
    const slug = this.generateSlug(data.name);
    
    // Check if slug already exists
    const existingCategory = await this.categoriesCollection.findOne({
      companyId,
      slug,
      isActive: true
    } as any);

    if (existingCategory) {
      throw new Error('Category with this name already exists');
    }

    // Determine level based on parent
    let level = 0;
    let path: string[] = [data.name];
    
    if (data.parentId) {
      const parent = await this.getCategoryById(companyId, data.parentId);
      if (!parent) {
        throw new Error('Parent category not found');
      }
      level = parent.level + 1;
      path = [...(parent.path || []), data.name];
    }

    // Get next order if not specified
    const order = data.order ?? await this.getNextOrder(companyId, data.parentId);

    const _id = generateId();
    const category: Category = {
      _id,
      companyId,
      name: data.name,
      description: data.description,
      slug,
      parentId: data.parentId,
      level,
      order,
      icon: data.icon,
      color: data.color,
      isActive: true,
      path,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.categoriesCollection.insertOne(category as any);
    return category;
  }

  async updateCategory(companyId: string, categoryId: string, data: UpdateCategoryRequest): Promise<Category> {
    const category = await this.getCategoryById(companyId, categoryId);
    if (!category) {
      throw new Error('Category not found');
    }

    const updateData: any = {
      ...data,
      updatedAt: new Date()
    };

    // Handle slug update if name changed
    if (data.name && data.name !== category.name) {
      updateData.slug = this.generateSlug(data.name);
      
      // Check for slug conflicts
      const existingCategory = await this.categoriesCollection.findOne({
        companyId,
        slug: updateData.slug,
        _id: { $ne: categoryId },
        isActive: true
      } as any);

      if (existingCategory) {
        throw new Error('Category with this name already exists');
      }
    }

    // Handle parent change
    if (data.parentId !== undefined && data.parentId !== category.parentId) {
      if (data.parentId) {
        const parent = await this.getCategoryById(companyId, data.parentId);
        if (!parent) {
          throw new Error('Parent category not found');
        }
        
        // Prevent circular reference
        if (await this.wouldCreateCircularReference(categoryId, data.parentId)) {
          throw new Error('Cannot move category: would create circular reference');
        }
        
        updateData.level = parent.level + 1;
        updateData.path = [...(parent.path || []), data.name || category.name];
      } else {
        updateData.level = 0;
        updateData.path = [data.name || category.name];
      }

      // Update all descendants
      await this.updateDescendantPaths(companyId, categoryId, updateData.path, updateData.level);
    }

    await this.categoriesCollection.updateOne(
      { _id: categoryId, companyId } as any,
      { $set: updateData }
    );

    return this.getCategoryById(companyId, categoryId) as Promise<Category>;
  }

  async deleteCategory(companyId: string, categoryId: string): Promise<boolean> {
    const category = await this.getCategoryById(companyId, categoryId);
    if (!category) {
      throw new Error('Category not found');
    }

    // Check if category has children
    const hasChildren = await this.categoriesCollection.countDocuments({
      companyId,
      parentId: categoryId,
      isActive: true
    } as any) > 0;

    if (hasChildren) {
      throw new Error('Cannot delete category with subcategories');
    }

    // Check if category has products
    const hasProducts = await db.collection('pos_products').countDocuments({
      companyId,
      categoryId,
      isActive: true
    } as any) > 0;

    if (hasProducts) {
      throw new Error('Cannot delete category with products');
    }

    // Soft delete
    await this.categoriesCollection.updateOne(
      { _id: categoryId, companyId } as any,
      { 
        $set: { 
          isActive: false, 
          updatedAt: new Date() 
        } 
      }
    );

    return true;
  }

  async getCategoryById(companyId: string, categoryId: string): Promise<Category | null> {
    const result = await this.categoriesCollection.findOne({
      _id: categoryId,
      companyId,
      isActive: true
    } as any);

    return result ? { ...result, _id: result._id.toString() } as unknown as Category : null;
  }

  async getCategoryTree(companyId: string): Promise<CategoryTree> {
    const categories = await this.categoriesCollection
      .find({ companyId, isActive: true } as any)
      .sort({ level: 1, order: 1, name: 1 })
      .toArray();

    const categoriesWithIds = categories.map(cat => ({
      ...cat,
      _id: cat._id.toString()
    })) as unknown as Category[];

    // Build tree structure
    const tree = this.buildCategoryTree(categoriesWithIds);
    
    // Add product counts
    await this.addProductCounts(companyId, tree);

    const maxDepth = Math.max(...categoriesWithIds.map(c => c.level), 0);

    return {
      categories: tree,
      totalCount: categoriesWithIds.length,
      maxDepth
    };
  }

  async moveCategory(companyId: string, categoryId: string, data: CategoryMoveRequest): Promise<Category> {
    const category = await this.getCategoryById(companyId, categoryId);
    if (!category) {
      throw new Error('Category not found');
    }

    // Prevent circular reference
    if (data.targetParentId && await this.wouldCreateCircularReference(categoryId, data.targetParentId)) {
      throw new Error('Cannot move category: would create circular reference');
    }

    // Update category
    const updateData: UpdateCategoryRequest = {
      parentId: data.targetParentId,
      order: data.newOrder
    };

    return this.updateCategory(companyId, categoryId, updateData);
  }

  async getCategoriesByLevel(companyId: string, level: number): Promise<Category[]> {
    const categories = await this.categoriesCollection
      .find({ companyId, level, isActive: true } as any)
      .sort({ order: 1, name: 1 })
      .toArray();

    return categories.map(cat => ({
      ...cat,
      _id: cat._id.toString()
    })) as unknown as Category[];
  }

  async getCategories(companyId: string): Promise<Category[]> {
    const categories = await this.categoriesCollection
      .find({ companyId, isActive: true } as any)
      .sort({ level: 1, order: 1, name: 1 })
      .toArray();

    return categories.map(cat => ({
      ...cat,
      _id: cat._id.toString()
    })) as unknown as Category[];
  }

  private buildCategoryTree(categories: Category[]): Category[] {
    const categoryMap = new Map<string, Category>();
    const tree: Category[] = [];

    // First pass: create map
    categories.forEach(category => {
      categoryMap.set(category._id, { ...category, children: [] });
    });

    // Second pass: build tree
    categories.forEach(category => {
      const node = categoryMap.get(category._id)!;
      
      if (category.parentId && categoryMap.has(category.parentId)) {
        const parent = categoryMap.get(category.parentId)!;
        parent.children!.push(node);
      } else {
        tree.push(node);
      }
    });

    return tree;
  }

  private async addProductCounts(companyId: string, categories: Category[]): Promise<void> {
    for (const category of categories) {
      const count = await db.collection('pos_products').countDocuments({
        companyId,
        categoryId: category._id,
        isActive: true
      } as any);
      category.productCount = count;

      if (category.children) {
        await this.addProductCounts(companyId, category.children);
      }
    }
  }

  private async wouldCreateCircularReference(categoryId: string, targetParentId: string): Promise<boolean> {
    let currentId = targetParentId;
    
    while (currentId) {
      if (currentId === categoryId) {
        return true;
      }
      
      const parent = await this.categoriesCollection.findOne({
        _id: currentId
      } as any);
      
      currentId = parent?.parentId;
    }
    
    return false;
  }

  private async updateDescendantPaths(companyId: string, categoryId: string, parentPath: string[], parentLevel: number): Promise<void> {
    const descendants = await this.categoriesCollection
      .find({ companyId, parentId: categoryId, isActive: true } as any)
      .toArray();

    for (const descendant of descendants) {
      const newPath = [...parentPath, descendant.name];
      const newLevel = parentLevel + 1;

      await this.categoriesCollection.updateOne(
        { _id: descendant._id } as any,
        { 
          $set: { 
            path: newPath, 
            level: newLevel,
            updatedAt: new Date()
          } 
        }
      );

      // Recursively update descendants
      await this.updateDescendantPaths(companyId, descendant._id.toString(), newPath, newLevel);
    }
  }

  private async getNextOrder(companyId: string, parentId?: string): Promise<number> {
    const maxOrder = await this.categoriesCollection
      .findOne(
        { companyId, parentId: parentId || null, isActive: true } as any,
        { sort: { order: -1 } }
      );

    return (maxOrder?.order || 0) + 1;
  }

  private generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  }
} 