import { db } from '../../db/client';
import type { 
  PriceLevel, 
  CreatePriceLevelRequest, 
  UpdatePriceLevelRequest,
  ProductPrice,
  SetProductPriceRequest
} from '../../../../../shared/types/pos';

export class PriceLevelService {
  private generateId(): string {
    const characters = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 20; i++) {
      result += characters.charAt(Math.floor(Math.random() * characters.length));
    }
    return result;
  }

  private async getNextPriority(companyId: string): Promise<number> {
    const collection = db.collection('priceLevels');
    const maxPriority = await collection.findOne(
      { companyId },
      { sort: { priority: -1 } }
    );
    return maxPriority ? maxPriority.priority + 1 : 0;
  }

  async getPriceLevels(companyId: string): Promise<PriceLevel[]> {
    const collection = db.collection('priceLevels');
    const priceLevels = await collection
      .find({ companyId, isActive: true })
      .sort({ priority: 1, name: 1 })
      .toArray();

    // If no price levels exist, create default ones
    if (priceLevels.length === 0) {
      await this.createDefaultPriceLevels(companyId);
      return this.getPriceLevels(companyId);
    }

    return priceLevels.map(level => ({
      ...level,
      _id: level._id.toString()
    })) as PriceLevel[];
  }

  async createDefaultPriceLevels(companyId: string): Promise<void> {
    console.log(`Creating comprehensive default price levels for company: ${companyId}`);
    
    const defaultLevels: Omit<PriceLevel, '_id'>[] = [
      {
        companyId,
        name: 'Customer',
        description: 'Standard customer pricing for registered customers',
        code: 'CUSTOMER',
        priority: 0,
        discountPercentage: 0,
        color: '#3B82F6', // Blue
        isDefault: true,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId,
        name: 'Guest',
        description: 'Guest and walk-in customer pricing',
        code: 'GUEST',
        priority: 1,
        discountPercentage: 0,
        color: '#6B7280', // Gray
        isDefault: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId,
        name: 'Staff',
        description: 'Employee discount pricing for staff members',
        code: 'STAFF',
        priority: 2,
        discountPercentage: 15,
        color: '#10B981', // Emerald
        isDefault: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId,
        name: 'VIP Member',
        description: 'Premium customer pricing with exclusive discounts',
        code: 'VIP',
        priority: 3,
        discountPercentage: 10,
        color: '#F59E0B', // Amber
        isDefault: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        companyId,
        name: 'Wholesale',
        description: 'Bulk pricing for resellers and wholesale customers',
        code: 'WHOLESALE',
        priority: 4,
        discountPercentage: 25,
        color: '#8B5CF6', // Violet
        isDefault: false,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    const collection = db.collection('priceLevels');
    
    // Insert default price levels with generated IDs
    for (const level of defaultLevels) {
      const _id = this.generateId();
      await collection.insertOne({ _id: _id as any, ...level });
    }

    console.log(`Successfully created ${defaultLevels.length} comprehensive default price levels for company: ${companyId}`);
  }

  async getPriceLevelById(companyId: string, priceLevelId: string): Promise<PriceLevel | null> {
    const collection = db.collection('priceLevels');
    const priceLevel = await collection.findOne({
      _id: priceLevelId as any,
      companyId,
      isActive: true
    });

    if (!priceLevel) return null;

    return {
      ...priceLevel,
      _id: priceLevel._id.toString()
    } as PriceLevel;
  }

  async createPriceLevel(companyId: string, data: CreatePriceLevelRequest): Promise<PriceLevel> {
    const collection = db.collection('priceLevels');

    // Validate code uniqueness
    const existingCode = await collection.findOne({
      companyId,
      code: data.code.toUpperCase(),
      isActive: true
    });

    if (existingCode) {
      throw new Error(`Price level with code '${data.code}' already exists`);
    }

    // If this is set as default, unset other defaults
    if (data.isDefault) {
      await collection.updateMany(
        { companyId, isDefault: true },
        { $set: { isDefault: false } }
      );
    }

    // Auto-assign priority if not provided
    const priority = data.priority !== undefined 
      ? data.priority 
      : await this.getNextPriority(companyId);

    const priceLevel: Omit<PriceLevel, '_id'> = {
      companyId,
      name: data.name.trim(),
      description: data.description?.trim(),
      code: data.code.toUpperCase().trim(),
      priority: priority,
      discountPercentage: data.discountPercentage,
      color: data.color || '#3B82F6',
      isDefault: data.isDefault || false,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const _id = this.generateId();
    await collection.insertOne({ _id: _id as any, ...priceLevel });

    return {
      _id,
      ...priceLevel
    };
  }

  async updatePriceLevel(companyId: string, priceLevelId: string, data: UpdatePriceLevelRequest): Promise<PriceLevel> {
    const collection = db.collection('priceLevels');

    // Check if price level exists
    const existing = await this.getPriceLevelById(companyId, priceLevelId);
    if (!existing) {
      throw new Error('Price level not found');
    }

    // Validate code uniqueness if code is being updated
    if (data.code && data.code !== existing.code) {
      const existingCode = await collection.findOne({
        companyId,
        code: data.code.toUpperCase(),
        isActive: true,
        _id: { $ne: priceLevelId as any }
      });

      if (existingCode) {
        throw new Error(`Price level with code '${data.code}' already exists`);
      }
    }

    // If this is being set as default, unset other defaults
    if (data.isDefault && !existing.isDefault) {
      await collection.updateMany(
        { companyId, isDefault: true },
        { $set: { isDefault: false } }
      );
    }

    const updateData: any = {
      updatedAt: new Date()
    };

    // Only update provided fields
    if (data.name !== undefined) updateData.name = data.name.trim();
    if (data.description !== undefined) updateData.description = data.description?.trim();
    if (data.code !== undefined) updateData.code = data.code.toUpperCase().trim();
    if (data.priority !== undefined) updateData.priority = data.priority;
    if (data.discountPercentage !== undefined) updateData.discountPercentage = data.discountPercentage;
    if (data.color !== undefined) updateData.color = data.color;
    if (data.isDefault !== undefined) updateData.isDefault = data.isDefault;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    await collection.updateOne(
      { _id: priceLevelId as any, companyId },
      { $set: updateData }
    );

    return this.getPriceLevelById(companyId, priceLevelId) as Promise<PriceLevel>;
  }

  async deletePriceLevel(companyId: string, priceLevelId: string): Promise<void> {
    const collection = db.collection('priceLevels');
    const productPricesCollection = db.collection('productPrices');

    // Check if price level exists
    const existing = await this.getPriceLevelById(companyId, priceLevelId);
    if (!existing) {
      throw new Error('Price level not found');
    }

    // Check if it's the default price level
    if (existing.isDefault) {
      throw new Error('Cannot delete the default price level');
    }

    // Check if any products are using this price level
    const productsUsingLevel = await productPricesCollection.countDocuments({
      priceLevelId,
      isActive: true
    });

    if (productsUsingLevel > 0) {
      throw new Error(`Cannot delete price level. ${productsUsingLevel} products are using this price level.`);
    }

    // Soft delete the price level
    await collection.updateOne(
      { _id: priceLevelId as any, companyId },
      { 
        $set: { 
          isActive: false,
          updatedAt: new Date()
        }
      }
    );
  }

  async reorderPriceLevels(companyId: string, priceLevelIds: string[]): Promise<void> {
    const collection = db.collection('priceLevels');
    
    // Update priority based on array order
    const updatePromises = priceLevelIds.map((id, index) => 
      collection.updateOne(
        { _id: id as any, companyId },
        { 
          $set: { 
            priority: index,
            updatedAt: new Date()
          }
        }
      )
    );

    await Promise.all(updatePromises);
  }

  // Product Price Management
  async getProductPrices(companyId: string, productId: string): Promise<ProductPrice[]> {
    const collection = db.collection('productPrices');
    const prices = await collection
      .find({ productId, isActive: true })
      .toArray();

    return prices.map(price => ({
      ...price,
      _id: price._id?.toString()
    })) as unknown as ProductPrice[];
  }

  async setProductPrice(companyId: string, data: SetProductPriceRequest): Promise<ProductPrice> {
    const collection = db.collection('productPrices');

    // Check if price already exists for this product and price level
    const existing = await collection.findOne({
      productId: data.productId,
      priceLevelId: data.priceLevelId,
      isActive: true
    });

    const priceData = {
      productId: data.productId,
      priceLevelId: data.priceLevelId,
      priceKHR: data.priceKHR,
      priceUSD: data.priceUSD,
      isActive: true,
      effectiveDate: data.effectiveDate,
      expiryDate: data.expiryDate,
      updatedAt: new Date()
    };

    if (existing) {
      // Update existing price
      await collection.updateOne(
        { _id: existing._id },
        { $set: priceData }
      );
      
      return {
        ...priceData,
        createdAt: existing.createdAt
      } as ProductPrice;
    } else {
      // Create new price
      const newPrice = {
        ...priceData,
        createdAt: new Date()
      };

      await collection.insertOne(newPrice);
      return newPrice as ProductPrice;
    }
  }

  async removeProductPrice(companyId: string, productId: string, priceLevelId: string): Promise<void> {
    const collection = db.collection('productPrices');

    await collection.updateOne(
      { productId, priceLevelId },
      { 
        $set: { 
          isActive: false,
          updatedAt: new Date()
        }
      }
    );
  }

  async getDefaultPriceLevel(companyId: string): Promise<PriceLevel | null> {
    const collection = db.collection('priceLevels');
    const defaultLevel = await collection.findOne({
      companyId,
      isDefault: true,
      isActive: true
    });

    if (!defaultLevel) return null;

    return {
      ...defaultLevel,
      _id: defaultLevel._id.toString()
    } as PriceLevel;
  }

  async getProductPriceByLevel(productId: string, priceLevelId: string): Promise<ProductPrice | null> {
    const collection = db.collection('productPrices');
    const price = await collection.findOne({
      productId,
      priceLevelId,
      isActive: true,
      $and: [
        {
          $or: [
            { effectiveDate: { $exists: false } },
            { effectiveDate: { $lte: new Date() } }
          ]
        },
        {
          $or: [
            { expiryDate: { $exists: false } },
            { expiryDate: { $gte: new Date() } }
          ]
        }
      ]
    });

    if (!price) return null;

    return {
      ...price,
      _id: price._id?.toString()
    } as unknown as ProductPrice;
  }
} 