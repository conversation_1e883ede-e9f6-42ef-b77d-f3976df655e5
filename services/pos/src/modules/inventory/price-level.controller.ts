import { Elysia, t } from 'elysia';
import { PriceLevelService } from './price-level.service';

const priceLevelService = new PriceLevelService();

export const priceLevelController = new Elysia({ prefix: '/price-levels' })
  // Get all price levels for a company
  .get('/', async ({ query }) => {
    try {
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching price levels for company:', companyId);
      
      const priceLevels = await priceLevelService.getPriceLevels(companyId);
      
      return {
        success: true,
        data: priceLevels
      };
    } catch (err) {
      console.error('Failed to fetch price levels:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch price levels' 
      };
    }
  }, {
    query: t.Object({
      companyId: t.String()
    })
  })

  // Get a specific price level
  .get('/:priceLevelId', async ({ params, query }) => {
    try {
      const { priceLevelId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching price level:', priceLevelId, 'for company:', companyId);
      
      const priceLevel = await priceLevelService.getPriceLevelById(companyId, priceLevelId);
      
      if (!priceLevel) {
        return { success: false, error: 'Price level not found' };
      }
      
      return {
        success: true,
        data: priceLevel
      };
    } catch (err) {
      console.error('Failed to fetch price level:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch price level' 
      };
    }
  }, {
    params: t.Object({
      priceLevelId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Create a new price level
  .post('/', async ({ body }) => {
    try {
      const { companyId, ...data } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Creating price level:', data.name, 'for company:', companyId);
      
      const newPriceLevel = await priceLevelService.createPriceLevel(companyId, data);
      
      return {
        success: true,
        data: newPriceLevel
      };
    } catch (err) {
      console.error('Failed to create price level:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create price level' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      name: t.String(),
      description: t.Optional(t.String()),
      code: t.String(),
      priority: t.Optional(t.Number()),
      discountPercentage: t.Optional(t.Number()),
      color: t.Optional(t.String()),
      isDefault: t.Optional(t.Boolean())
    })
  })

  // Update a price level
  .put('/:priceLevelId', async ({ params, body }) => {
    try {
      const { priceLevelId } = params;
      const { companyId, ...data } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Updating price level:', priceLevelId, 'for company:', companyId);
      
      const updatedPriceLevel = await priceLevelService.updatePriceLevel(companyId, priceLevelId, data);
      
      return {
        success: true,
        data: updatedPriceLevel
      };
    } catch (err) {
      console.error('Failed to update price level:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update price level' 
      };
    }
  }, {
    params: t.Object({
      priceLevelId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      name: t.Optional(t.String()),
      description: t.Optional(t.String()),
      code: t.Optional(t.String()),
      priority: t.Optional(t.Number()),
      discountPercentage: t.Optional(t.Number()),
      color: t.Optional(t.String()),
      isDefault: t.Optional(t.Boolean()),
      isActive: t.Optional(t.Boolean())
    })
  })

  // Delete a price level
  .delete('/:priceLevelId', async ({ params, query }) => {
    try {
      const { priceLevelId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Deleting price level:', priceLevelId, 'for company:', companyId);
      
      await priceLevelService.deletePriceLevel(companyId, priceLevelId);
      
      return {
        success: true,
        message: 'Price level deleted successfully'
      };
    } catch (err) {
      console.error('Failed to delete price level:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to delete price level' 
      };
    }
  }, {
    params: t.Object({
      priceLevelId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Reorder price levels
  .post('/reorder', async ({ body }) => {
    try {
      const { companyId, priceLevelIds } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Reordering price levels for company:', companyId);
      
      await priceLevelService.reorderPriceLevels(companyId, priceLevelIds);
      
      return {
        success: true,
        message: 'Price levels reordered successfully'
      };
    } catch (err) {
      console.error('Failed to reorder price levels:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to reorder price levels' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      priceLevelIds: t.Array(t.String())
    })
  })

  // Initialize default price levels for a company
  .post('/initialize-defaults', async ({ body }) => {
    try {
      const { companyId } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Initializing default price levels for company:', companyId);
      
      await priceLevelService.createDefaultPriceLevels(companyId);
      const newPriceLevels = await priceLevelService.getPriceLevels(companyId);
      
      return {
        success: true,
        data: newPriceLevels,
        message: 'Complete price level suite (Customer, Guest, Staff, VIP Member, Wholesale) created successfully'
      };
    } catch (err) {
      console.error('Failed to initialize default price levels:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to initialize default price levels' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String()
    })
  })

  // Get default price level
  .get('/default/info', async ({ query }) => {
    try {
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching default price level for company:', companyId);
      
      const defaultPriceLevel = await priceLevelService.getDefaultPriceLevel(companyId);
      
      return {
        success: true,
        data: defaultPriceLevel
      };
    } catch (err) {
      console.error('Failed to fetch default price level:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch default price level' 
      };
    }
  }, {
    query: t.Object({
      companyId: t.String()
    })
  })

  // Product Price Management Routes

  // Get all prices for a product
  .get('/products/:productId/prices', async ({ params, query }) => {
    try {
      const { productId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching prices for product:', productId, 'company:', companyId);
      
      const prices = await priceLevelService.getProductPrices(companyId, productId);
      
      return {
        success: true,
        data: prices
      };
    } catch (err) {
      console.error('Failed to fetch product prices:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch product prices' 
      };
    }
  }, {
    params: t.Object({
      productId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Set price for a product at a specific level
  .post('/products/prices', async ({ body }) => {
    try {
      const { companyId, ...data } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Setting product price for company:', companyId);
      
      const productPrice = await priceLevelService.setProductPrice(companyId, data);
      
      return {
        success: true,
        data: productPrice
      };
    } catch (err) {
      console.error('Failed to set product price:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to set product price' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      productId: t.String(),
      priceLevelId: t.String(),
      priceKHR: t.Number(),
      priceUSD: t.Number(),
      effectiveDate: t.Optional(t.Date()),
      expiryDate: t.Optional(t.Date())
    })
  })

  // Remove price for a product at a specific level
  .delete('/products/:productId/prices/:priceLevelId', async ({ params, query }) => {
    try {
      const { productId, priceLevelId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Removing product price:', productId, priceLevelId, 'for company:', companyId);
      
      await priceLevelService.removeProductPrice(companyId, productId, priceLevelId);
      
      return {
        success: true,
        message: 'Product price removed successfully'
      };
    } catch (err) {
      console.error('Failed to remove product price:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to remove product price' 
      };
    }
  }, {
    params: t.Object({
      productId: t.String(),
      priceLevelId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Get specific price for a product at a level
  .get('/products/:productId/prices/:priceLevelId', async ({ params, query }) => {
    try {
      const { productId, priceLevelId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching product price:', productId, priceLevelId, 'for company:', companyId);
      
      const price = await priceLevelService.getProductPriceByLevel(productId, priceLevelId);
      
      return {
        success: true,
        data: price
      };
    } catch (err) {
      console.error('Failed to fetch product price:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch product price' 
      };
    }
  }, {
    params: t.Object({
      productId: t.String(),
      priceLevelId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  }); 