import { Elysia, t } from 'elysia';
import { OrderService } from './order.service';

const orderService = new OrderService();

export const orderController = new Elysia({ prefix: '/orders' })
  // Get orders by table
  .get('/table/:tableId', async ({ params, query }) => {
    try {
      const { tableId } = params;
      const { companyId, activeOnly } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching orders for table:', tableId, 'company:', companyId, 'activeOnly:', activeOnly);
      
      const orders = await orderService.getOrdersByTable(companyId, tableId, activeOnly === 'true');
      
      return {
        success: true,
        data: orders
      };
    } catch (err) {
      console.error('Failed to fetch table orders:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch table orders' 
      };
    }
  }, {
    params: t.Object({
      tableId: t.String()
    }),
    query: t.Object({
      companyId: t.String(),
      activeOnly: t.Optional(t.String())
    })
  })

  // Get all orders for company
  .get('/', async ({ query }) => {
    try {
      const { companyId, status, limit, offset } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching orders for company:', companyId);
      
      const orders = await orderService.getOrders(companyId, {
        status,
        limit: limit ? parseInt(limit) : undefined,
        offset: offset ? parseInt(offset) : undefined
      });
      
      return {
        success: true,
        data: orders
      };
    } catch (err) {
      console.error('Failed to fetch orders:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch orders' 
      };
    }
  }, {
    query: t.Object({
      companyId: t.String(),
      status: t.Optional(t.String()),
      limit: t.Optional(t.String()),
      offset: t.Optional(t.String())
    })
  })

  // Create new order
  .post('/', async ({ body }) => {
    try {
      const { companyId, tableId, orderType, items, customerInfo } = body;
      
      if (!companyId || !tableId || !items || items.length === 0) {
        return { success: false, error: 'Company ID, table ID, and items are required' };
      }

      console.log('Creating new order for table:', tableId, 'company:', companyId);
      
      const order = await orderService.createOrder(companyId, {
        tableId,
        orderType,
        items,
        customerInfo
      });
      
      return {
        success: true,
        data: order,
        message: 'Order created successfully'
      };
    } catch (err) {
      console.error('Failed to create order:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create order' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      tableId: t.String(),
      orderType: t.Optional(t.Union([
        t.Literal('standard'), 
        t.Literal('bbq_unlimited'), 
        t.Literal('takeout')
      ])),
      items: t.Array(t.Object({
        productId: t.String(),
        name: t.String(),
        quantity: t.Number(),
        price: t.Number(),
        notes: t.Optional(t.String())
      })),
      customerInfo: t.Optional(t.Object({
        name: t.Optional(t.String()),
        phone: t.Optional(t.String()),
        email: t.Optional(t.String())
      }))
    })
  })

  // Update order status
  .patch('/:orderId/status', async ({ params, body }) => {
    try {
      const { orderId } = params;
      const { companyId, status } = body;
      
      if (!companyId || !status) {
        return { success: false, error: 'Company ID and status are required' };
      }

      console.log('Updating order status:', orderId, 'to:', status);
      
      const order = await orderService.updateOrderStatus(companyId, orderId, status);
      
      if (!order) {
        return { success: false, error: 'Order not found' };
      }
      
      return {
        success: true,
        data: order,
        message: 'Order status updated successfully'
      };
    } catch (err) {
      console.error('Failed to update order status:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update order status' 
      };
    }
  }, {
    params: t.Object({
      orderId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      status: t.Union([
        t.Literal('pending'),
        t.Literal('confirmed'),
        t.Literal('preparing'),
        t.Literal('ready'),
        t.Literal('served'),
        t.Literal('cancelled')
      ])
    })
  })

  // Get order by ID
  .get('/:orderId', async ({ params, query }) => {
    try {
      const { orderId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching order:', orderId, 'company:', companyId);
      
      const order = await orderService.getOrderById(companyId, orderId);
      
      if (!order) {
        return { success: false, error: 'Order not found' };
      }
      
      return {
        success: true,
        data: order
      };
    } catch (err) {
      console.error('Failed to fetch order:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch order' 
      };
    }
  }, {
    params: t.Object({
      orderId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  }); 