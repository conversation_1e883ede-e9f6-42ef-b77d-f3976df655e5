import { Elysia, t } from 'elysia';
import { QRMenuService } from './qr-menu.service';

const qrMenuService = new QRMenuService();

export const qrMenuController = new Elysia({ prefix: '/qr-menu' })
  // Create QR menu for a table
  .post('/', async ({ body }) => {
    try {
      const { companyId, tableId, tableNumber, menuType } = body;
      
      if (!companyId || !tableId || !tableNumber) {
        return { success: false, error: 'Company ID, table ID, and table number are required' };
      }

      console.log('Creating QR menu for table:', tableNumber, 'company:', companyId);
      
      const qrMenu = await qrMenuService.createQRMenu(companyId, tableId, tableNumber, menuType);
      
      return {
        success: true,
        data: qrMenu
      };
    } catch (err) {
      console.error('Failed to create QR menu:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create QR menu' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      tableId: t.String(),
      tableNumber: t.String(),
      menuType: t.Optional(t.Union([
        t.Literal('standard'), 
        t.Literal('bbq_unlimited'), 
        t.Literal('special_event')
      ]))
    })
  })

  // Get QR menu by table
  .get('/table/:tableId', async ({ params, query }) => {
    try {
      const { tableId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching QR menu for table:', tableId, 'company:', companyId);
      
      const qrMenu = await qrMenuService.getQRMenuByTable(companyId, tableId);
      
      if (!qrMenu) {
        return { success: false, error: 'QR menu not found for this table' };
      }
      
      return {
        success: true,
        data: qrMenu
      };
    } catch (err) {
      console.error('Failed to fetch QR menu:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch QR menu' 
      };
    }
  }, {
    params: t.Object({
      tableId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Scan QR menu (track analytics)
  .post('/:qrMenuId/scan', async ({ params }) => {
    try {
      const { qrMenuId } = params;
      
      console.log('QR menu scanned:', qrMenuId);
      
      const qrMenu = await qrMenuService.scanQRMenu(qrMenuId);
      
      if (!qrMenu) {
        return { success: false, error: 'QR menu not found' };
      }
      
      return {
        success: true,
        data: qrMenu
      };
    } catch (err) {
      console.error('Failed to scan QR menu:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to scan QR menu' 
      };
    }
  }, {
    params: t.Object({
      qrMenuId: t.String()
    })
  })

  // Start BBQ unlimited session
  .post('/:qrMenuId/bbq/start', async ({ params, body }) => {
    try {
      const { qrMenuId } = params;
      const { companyId, partySize } = body;
      
      if (!companyId || !partySize) {
        return { success: false, error: 'Company ID and party size are required' };
      }

      console.log('Starting BBQ session for QR menu:', qrMenuId, 'party size:', partySize);
      
      const bbqOrder = await qrMenuService.startBBQSession(companyId, qrMenuId, partySize);
      
      return {
        success: true,
        data: bbqOrder,
        message: `BBQ unlimited session started for ${partySize} people`
      };
    } catch (err) {
      console.error('Failed to start BBQ session:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to start BBQ session' 
      };
    }
  }, {
    params: t.Object({
      qrMenuId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      partySize: t.Number()
    })
  })

  // Add BBQ order to existing session
  .post('/bbq/:sessionId/order', async ({ params, body }) => {
    try {
      const { sessionId } = params;
      const { companyId, items } = body;
      
      if (!companyId || !items || items.length === 0) {
        return { success: false, error: 'Company ID and items are required' };
      }

      console.log('Adding BBQ order to session:', sessionId, 'items:', items.length);
      
      const bbqOrder = await qrMenuService.addBBQOrder(companyId, sessionId, items);
      
      return {
        success: true,
        data: bbqOrder,
        message: 'BBQ order added successfully'
      };
    } catch (err) {
      console.error('Failed to add BBQ order:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to add BBQ order' 
      };
    }
  }, {
    params: t.Object({
      sessionId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      items: t.Array(t.Object({
        productId: t.String(),
        name: t.String(),
        quantity: t.Number(),
        price: t.Number(),
        notes: t.Optional(t.String())
      }))
    })
  })

  // Create standard order
  .post('/:qrMenuId/order', async ({ params, body }) => {
    try {
      const { qrMenuId } = params;
      const { companyId, items, customerInfo } = body;
      
      if (!companyId || !items || items.length === 0) {
        return { success: false, error: 'Company ID and items are required' };
      }

      console.log('Creating standard order for QR menu:', qrMenuId, 'items:', items.length);
      
      const order = await qrMenuService.createStandardOrder(companyId, qrMenuId, items, customerInfo);
      
      return {
        success: true,
        data: order,
        message: 'Order created successfully'
      };
    } catch (err) {
      console.error('Failed to create order:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create order' 
      };
    }
  }, {
    params: t.Object({
      qrMenuId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      items: t.Array(t.Object({
        productId: t.String(),
        name: t.String(),
        quantity: t.Number(),
        price: t.Number(),
        notes: t.Optional(t.String())
      })),
      customerInfo: t.Optional(t.Object({
        name: t.Optional(t.String()),
        phone: t.Optional(t.String()),
        email: t.Optional(t.String()),
        specialRequests: t.Optional(t.String())
      }))
    })
  }); 