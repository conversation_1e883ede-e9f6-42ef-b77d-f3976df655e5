import { db } from '../../db/client';

// Generate a unique string ID
function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export interface QRMenu {
  _id: string;
  companyId: string;
  tableId: string;
  tableNumber: string;
  menuType: 'standard' | 'bbq_unlimited' | 'special_event';
  qrCode: string;
  isActive: boolean;
  sessionId?: string;
  activeOrder?: {
    orderId: string;
    startTime: Date;
    endTime?: Date;
    totalAmount: number;
    itemCount: number;
  };
  bbqMode?: {
    isUnlimitedMode: boolean;
    timeLimit: number; // minutes
    pricePerPerson: number;
    maxOrdersPerSession: number;
    allowedCategories: string[];
  };
  customization: {
    theme: 'light' | 'dark' | 'cambodian';
    language: 'en' | 'km';
    showPrices: boolean;
    showImages: boolean;
    allowSpecialRequests: boolean;
  };
  analytics: {
    totalScans: number;
    uniqueCustomers: number;
    averageOrderValue: number;
    lastScannedAt?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface QRMenuOrder {
  _id: string;
  companyId: string;
  qrMenuId: string;
  tableId: string;
  sessionId: string;
  orderType: 'standard' | 'bbq_unlimited';
  customerInfo?: {
    name?: string;
    phone?: string;
    email?: string;
    specialRequests?: string;
  };
  items: Array<{
    productId: string;
    name: string;
    quantity: number;
    price: number;
    notes?: string;
    isUnlimitedItem?: boolean;
  }>;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'cancelled';
  bbqSession?: {
    startTime: Date;
    endTime: Date;
    partySize: number;
    timeRemaining: number;
    orderCount: number;
  };
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
}

const QR_MENUS_COLLECTION = 'restaurant_qr_menus';
const QR_ORDERS_COLLECTION = 'restaurant_qr_orders';

export class QRMenuService {
  private get qrMenusCollection() {
    return db.collection(QR_MENUS_COLLECTION);
  }

  private get qrOrdersCollection() {
    return db.collection(QR_ORDERS_COLLECTION);
  }

  // QR Menu Management
  async createQRMenu(companyId: string, tableId: string, tableNumber: string, menuType: QRMenu['menuType'] = 'standard'): Promise<QRMenu> {
    const _id = generateId();
    const qrCode = this.generateQRCode(companyId, tableId, _id);
    
    const qrMenu: QRMenu = {
      _id,
      companyId,
      tableId,
      tableNumber,
      menuType,
      qrCode,
      isActive: true,
      customization: {
        theme: 'light',
        language: 'en',
        showPrices: true,
        showImages: true,
        allowSpecialRequests: true
      },
      analytics: {
        totalScans: 0,
        uniqueCustomers: 0,
        averageOrderValue: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Set BBQ mode defaults if needed
    if (menuType === 'bbq_unlimited') {
      qrMenu.bbqMode = {
        isUnlimitedMode: true,
        timeLimit: 90, // 90 minutes
        pricePerPerson: 25.00, // $25 per person
        maxOrdersPerSession: 20,
        allowedCategories: ['bbq_meat', 'vegetables', 'drinks', 'side_dishes']
      };
    }

    await this.qrMenusCollection.insertOne(qrMenu as any);
    console.log('QR Menu created successfully', { qrMenuId: qrMenu._id, tableNumber, companyId });
    return qrMenu;
  }

  async getQRMenu(companyId: string, qrMenuId: string): Promise<QRMenu | null> {
    const qrMenu = await this.qrMenusCollection.findOne({ _id: qrMenuId, companyId } as any);
    return qrMenu ? { ...qrMenu, _id: qrMenu._id.toString() } as unknown as QRMenu : null;
  }

  async getQRMenuByTable(companyId: string, tableId: string): Promise<QRMenu | null> {
    const qrMenu = await this.qrMenusCollection.findOne({ 
      companyId, 
      tableId, 
      isActive: true 
    } as any);
    return qrMenu ? { ...qrMenu, _id: qrMenu._id.toString() } as unknown as QRMenu : null;
  }

  async scanQRMenu(qrMenuId: string): Promise<QRMenu | null> {
    const result = await this.qrMenusCollection.findOneAndUpdate(
      { _id: qrMenuId } as any,
      { 
        $inc: { 'analytics.totalScans': 1 },
        $set: { 
          'analytics.lastScannedAt': new Date(),
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    console.log('QR Menu scanned', { qrMenuId });
    return result ? { ...result, _id: result._id.toString() } as unknown as QRMenu : null;
  }

  // BBQ Unlimited Mode
  async startBBQSession(companyId: string, qrMenuId: string, partySize: number): Promise<QRMenuOrder> {
    const qrMenu = await this.getQRMenu(companyId, qrMenuId);
    if (!qrMenu || qrMenu.menuType !== 'bbq_unlimited') {
      throw new Error('QR Menu not found or not in BBQ unlimited mode');
    }

    const sessionId = generateId();
    const _id = generateId();
    
    const bbqOrder: QRMenuOrder = {
      _id,
      companyId,
      qrMenuId,
      tableId: qrMenu.tableId,
      sessionId,
      orderType: 'bbq_unlimited',
      items: [],
      status: 'pending',
      bbqSession: {
        startTime: new Date(),
        endTime: new Date(Date.now() + (qrMenu.bbqMode!.timeLimit * 60 * 1000)),
        partySize,
        timeRemaining: qrMenu.bbqMode!.timeLimit,
        orderCount: 0
      },
      totalAmount: qrMenu.bbqMode!.pricePerPerson * partySize,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.qrOrdersCollection.insertOne(bbqOrder as any);
    
    // Update QR menu with active session
    await this.qrMenusCollection.updateOne(
      { _id: qrMenuId } as any,
      { 
        $set: { 
          sessionId,
          activeOrder: {
            orderId: _id,
            startTime: bbqOrder.bbqSession!.startTime,
            totalAmount: bbqOrder.totalAmount,
            itemCount: 0
          },
          updatedAt: new Date()
        }
      }
    );

    console.log('BBQ session started', { orderId: _id, tableId: qrMenu.tableId, partySize });
    return bbqOrder;
  }

  async addBBQOrder(companyId: string, sessionId: string, items: QRMenuOrder['items']): Promise<QRMenuOrder | null> {
    const activeOrder = await this.qrOrdersCollection.findOne({
      companyId,
      sessionId,
      orderType: 'bbq_unlimited',
      status: { $in: ['pending', 'confirmed', 'preparing'] }
    } as any);

    if (!activeOrder) {
      throw new Error('No active BBQ session found');
    }

    // Check time limit
    const now = new Date();
    if (now > activeOrder.bbqSession.endTime) {
      throw new Error('BBQ session has expired');
    }

    // Check order limit
    if (activeOrder.bbqSession.orderCount >= 20) { // Max orders per session
      throw new Error('Maximum orders per session reached');
    }

    // Add new items to order
    const newOrderId = generateId();
    const newOrder: QRMenuOrder = {
      _id: newOrderId,
      companyId,
      qrMenuId: activeOrder.qrMenuId,
      tableId: activeOrder.tableId,
      sessionId,
      orderType: 'bbq_unlimited',
      items: items.map(item => ({ ...item, isUnlimitedItem: true })),
      status: 'pending',
      bbqSession: {
        ...activeOrder.bbqSession,
        orderCount: activeOrder.bbqSession.orderCount + 1,
        timeRemaining: Math.max(0, Math.floor((activeOrder.bbqSession.endTime.getTime() - now.getTime()) / (1000 * 60)))
      },
      totalAmount: 0, // No additional charge for unlimited BBQ items
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.qrOrdersCollection.insertOne(newOrder as any);
    console.log('BBQ order added', { orderId: newOrderId, sessionId, itemCount: items.length });
    
    return newOrder;
  }

  // Standard Menu Ordering
  async createStandardOrder(companyId: string, qrMenuId: string, items: QRMenuOrder['items'], customerInfo?: QRMenuOrder['customerInfo']): Promise<QRMenuOrder> {
    const qrMenu = await this.getQRMenu(companyId, qrMenuId);
    if (!qrMenu) {
      throw new Error('QR Menu not found');
    }

    const _id = generateId();
    const totalAmount = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    const order: QRMenuOrder = {
      _id,
      companyId,
      qrMenuId,
      tableId: qrMenu.tableId,
      sessionId: generateId(),
      orderType: 'standard',
      customerInfo,
      items,
      status: 'pending',
      totalAmount,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.qrOrdersCollection.insertOne(order as any);
    console.log('Standard order created', { orderId: _id, tableId: qrMenu.tableId, totalAmount });
    
    return order;
  }

  async updateOrderStatus(companyId: string, orderId: string, status: QRMenuOrder['status']): Promise<QRMenuOrder | null> {
    const result = await this.qrOrdersCollection.findOneAndUpdate(
      { _id: orderId, companyId } as any,
      { 
        $set: { 
          status,
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    console.log('Order status updated', { orderId, status });
    return result ? { ...result, _id: result._id.toString() } as unknown as QRMenuOrder : null;
  }

  async getTableOrders(companyId: string, tableId: string, activeOnly: boolean = false): Promise<QRMenuOrder[]> {
    const filter: any = { companyId, tableId };
    if (activeOnly) {
      filter.status = { $in: ['pending', 'confirmed', 'preparing', 'ready'] };
    }

    const orders = await this.qrOrdersCollection.find(filter)
      .sort({ createdAt: -1 })
      .toArray();
    
    return orders.map(order => ({ ...order, _id: order._id.toString() })) as unknown as QRMenuOrder[];
  }

  // Khmer Food Categories
  async createKhmerFoodCategories(companyId: string): Promise<void> {
    const khmerCategories = [
      { name: 'Soup & Curry', nameKhmer: 'ស៊ុប និង ការី', icon: 'bowl-hot' },
      { name: 'Rice & Noodles', nameKhmer: 'បាយ និង មី', icon: 'wheat' },
      { name: 'Grilled & BBQ', nameKhmer: 'អាំង និង បាប៊ីគ្យូ', icon: 'fire' },
      { name: 'Stir Fry', nameKhmer: 'ឆារ', icon: 'utensils' },
      { name: 'Salad & Appetizers', nameKhmer: 'សាឡាត និង ម្ហូបក្រុម', icon: 'leaf' },
      { name: 'Desserts', nameKhmer: 'បង្អែម', icon: 'cake' },
      { name: 'Beverages', nameKhmer: 'ភេសជ្ជៈ', icon: 'glass' }
    ];

    const categoriesCollection = db.collection('pos_categories');
    
    for (const category of khmerCategories) {
      const existingCategory = await categoriesCollection.findOne({
        companyId,
        name: category.name,
        isActive: true
      } as any);

      if (!existingCategory) {
        await categoriesCollection.insertOne({
          _id: generateId(),
          companyId,
          name: category.name,
          nameKhmer: category.nameKhmer,
          description: `Traditional Khmer ${category.name.toLowerCase()}`,
          slug: category.name.toLowerCase().replace(/[^a-z0-9]/g, '-'),
          parentId: null,
          level: 0,
          order: khmerCategories.indexOf(category) + 1,
          icon: category.icon,
          color: '#FF6B35',
          isActive: true,
          path: [category.name],
          createdAt: new Date(),
          updatedAt: new Date()
        } as any);
      }
    }

    console.log('Khmer food categories created', { companyId });
  }

  // QR Code Generation
  private generateQRCode(companyId: string, tableId: string, qrMenuId: string): string {
    // Generate QR code URL that points to the e-menu
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    return `${baseUrl}/menu/${companyId}/${tableId}?qr=${qrMenuId}`;
  }

  // Analytics
  async getQRMenuAnalytics(companyId: string, qrMenuId: string): Promise<any> {
    const orders = await this.qrOrdersCollection.find({ companyId, qrMenuId } as any).toArray();
    
    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    const ordersByType = orders.reduce((acc, order) => {
      acc[order.orderType] = (acc[order.orderType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const popularItems = orders
      .flatMap(order => order.items)
      .reduce((acc, item) => {
        acc[item.name] = (acc[item.name] || 0) + item.quantity;
        return acc;
      }, {} as Record<string, number>);

    return {
      totalOrders,
      totalRevenue,
      averageOrderValue,
      ordersByType,
      popularItems: Object.entries(popularItems)
        .sort(([,a], [,b]) => (b as number) - (a as number))
        .slice(0, 10)
        .map(([name, quantity]) => ({ name, quantity: quantity as number }))
    };
  }
} 