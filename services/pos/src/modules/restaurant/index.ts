import { Elysia } from 'elysia';
import { tableController } from './table.controller';
import { floorController } from './floor.controller';
import { qrMenuController } from './qr-menu.controller';
import { orderController } from './order.controller';

export const restaurantModule = new Elysia({ prefix: '/restaurant' })
  .use(tableController)
  .use(floorController)
  .use(qrMenuController)
  .use(orderController)
  .get('/health', () => {
    return {
      service: 'restaurant-module',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      features: [
        'Table Management',
        'Floor Management',
        'QR Code E-Menu',
        'BBQ Unlimited Mode',
        'Standard Ordering',
        'Khmer Food Categories',
        'Kitchen Display',
        'Order Tracking',
        'Analytics'
      ]
    };
  });

export * from './table.service';
export * from './qr-menu.service';
export * from './table.controller';
export * from './floor.controller';
export * from './qr-menu.controller'; 