export interface OrderItem {
  productId: string;
  name: string;
  quantity: number;
  price: number;
  notes?: string;
  isUnlimitedItem?: boolean;
}

export interface CustomerInfo {
  name?: string;
  phone?: string;
  email?: string;
}

export interface RestaurantOrder {
  _id: string;
  companyId: string;
  tableId: string;
  orderNumber: string;
  orderType: 'standard' | 'bbq_unlimited' | 'takeout';
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'cancelled';
  items: OrderItem[];
  totalAmount: number;
  customerInfo?: CustomerInfo;
  bbqSession?: {
    sessionId: string;
    partySize: number;
    timeRemaining: number;
    orderCount: number;
    startTime: Date;
    endTime: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  notes?: string;
}

export interface CreateOrderData {
  tableId: string;
  orderType?: 'standard' | 'bbq_unlimited' | 'takeout';
  items: OrderItem[];
  customerInfo?: CustomerInfo;
  notes?: string;
}

export interface OrderQueryOptions {
  status?: string;
  limit?: number;
  offset?: number;
}

export class OrderService {
  private orders: Map<string, RestaurantOrder> = new Map();

  private generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substr(2, 5).toUpperCase();
    return `ORD-${timestamp.slice(-6)}${random}`;
  }

  private generateId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  async getOrdersByTable(companyId: string, tableId: string, activeOnly: boolean = false): Promise<RestaurantOrder[]> {
    const allOrders = Array.from(this.orders.values())
      .filter(order => order.companyId === companyId && order.tableId === tableId);

    if (activeOnly) {
      return allOrders.filter(order => 
        ['pending', 'confirmed', 'preparing', 'ready'].includes(order.status)
      );
    }

    return allOrders.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async getOrders(companyId: string, options: OrderQueryOptions = {}): Promise<RestaurantOrder[]> {
    let orders = Array.from(this.orders.values())
      .filter(order => order.companyId === companyId);

    if (options.status) {
      orders = orders.filter(order => order.status === options.status);
    }

    orders.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    if (options.offset) {
      orders = orders.slice(options.offset);
    }

    if (options.limit) {
      orders = orders.slice(0, options.limit);
    }

    return orders;
  }

  async createOrder(companyId: string, orderData: CreateOrderData): Promise<RestaurantOrder> {
    const now = new Date();
    const totalAmount = orderData.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const orderId = this.generateId();

    const order: RestaurantOrder = {
      _id: orderId,
      companyId,
      tableId: orderData.tableId,
      orderNumber: this.generateOrderNumber(),
      orderType: orderData.orderType || 'standard',
      status: 'pending',
      items: orderData.items,
      totalAmount,
      customerInfo: orderData.customerInfo,
      createdAt: now,
      updatedAt: now,
      notes: orderData.notes
    };

    this.orders.set(orderId, order);
    return order;
  }

  async updateOrderStatus(companyId: string, orderId: string, status: RestaurantOrder['status']): Promise<RestaurantOrder | null> {
    const order = this.orders.get(orderId);
    
    if (!order || order.companyId !== companyId) {
      return null;
    }

    order.status = status;
    order.updatedAt = new Date();
    
    this.orders.set(orderId, order);
    return order;
  }

  async getOrderById(companyId: string, orderId: string): Promise<RestaurantOrder | null> {
    const order = this.orders.get(orderId);
    
    if (!order || order.companyId !== companyId) {
      return null;
    }

    return order;
  }

  async deleteOrder(companyId: string, orderId: string): Promise<boolean> {
    const order = this.orders.get(orderId);
    
    if (!order || order.companyId !== companyId) {
      return false;
    }

    return this.orders.delete(orderId);
  }

  async getOrderAnalytics(companyId: string, startDate?: Date, endDate?: Date) {
    const orders = Array.from(this.orders.values())
      .filter(order => {
        if (order.companyId !== companyId) return false;
        if (startDate && order.createdAt < startDate) return false;
        if (endDate && order.createdAt > endDate) return false;
        return true;
      });

    const totalOrders = orders.length;
    const totalRevenue = orders.reduce((sum, order) => sum + order.totalAmount, 0);
    const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    const ordersByStatus = orders.map(order => ({
      status: order.status,
      amount: order.totalAmount
    }));

    return {
      totalOrders,
      totalRevenue,
      avgOrderValue,
      ordersByStatus
    };
  }
} 