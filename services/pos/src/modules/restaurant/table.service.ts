import { db } from '../../db/client';

// Generate a unique string ID
function generateId(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

export interface RestaurantTable {
  _id: string;
  companyId: string;
  tableNumber: string;
  floorId?: string;
  floorName?: string;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'cleaning' | 'maintenance';
  position: {
    x: number;
    y: number;
  };
  shape: 'square' | 'round' | 'rectangle';
  size: 'small' | 'medium' | 'large';
  isVipTable: boolean;
  currentOrderId?: string;
  reservationInfo?: {
    customerName: string;
    customerPhone: string;
    reservationTime: Date;
    partySize: number;
    notes?: string;
  };
  lastCleanedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface RestaurantFloor {
  _id: string;
  companyId: string;
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  displayOrder: number;
  layout?: {
    width: number;
    height: number;
    backgroundImage?: string;
  };
  settings?: {
    gridSize: number;
    snapToGrid: boolean;
    showGridLines: boolean;
  };
  tableCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

const TABLES_COLLECTION = 'restaurant_tables';
const FLOORS_COLLECTION = 'restaurant_floors';

export class TableService {
  private get tablesCollection() {
    return db.collection(TABLES_COLLECTION);
  }

  private get floorsCollection() {
    return db.collection(FLOORS_COLLECTION);
  }

  // Table Management
  async createTable(companyId: string, tableData: Partial<RestaurantTable>): Promise<RestaurantTable> {
    // Validate required fields
    if (!tableData.tableNumber || tableData.tableNumber.trim() === '') {
      throw new Error('Table number is required');
    }

    // Normalize table number (trim and convert to consistent case)
    const normalizedTableNumber = tableData.tableNumber.trim();
    
    // Check for table number uniqueness within the company (case-insensitive)
    const existingTable = await this.tablesCollection.findOne({
      companyId,
      tableNumber: { $regex: new RegExp(`^${normalizedTableNumber.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') }
    } as any);

    if (existingTable) {
      throw new Error(`Table number "${normalizedTableNumber}" already exists. Please choose a different table number.`);
    }

    const _id = generateId();
    const newTable: RestaurantTable = {
      _id,
      companyId,
      tableNumber: normalizedTableNumber,
      floorId: tableData.floorId,
      floorName: tableData.floorName,
      capacity: tableData.capacity || 4,
      status: 'available',
      position: tableData.position || { x: 0, y: 0 },
      shape: tableData.shape || 'square',
      size: tableData.size || 'medium',
      isVipTable: tableData.isVipTable || false,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.tablesCollection.insertOne(newTable as any);
    console.log('Table created successfully', { tableId: newTable._id, tableNumber: newTable.tableNumber, companyId });
    return newTable;
  }

  // Check if table number is available
  async isTableNumberAvailable(companyId: string, tableNumber: string): Promise<boolean> {
    if (!tableNumber || tableNumber.trim() === '') {
      return false;
    }

    const normalizedTableNumber = tableNumber.trim();
    const existingTable = await this.tablesCollection.findOne({
      companyId,
      tableNumber: { $regex: new RegExp(`^${normalizedTableNumber.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') }
    } as any);

    return !existingTable;
  }

  // Get next available table number
  async getNextTableNumber(companyId: string): Promise<string> {
    const tables = await this.tablesCollection.find({ companyId } as any).toArray();
    
    // Extract numeric table numbers and find the highest
    const numericTableNumbers = tables
      .map(table => parseInt(table.tableNumber))
      .filter(num => !isNaN(num))
      .sort((a, b) => a - b);

    if (numericTableNumbers.length === 0) {
      return '1';
    }

    return (Math.max(...numericTableNumbers) + 1).toString();
  }

  async updateTablePosition(companyId: string, tableId: string, position: { x: number; y: number }): Promise<RestaurantTable | null> {
    const result = await this.tablesCollection.findOneAndUpdate(
      { _id: tableId, companyId } as any,
      {
        $set: {
          position,
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    console.log('Table position updated', { tableId, position, companyId });
    return result ? { ...result, _id: result._id.toString() } as unknown as RestaurantTable : null;
  }

  async updateMultipleTablePositions(companyId: string, updates: Array<{ tableId: string; position: { x: number; y: number } }>): Promise<boolean> {
    try {
      const bulkOps = updates.map(update => ({
        updateOne: {
          filter: { _id: update.tableId, companyId },
          update: {
            $set: {
              position: update.position,
              updatedAt: new Date()
            }
          }
        }
      }));

      const result = await this.tablesCollection.bulkWrite(bulkOps as any);
      console.log('Multiple table positions updated', { companyId, updatedCount: result.modifiedCount, totalUpdates: updates.length });
      
      return result.modifiedCount === updates.length;
    } catch (error) {
      console.error('Failed to update multiple table positions:', error);
      return false;
    }
  }

  async updateTableStatus(companyId: string, tableId: string, status: RestaurantTable['status'], orderId?: string): Promise<RestaurantTable | null> {
    let updateOperation: any;

    if (orderId) {
      updateOperation = {
        $set: {
          status,
          currentOrderId: orderId,
          updatedAt: new Date()
        }
      };
    } else if (status === 'available') {
      updateOperation = {
        $set: {
          status,
          updatedAt: new Date()
        },
        $unset: {
          currentOrderId: 1
        }
      };
    } else {
      updateOperation = {
        $set: {
          status,
          updatedAt: new Date()
        }
      };
    }

    const result = await this.tablesCollection.findOneAndUpdate(
      { _id: tableId, companyId } as any,
      updateOperation,
      { returnDocument: 'after' }
    );

    console.log('Table status updated', { tableId, status, companyId });
    return result ? { ...result, _id: result._id.toString() } as unknown as RestaurantTable : null;
  }

  async deleteTable(companyId: string, tableId: string): Promise<boolean> {
    // Check if table is currently occupied or reserved
    const table = await this.getTableById(companyId, tableId);
    
    if (!table) {
      throw new Error('Table not found');
    }

    if (table.status === 'occupied') {
      throw new Error('Cannot delete table that is currently occupied. Please clear the table first.');
    }

    if (table.status === 'reserved') {
      throw new Error('Cannot delete table that is reserved. Please cancel the reservation first.');
    }

    const result = await this.tablesCollection.deleteOne({ _id: tableId, companyId } as any);
    
    if (result.deletedCount > 0) {
      console.log('Table deleted successfully', { tableId, tableNumber: table.tableNumber, companyId });
      return true;
    }
    
    return false;
  }

  async getTables(companyId: string, floorId?: string): Promise<RestaurantTable[]> {
    const filter: any = { companyId };
    if (floorId) {
      filter.floorId = floorId;
    }

    const tables = await this.tablesCollection.find(filter).sort({ tableNumber: 1 }).toArray();
    
    console.log('Tables retrieved', { count: tables.length, companyId, floorId });
    return tables.map(table => ({ ...table, _id: table._id.toString() })) as unknown as RestaurantTable[];
  }

  async getTableById(companyId: string, tableId: string): Promise<RestaurantTable | null> {
    const table = await this.tablesCollection.findOne({ _id: tableId, companyId } as any);
    return table ? { ...table, _id: table._id.toString() } as unknown as RestaurantTable : null;
  }

  // Table Reservation Management
  async reserveTable(companyId: string, tableId: string, reservationData: RestaurantTable['reservationInfo']): Promise<RestaurantTable | null> {
    const result = await this.tablesCollection.findOneAndUpdate(
      { _id: tableId, companyId, status: 'available' } as any,
      {
        $set: {
          status: 'reserved',
          reservationInfo: reservationData,
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (result) {
      console.log('Table reserved successfully', { tableId, companyId });
    }
    
    return result ? { ...result, _id: result._id.toString() } as unknown as RestaurantTable : null;
  }

  async cancelReservation(companyId: string, tableId: string): Promise<RestaurantTable | null> {
    const result = await this.tablesCollection.findOneAndUpdate(
      { _id: tableId, companyId } as any,
      {
        $set: {
          status: 'available',
          updatedAt: new Date()
        },
        $unset: { reservationInfo: '' }
      },
      { returnDocument: 'after' }
    );

    console.log('Table reservation cancelled', { tableId, companyId });
    return result ? { ...result, _id: result._id.toString() } as unknown as RestaurantTable : null;
  }

  // Floor Management
  async createFloor(companyId: string, floorData: Partial<RestaurantFloor>): Promise<RestaurantFloor> {
    if (!floorData.name || floorData.name.trim() === '') {
      throw new Error('Floor name is required');
    }

    // Check for floor name uniqueness within the company
    const existingFloor = await this.floorsCollection.findOne({
      companyId,
      name: { $regex: new RegExp(`^${floorData.name.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') },
      isActive: true
    } as any);

    if (existingFloor) {
      throw new Error(`Floor name "${floorData.name.trim()}" already exists. Please choose a different name.`);
    }

    const _id = generateId();
    const newFloor: RestaurantFloor = {
      _id,
      companyId,
      name: floorData.name.trim(),
      description: floorData.description,
      color: floorData.color || '#3B82F6',
      isActive: floorData.isActive !== false,
      displayOrder: floorData.displayOrder || 0,
      layout: floorData.layout || {
        width: 1200,
        height: 800
      },
      settings: floorData.settings || {
        gridSize: 20,
        snapToGrid: true,
        showGridLines: false
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.floorsCollection.insertOne(newFloor as any);
    console.log('Floor created successfully', { floorId: newFloor._id, companyId });
    return newFloor;
  }

  async getFloors(companyId: string): Promise<RestaurantFloor[]> {
    const floors = await this.floorsCollection.find({ companyId, isActive: true } as any)
      .sort({ displayOrder: 1, name: 1 })
      .toArray();
    
    // Add table count for each floor
    const floorsWithCounts = await Promise.all(floors.map(async (floor) => {
      const tableCount = await this.tablesCollection.countDocuments({ companyId, floorId: floor._id.toString() } as any);
      return {
        ...floor,
        _id: floor._id.toString(),
        tableCount
      } as unknown as RestaurantFloor;
    }));
    
    console.log('Floors retrieved', { count: floorsWithCounts.length, companyId });
    return floorsWithCounts;
  }

  async getFloorById(companyId: string, floorId: string): Promise<RestaurantFloor | null> {
    const floor = await this.floorsCollection.findOne({ _id: floorId, companyId, isActive: true } as any);
    
    if (!floor) {
      return null;
    }

    const tableCount = await this.tablesCollection.countDocuments({ companyId, floorId } as any);
    
    return {
      ...floor,
      _id: floor._id.toString(),
      tableCount
    } as unknown as RestaurantFloor;
  }

  async updateFloor(companyId: string, floorId: string, updateData: Partial<RestaurantFloor>): Promise<RestaurantFloor | null> {
    // If updating name, check for uniqueness
    if (updateData.name && updateData.name.trim() !== '') {
      const existingFloor = await this.floorsCollection.findOne({
        companyId,
        _id: { $ne: floorId },
        name: { $regex: new RegExp(`^${updateData.name.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}$`, 'i') },
        isActive: true
      } as any);

      if (existingFloor) {
        throw new Error(`Floor name "${updateData.name.trim()}" already exists. Please choose a different name.`);
      }
    }

    const result = await this.floorsCollection.findOneAndUpdate(
      { _id: floorId, companyId } as any,
      {
        $set: {
          ...updateData,
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (!result) {
      return null;
    }

    const tableCount = await this.tablesCollection.countDocuments({ companyId, floorId } as any);
    
    console.log('Floor updated successfully', { floorId, companyId });
    return {
      ...result,
      _id: result._id.toString(),
      tableCount
    } as unknown as RestaurantFloor;
  }

  async deleteFloor(companyId: string, floorId: string): Promise<boolean> {
    // Check if floor has tables
    const tableCount = await this.tablesCollection.countDocuments({ companyId, floorId } as any);
    
    if (tableCount > 0) {
      throw new Error(`Cannot delete floor. It contains ${tableCount} table(s). Please move or delete all tables first.`);
    }

    const result = await this.floorsCollection.findOneAndUpdate(
      { _id: floorId, companyId } as any,
      {
        $set: {
          isActive: false,
          updatedAt: new Date()
        }
      }
    );

    console.log('Floor deleted successfully', { floorId, companyId });
    return result !== null;
  }

  async moveTableToFloor(companyId: string, tableId: string, targetFloorId: string): Promise<RestaurantTable | null> {
    // Get target floor info
    const targetFloor = await this.getFloorById(companyId, targetFloorId);
    if (!targetFloor) {
      throw new Error('Target floor not found');
    }

    const result = await this.tablesCollection.findOneAndUpdate(
      { _id: tableId, companyId } as any,
      {
        $set: {
          floorId: targetFloorId,
          floorName: targetFloor.name,
          position: { x: 100, y: 100 }, // Reset position when moving floors
          updatedAt: new Date()
        }
      },
      { returnDocument: 'after' }
    );

    if (result) {
      console.log('Table moved to floor', { tableId, targetFloorId, companyId });
    }
    
    return result ? { ...result, _id: result._id.toString() } as unknown as RestaurantTable : null;
  }

  async reorderFloors(companyId: string, floorOrders: Array<{ floorId: string; displayOrder: number }>): Promise<boolean> {
    const bulkOps = floorOrders.map(({ floorId, displayOrder }) => ({
      updateOne: {
        filter: { _id: floorId, companyId },
        update: { $set: { displayOrder, updatedAt: new Date() } }
      }
    }));

    if (bulkOps.length > 0) {
      await this.floorsCollection.bulkWrite(bulkOps as any);
      console.log('Floors reordered successfully', { companyId, count: bulkOps.length });
    }

    return true;
  }

  // Initialize default floors and tables for new restaurant
  async createDefaultTables(companyId: string): Promise<RestaurantTable[]> {
    // Create main dining floor
    const mainFloor = await this.createFloor(companyId, {
      name: 'Main Dining',
      description: 'Main dining area',
      color: '#3B82F6',
      displayOrder: 1
    });

    // Create default tables layout (2x3 grid)
    const defaultTables = [
      { tableNumber: '1', position: { x: 100, y: 100 }, capacity: 4, shape: 'square' as const },
      { tableNumber: '2', position: { x: 200, y: 100 }, capacity: 4, shape: 'square' as const },
      { tableNumber: '3', position: { x: 300, y: 100 }, capacity: 4, shape: 'square' as const },
      { tableNumber: '4', position: { x: 100, y: 200 }, capacity: 6, shape: 'rectangle' as const },
      { tableNumber: '5', position: { x: 200, y: 200 }, capacity: 6, shape: 'rectangle' as const },
      { tableNumber: '6', position: { x: 300, y: 200 }, capacity: 8, shape: 'round' as const, size: 'large' as const, isVipTable: true }
    ];

    const createdTables: RestaurantTable[] = [];
    
    for (const tableData of defaultTables) {
      const table = await this.createTable(companyId, {
        ...tableData,
        floorId: mainFloor._id,
        floorName: mainFloor.name
      });
      createdTables.push(table);
    }

    console.log('Default restaurant tables created', { companyId, count: createdTables.length });
    return createdTables;
  }
} 