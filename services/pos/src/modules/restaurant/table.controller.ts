import { Elysia, t } from 'elysia';
import { TableService } from './table.service';

const tableService = new TableService();

export const tableController = new Elysia({ prefix: '/tables' })
  // Get all tables for a company
  .get('/', async ({ query }) => {
    try {
      const { companyId, floorId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching tables for company:', companyId);
      
      const tables = await tableService.getTables(companyId, floorId);
      
      return {
        success: true,
        data: tables
      };
    } catch (err) {
      console.error('Failed to fetch tables:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch tables' 
      };
    }
  }, {
    query: t.Object({
      companyId: t.String(),
      floorId: t.Optional(t.String())
    })
  })

  // Create a new table
  .post('/', async ({ body }) => {
    try {
      const { companyId, tableNumber, floorId, capacity, position, shape, size, isVipTable } = body;
      
      if (!companyId || !tableNumber) {
        return { success: false, error: 'Company ID and table number are required' };
      }

      console.log('Creating table:', tableNumber, 'for company:', companyId);
      
      const newTable = await tableService.createTable(companyId, {
        tableNumber,
        floorId,
        capacity,
        position,
        shape,
        size,
        isVipTable
      });
      
      return {
        success: true,
        data: newTable
      };
    } catch (err) {
      console.error('Failed to create table:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create table' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      tableNumber: t.String(),
      floorId: t.Optional(t.String()),
      capacity: t.Optional(t.Number()),
      position: t.Optional(t.Object({
        x: t.Number(),
        y: t.Number()
      })),
      shape: t.Optional(t.Union([t.Literal('square'), t.Literal('round'), t.Literal('rectangle')])),
      size: t.Optional(t.Union([t.Literal('small'), t.Literal('medium'), t.Literal('large')])),
      isVipTable: t.Optional(t.Boolean())
    })
  })

  // Delete table
  .delete('/:tableId', async ({ params, query }) => {
    try {
      const { tableId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Deleting table:', tableId, 'for company:', companyId);
      
      const success = await tableService.deleteTable(companyId, tableId);
      
      if (!success) {
        return { success: false, error: 'Failed to delete table' };
      }
      
      return {
        success: true,
        message: 'Table deleted successfully'
      };
    } catch (err) {
      console.error('Failed to delete table:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to delete table' 
      };
    }
  }, {
    params: t.Object({
      tableId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Update table status
  .patch('/:tableId/status', async ({ params, body }) => {
    try {
      const { tableId } = params;
      const { companyId, status } = body;
      
      if (!companyId || !status) {
        return { success: false, error: 'Company ID and status are required' };
      }

      console.log('Updating table status:', tableId, 'to', status);
      
      const updatedTable = await tableService.updateTableStatus(companyId, tableId, status);
      
      if (!updatedTable) {
        return { success: false, error: 'Table not found' };
      }
      
      return {
        success: true,
        data: updatedTable
      };
    } catch (err) {
      console.error('Failed to update table status:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update table status' 
      };
    }
  }, {
    params: t.Object({
      tableId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      status: t.Union([
        t.Literal('available'),
        t.Literal('occupied'),
        t.Literal('reserved'),
        t.Literal('cleaning'),
        t.Literal('maintenance')
      ])
    })
  })

  // Get table by ID
  .get('/:tableId', async ({ params, query }) => {
    try {
      const { tableId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching table:', tableId, 'for company:', companyId);
      
      const table = await tableService.getTableById(companyId, tableId);
      
      if (!table) {
        return { success: false, error: 'Table not found' };
      }
      
      return {
        success: true,
        data: table
      };
    } catch (err) {
      console.error('Failed to fetch table:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch table' 
      };
    }
  }, {
    params: t.Object({
      tableId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Reserve table
  .post('/:tableId/reserve', async ({ params, body }) => {
    try {
      const { tableId } = params;
      const { companyId, reservationInfo } = body;
      
      if (!companyId || !reservationInfo) {
        return { success: false, error: 'Company ID and reservation info are required' };
      }

      console.log('Reserving table:', tableId, 'for company:', companyId);
      
      const reservedTable = await tableService.reserveTable(companyId, tableId, {
        ...reservationInfo,
        reservationTime: new Date(reservationInfo.reservationTime)
      });
      
      if (!reservedTable) {
        return { success: false, error: 'Table not found or not available for reservation' };
      }
      
      return {
        success: true,
        data: reservedTable
      };
    } catch (err) {
      console.error('Failed to reserve table:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to reserve table' 
      };
    }
  }, {
    params: t.Object({
      tableId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      reservationInfo: t.Object({
        customerName: t.String(),
        customerPhone: t.String(),
        reservationTime: t.String(),
        partySize: t.Number(),
        notes: t.Optional(t.String())
      })
    })
  })

  // Update table position
  .patch('/:tableId/position', async ({ params, body }) => {
    try {
      const { tableId } = params;
      const { companyId, position } = body;
      
      if (!companyId || !position) {
        return { success: false, error: 'Company ID and position are required' };
      }

      console.log('Updating table position:', tableId, 'to', position);
      
      const updatedTable = await tableService.updateTablePosition(companyId, tableId, position);
      
      if (!updatedTable) {
        return { success: false, error: 'Table not found' };
      }
      
      return {
        success: true,
        data: updatedTable
      };
    } catch (err) {
      console.error('Failed to update table position:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update table position' 
      };
    }
  }, {
    params: t.Object({
      tableId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      position: t.Object({
        x: t.Number(),
        y: t.Number()
      })
    })
  })

  // Bulk update table positions
  .patch('/positions/bulk', async ({ body }) => {
    try {
      const { companyId, updates } = body;
      
      if (!companyId || !updates || !Array.isArray(updates)) {
        return { success: false, error: 'Company ID and updates array are required' };
      }

      console.log('Bulk updating table positions for company:', companyId);
      
      const success = await tableService.updateMultipleTablePositions(companyId, updates);
      
      return {
        success,
        message: success ? 'Table positions updated successfully' : 'Failed to update some table positions'
      };
    } catch (err) {
      console.error('Failed to bulk update table positions:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to bulk update table positions' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      updates: t.Array(t.Object({
        tableId: t.String(),
        position: t.Object({
          x: t.Number(),
          y: t.Number()
        })
      }))
    })
  })

  // Initialize default tables
  .post('/initialize-defaults', async ({ body }) => {
    try {
      const { companyId } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Initializing default tables for company:', companyId);
      
      const tables = await tableService.createDefaultTables(companyId);
      
      return {
        success: true,
        data: tables,
        message: 'Default restaurant tables and floors created successfully'
      };
    } catch (err) {
      console.error('Failed to initialize default tables:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to initialize default tables' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String()
    })
  })

  // Check table number availability
  .get('/check-availability/:tableNumber', async ({ params, query }) => {
    try {
      const { tableNumber } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Checking table number availability:', tableNumber, 'for company:', companyId);
      
      const isAvailable = await tableService.isTableNumberAvailable(companyId, tableNumber);
      
      return {
        success: true,
        data: {
          tableNumber,
          isAvailable
        }
      };
    } catch (err) {
      console.error('Failed to check table number availability:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to check table number availability' 
      };
    }
  }, {
    params: t.Object({
      tableNumber: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Get next available table number
  .get('/next-number', async ({ query }) => {
    try {
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Getting next table number for company:', companyId);
      
      const nextTableNumber = await tableService.getNextTableNumber(companyId);
      
      return {
        success: true,
        data: {
          nextTableNumber
        }
      };
    } catch (err) {
      console.error('Failed to get next table number:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to get next table number' 
      };
    }
  }, {
    query: t.Object({
      companyId: t.String()
    })
  })

  // Move table to floor
  .patch('/:tableId/move-floor', async ({ params, body }) => {
    try {
      const { tableId } = params;
      const { companyId, targetFloorId } = body;
      
      if (!companyId || !targetFloorId) {
        return { success: false, error: 'Company ID and target floor ID are required' };
      }

      console.log('Moving table to floor:', tableId, 'to floor:', targetFloorId);
      
      const movedTable = await tableService.moveTableToFloor(companyId, tableId, targetFloorId);
      
      if (!movedTable) {
        return { success: false, error: 'Failed to move table to floor' };
      }
      
      return {
        success: true,
        data: movedTable
      };
    } catch (err) {
      console.error('Failed to move table to floor:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to move table to floor' 
      };
    }
  }, {
    params: t.Object({
      tableId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      targetFloorId: t.String()
    })
  });

// Note: Floor Management Controller has been moved to floor.controller.ts 