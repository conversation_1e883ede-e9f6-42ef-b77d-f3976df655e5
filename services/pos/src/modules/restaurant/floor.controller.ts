import { Elysia, t } from 'elysia';
import { TableService } from './table.service';

const tableService = new TableService();

export const floorController = new Elysia({ prefix: '/floors' })
  // Get all floors for a company
  .get('/', async ({ query }) => {
    try {
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching floors for company:', companyId);
      
      const floors = await tableService.getFloors(companyId);
      
      return {
        success: true,
        data: floors
      };
    } catch (err) {
      console.error('Failed to fetch floors:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch floors' 
      };
    }
  }, {
    query: t.Object({
      companyId: t.String()
    })
  })

  // Get floor by ID
  .get('/:floorId', async ({ params, query }) => {
    try {
      const { floorId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Fetching floor:', floorId, 'for company:', companyId);
      
      const floor = await tableService.getFloorById(companyId, floorId);
      
      if (!floor) {
        return { success: false, error: 'Floor not found' };
      }
      
      return {
        success: true,
        data: floor
      };
    } catch (err) {
      console.error('Failed to fetch floor:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to fetch floor' 
      };
    }
  }, {
    params: t.Object({
      floorId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Create a new floor
  .post('/', async ({ body }) => {
    try {
      const { companyId, name, description, color, layout, settings } = body;
      
      if (!companyId || !name) {
        return { success: false, error: 'Company ID and floor name are required' };
      }

      console.log('Creating floor:', name, 'for company:', companyId);
      
      const newFloor = await tableService.createFloor(companyId, {
        name,
        description,
        color,
        layout,
        settings
      });
      
      return {
        success: true,
        data: newFloor
      };
    } catch (err) {
      console.error('Failed to create floor:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to create floor' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      name: t.String(),
      description: t.Optional(t.String()),
      color: t.Optional(t.String()),
      layout: t.Optional(t.Object({
        width: t.Number(),
        height: t.Number(),
        backgroundImage: t.Optional(t.String())
      })),
      settings: t.Optional(t.Object({
        gridSize: t.Number(),
        snapToGrid: t.Boolean(),
        showGridLines: t.Boolean()
      }))
    })
  })

  // Update floor
  .put('/:floorId', async ({ params, body }) => {
    try {
      const { floorId } = params;
      const { companyId, name, description, color, layout, settings } = body;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Updating floor:', floorId, 'for company:', companyId);
      
      const updatedFloor = await tableService.updateFloor(companyId, floorId, {
        name,
        description,
        color,
        layout,
        settings
      });
      
      if (!updatedFloor) {
        return { success: false, error: 'Floor not found' };
      }
      
      return {
        success: true,
        data: updatedFloor
      };
    } catch (err) {
      console.error('Failed to update floor:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to update floor' 
      };
    }
  }, {
    params: t.Object({
      floorId: t.String()
    }),
    body: t.Object({
      companyId: t.String(),
      name: t.Optional(t.String()),
      description: t.Optional(t.String()),
      color: t.Optional(t.String()),
      layout: t.Optional(t.Object({
        width: t.Number(),
        height: t.Number(),
        backgroundImage: t.Optional(t.String())
      })),
      settings: t.Optional(t.Object({
        gridSize: t.Number(),
        snapToGrid: t.Boolean(),
        showGridLines: t.Boolean()
      }))
    })
  })

  // Delete floor
  .delete('/:floorId', async ({ params, query }) => {
    try {
      const { floorId } = params;
      const { companyId } = query;
      
      if (!companyId) {
        return { success: false, error: 'Company ID is required' };
      }

      console.log('Deleting floor:', floorId, 'for company:', companyId);
      
      const success = await tableService.deleteFloor(companyId, floorId);
      
      if (!success) {
        return { success: false, error: 'Floor not found' };
      }
      
      return {
        success: true,
        message: 'Floor deleted successfully'
      };
    } catch (err) {
      console.error('Failed to delete floor:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to delete floor' 
      };
    }
  }, {
    params: t.Object({
      floorId: t.String()
    }),
    query: t.Object({
      companyId: t.String()
    })
  })

  // Reorder floors
  .patch('/reorder', async ({ body }) => {
    try {
      const { companyId, floorOrders } = body;
      
      if (!companyId || !floorOrders || !Array.isArray(floorOrders)) {
        return { success: false, error: 'Company ID and floor orders array are required' };
      }

      console.log('Reordering floors for company:', companyId);
      
      const success = await tableService.reorderFloors(companyId, floorOrders);
      
      return {
        success,
        message: success ? 'Floors reordered successfully' : 'Failed to reorder floors'
      };
    } catch (err) {
      console.error('Failed to reorder floors:', err);
      return { 
        success: false, 
        error: err instanceof Error ? err.message : 'Failed to reorder floors' 
      };
    }
  }, {
    body: t.Object({
      companyId: t.String(),
      floorOrders: t.Array(t.Object({
        floorId: t.String(),
        displayOrder: t.Number()
      }))
    })
  }); 