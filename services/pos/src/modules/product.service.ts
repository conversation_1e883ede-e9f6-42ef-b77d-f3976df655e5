import { db } from '../db/client';
import { createLogger } from '../../../../shared/utils/logger';
import { generateHexId } from '../../../../shared/utils/id-generator';
import type { Product, RestaurantProductFeatures } from '../../../../shared/types/pos';

const logger = createLogger('PRODUCT_SERVICE');

export interface ProductListOptions {
  page?: number;
  limit?: number;
  search?: string;
  categoryId?: string;
  isAvailable?: boolean;
  spiceLevel?: string;
  mealTime?: string;
  dietaryRestriction?: string;
  isBBQItem?: boolean;
  sortBy?: 'name' | 'price' | 'stock' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  companyId: string;
}

export interface ProductListResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreateProductRequest {
  companyId: string;
  name: string;
  nameKhmer?: string;
  description?: string;
  descriptionKhmer?: string;
  sku: string;
  barcode?: string;
  price: number;
  priceUSD?: number;
  cost: number;
  costUSD?: number;
  stock: number;
  minStock: number;
  categoryId: string;
  unit: string;
  supplier?: string;
  expiryDate?: Date;
  batchNumber?: string;
  restaurantFeatures?: RestaurantProductFeatures;
  images?: string[];
  thumbnail?: string;
  isAvailable?: boolean;
  availableFrom?: string;
  availableUntil?: string;
  daysAvailable?: string[];
  modifiers?: any[];
}

export interface UpdateProductRequest {
  name?: string;
  nameKhmer?: string;
  description?: string;
  descriptionKhmer?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  priceUSD?: number;
  cost?: number;
  costUSD?: number;
  stock?: number;
  minStock?: number;
  categoryId?: string;
  unit?: string;
  supplier?: string;
  expiryDate?: Date;
  batchNumber?: string;
  restaurantFeatures?: RestaurantProductFeatures;
  images?: string[];
  thumbnail?: string;
  isAvailable?: boolean;
  availableFrom?: string;
  availableUntil?: string;
  daysAvailable?: string[];
  modifiers?: any[];
  isActive?: boolean;
}

export class ProductService {
  private get database() {
    return db;
  }

  private get productsCollection() {
    return this.database.collection('products');
  }

  // Helper method to create documents with hex ID as _id
  private createDocument(data: Record<string, any>): Record<string, any> {
    const now = new Date();
    return {
      _id: generateHexId(),
      ...data,
      createdAt: now,
      updatedAt: now,
    };
  }

  // Helper method to update documents with updatedAt
  private updateDocument(data: Record<string, any>): Record<string, any> {
    return {
      ...data,
      updatedAt: new Date(),
    };
  }

  async getProducts(options: ProductListOptions): Promise<ProductListResponse> {
    const {
      page = 1,
      limit = 20,
      search,
      categoryId,
      isAvailable,
      spiceLevel,
      mealTime,
      dietaryRestriction,
      isBBQItem,
      sortBy = 'name',
      sortOrder = 'asc',
      companyId
    } = options;

    // Build filter query
    const filter: any = { companyId, isActive: true };

    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { nameKhmer: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { sku: { $regex: search, $options: 'i' } }
      ];
    }

    if (categoryId) {
      filter.categoryId = categoryId;
    }

    if (isAvailable !== undefined) {
      filter.isAvailable = isAvailable;
    }

    // Restaurant-specific filters
    if (spiceLevel) {
      filter['restaurantFeatures.spiceLevel'] = spiceLevel;
    }

    if (mealTime) {
      filter['restaurantFeatures.mealTimes'] = mealTime;
    }

    if (dietaryRestriction) {
      filter['restaurantFeatures.dietaryRestrictions'] = dietaryRestriction;
    }

    if (isBBQItem) {
      filter['restaurantFeatures.isBBQItem'] = true;
    }

    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [products, total] = await Promise.all([
      this.productsCollection.find(filter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .toArray(),
      this.productsCollection.countDocuments(filter)
    ]);

    return {
      products: products as any[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  async getProduct(id: string): Promise<Product | null> {
    const product = await this.productsCollection.findOne({ _id: id, isActive: true } as any);
    return product as Product | null;
  }

  async createProduct(data: CreateProductRequest): Promise<Product> {
    logger.info('Creating product', { name: data.name, companyId: data.companyId });

    // Check for duplicate SKU
    const existingProduct = await this.productsCollection.findOne({
      sku: data.sku,
      companyId: data.companyId,
      isActive: true
    });

    if (existingProduct) {
      throw new Error('Product with this SKU already exists');
    }

    const product = this.createDocument({
      ...data,
      isActive: true,
      isAvailable: data.isAvailable ?? true,
      daysAvailable: data.daysAvailable || [],
      modifiers: data.modifiers || [],
      images: data.images || [],
    });

    await this.productsCollection.insertOne(product as any);

    logger.info('Product created successfully', { 
      productId: product._id, 
      name: data.name 
    });

    return product as Product;
  }

  async updateProduct(id: string, data: UpdateProductRequest): Promise<Product | null> {
    logger.info('Updating product', { productId: id });

    // Check if product exists
    const existingProduct = await this.productsCollection.findOne({ _id: id, isActive: true } as any);
    if (!existingProduct) {
      return null;
    }

    // Check for duplicate SKU if SKU is being updated
    if (data.sku && data.sku !== existingProduct.sku) {
      const duplicateProduct = await this.productsCollection.findOne({
        sku: data.sku,
        companyId: existingProduct.companyId,
        isActive: true,
        _id: { $ne: id }
      } as any);

      if (duplicateProduct) {
        throw new Error('Product with this SKU already exists');
      }
    }

    const result = await this.productsCollection.updateOne(
      { _id: id } as any,
      { $set: this.updateDocument(data) }
    );

    if (result.modifiedCount === 0) {
      return null;
    }

    logger.info('Product updated successfully', { productId: id });

    return this.getProduct(id);
  }

  async deleteProduct(id: string): Promise<boolean> {
    logger.info('Deleting product', { productId: id });

    // Soft delete - set isActive to false
    const result = await this.productsCollection.updateOne(
      { _id: id } as any,
      { $set: this.updateDocument({ isActive: false }) }
    );

    const success = result.modifiedCount > 0;

    if (success) {
      logger.info('Product deleted successfully', { productId: id });
    }

    return success;
  }

  async updateStock(id: string, stock: number): Promise<Product | null> {
    logger.info('Updating product stock', { productId: id, stock });

    const result = await this.productsCollection.updateOne(
      { _id: id } as any,
      { $set: this.updateDocument({ stock }) }
    );

    if (result.modifiedCount === 0) {
      return null;
    }

    return this.getProduct(id);
  }

  async bulkUpdateStock(updates: { productId: string; stock: number }[]): Promise<void> {
    logger.info('Bulk updating stock', { updateCount: updates.length });

    const bulkOps = updates.map(update => ({
      updateOne: {
        filter: { _id: update.productId, isActive: true } as any,
        update: { $set: { stock: update.stock, updatedAt: new Date() } }
      }
    }));

    await this.productsCollection.bulkWrite(bulkOps as any);

    logger.info('Bulk stock update completed', { updateCount: updates.length });
  }

  async toggleAvailability(id: string, isAvailable: boolean): Promise<Product | null> {
    logger.info('Toggling product availability', { productId: id, isAvailable });

    const result = await this.productsCollection.updateOne(
      { _id: id } as any,
      { $set: this.updateDocument({ isAvailable }) }
    );

    if (result.modifiedCount === 0) {
      return null;
    }

    return this.getProduct(id);
  }

  async duplicateProduct(id: string): Promise<Product | null> {
    logger.info('Duplicating product', { productId: id });

    const originalProduct = await this.productsCollection.findOne({ _id: id, isActive: true } as any);
    if (!originalProduct) {
      return null;
    }

    // Create duplicate with modified name and new SKU
    const duplicateData = {
      ...originalProduct,
      _id: generateHexId(),
      name: `${originalProduct.name} (Copy)`,
      nameKhmer: originalProduct.nameKhmer ? `${originalProduct.nameKhmer} (ចម្លង)` : undefined,
      sku: `${originalProduct.sku}-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await this.productsCollection.insertOne(duplicateData as any);

    logger.info('Product duplicated successfully', { 
      originalId: id, 
      duplicateId: duplicateData._id 
    });

    return duplicateData as Product;
  }

  async getProductsByCategory(categoryId: string, companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      categoryId,
      companyId,
      isActive: true
    }).toArray();

    return products as any[];
  }

  async getQRMenuProducts(companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      isAvailable: true,
      $or: [
        { 'restaurantFeatures.availableForDineIn': true },
        { 'restaurantFeatures.availableForTakeout': true }
      ]
    }).toArray();

    return products as any[];
  }

  async getBBQProducts(companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      'restaurantFeatures.isBBQItem': true
    }).toArray();

    return products as any[];
  }

  async getProductsByMealTime(mealTime: string, companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      'restaurantFeatures.mealTimes': mealTime
    }).toArray();

    return products as any[];
  }

  async getKhmerTraditionalProducts(companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      'restaurantFeatures.isTraditionalKhmer': true
    }).toArray();

    return products as any[];
  }

  async getProductsBySpiceLevel(spiceLevel: string, companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      'restaurantFeatures.spiceLevel': spiceLevel
    }).toArray();

    return products as any[];
  }

  async getProductsByRegion(region: string, companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      'restaurantFeatures.region': region
    }).toArray();

    return products as any[];
  }

  async searchProducts(query: string, companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { nameKhmer: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { descriptionKhmer: { $regex: query, $options: 'i' } },
        { sku: { $regex: query, $options: 'i' } }
      ]
    }).toArray();

    return products as any[];
  }

  async getLowStockProducts(companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      $expr: { $lte: ['$stock', '$minStock'] }
    }).toArray();

    return products as any[];
  }

  async getOutOfStockProducts(companyId: string): Promise<Product[]> {
    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      stock: { $lte: 0 }
    }).toArray();

    return products as any[];
  }

  async getProductsExpiringToday(companyId: string): Promise<Product[]> {
    const today = new Date();
    today.setHours(23, 59, 59, 999);

    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      expiryDate: { $lte: today }
    }).toArray();

    return products as any[];
  }

  async getProductsExpiringWithin(companyId: string, days: number): Promise<Product[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    const products = await this.productsCollection.find({
      companyId,
      isActive: true,
      expiryDate: { $lte: futureDate }
    }).toArray();

    return products as any[];
  }

  async getProductStatistics(companyId: string): Promise<{
    total: number;
    available: number;
    lowStock: number;
    outOfStock: number;
    expiringToday: number;
    khmerTraditional: number;
    bbqItems: number;
  }> {
    const [
      total,
      available,
      lowStock,
      outOfStock,
      expiringToday,
      khmerTraditional,
      bbqItems
    ] = await Promise.all([
      this.productsCollection.countDocuments({ companyId, isActive: true }),
      this.productsCollection.countDocuments({ companyId, isActive: true, isAvailable: true }),
      this.productsCollection.countDocuments({ 
        companyId, 
        isActive: true, 
        $expr: { $lte: ['$stock', '$minStock'] } 
      }),
      this.productsCollection.countDocuments({ 
        companyId, 
        isActive: true, 
        stock: { $lte: 0 } 
      }),
      this.productsCollection.countDocuments({ 
        companyId, 
        isActive: true, 
        expiryDate: { $lte: new Date() } 
      }),
      this.productsCollection.countDocuments({ 
        companyId, 
        isActive: true, 
        'restaurantFeatures.isTraditionalKhmer': true 
      }),
      this.productsCollection.countDocuments({ 
        companyId, 
        isActive: true, 
        'restaurantFeatures.isBBQItem': true 
      })
    ]);

    return {
      total,
      available,
      lowStock,
      outOfStock,
      expiringToday,
      khmerTraditional,
      bbqItems
    };
  }
} 