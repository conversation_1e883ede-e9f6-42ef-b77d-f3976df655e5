import { Elysia, t } from 'elysia';
import { ProductService } from './product.service';
import { createLogger } from '../../../../shared/utils/logger';

const logger = createLogger('PRODUCT_CONTROLLER');
const productService = new ProductService();

export const productController = new Elysia({ prefix: '/products' })
  // Get all products with filtering and pagination
  .get('/', async ({ query, set }) => {
    try {
      const options = {
        page: query.page ? parseInt(query.page as string) : 1,
        limit: query.limit ? parseInt(query.limit as string) : 20,
        search: query.search as string,
        categoryId: query.categoryId as string,
        isAvailable: query.isAvailable === 'true' ? true : query.isAvailable === 'false' ? false : undefined,
        spiceLevel: query.spiceLevel as string,
        mealTime: query.mealTime as string,
        dietaryRestriction: query.dietaryRestriction as string,
        isBBQItem: query.isBBQItem === 'true' ? true : undefined,
        sortBy: (query.sortBy as 'name' | 'price' | 'stock' | 'createdAt') || 'name',
        sortOrder: (query.sortOrder as 'asc' | 'desc') || 'asc',
        companyId: query.companyId as string,
      };

      const result = await productService.getProducts(options);
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      logger.error('Get products failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        query
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get products',
        code: 'GET_PRODUCTS_FAILED'
      };
    }
  })

  // Get product by ID
  .get('/:id', async ({ params, set }) => {
    try {
      const product = await productService.getProduct(params.id);
      
      if (!product) {
        set.status = 404;
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }
      
      return {
        success: true,
        data: product
      };
    } catch (error) {
      logger.error('Get product failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        productId: params.id
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get product',
        code: 'GET_PRODUCT_FAILED'
      };
    }
  })

  // Create new product
  .post('/', async ({ body, set }) => {
    try {
      const product = await productService.createProduct(body);
      
      logger.info('Product created successfully', { 
        productId: product._id,
        name: product.name,
        companyId: product.companyId
      });
      
      return {
        success: true,
        data: product,
        message: 'Product created successfully'
      };
    } catch (error) {
      logger.error('Create product failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        body
      });
      
      set.status = 400;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create product',
        code: 'CREATE_PRODUCT_FAILED'
      };
    }
  }, {
    body: t.Object({
      companyId: t.String({ minLength: 1 }),
      name: t.String({ minLength: 1 }),
      nameKhmer: t.Optional(t.String()),
      description: t.Optional(t.String()),
      descriptionKhmer: t.Optional(t.String()),
      sku: t.String({ minLength: 1 }),
      barcode: t.Optional(t.String()),
      price: t.Number({ minimum: 0 }),
      priceUSD: t.Optional(t.Number({ minimum: 0 })),
      cost: t.Number({ minimum: 0 }),
      costUSD: t.Optional(t.Number({ minimum: 0 })),
      stock: t.Number({ minimum: 0 }),
      minStock: t.Number({ minimum: 0 }),
      categoryId: t.String({ minLength: 1 }),
      unit: t.String({ minLength: 1 }),
      supplier: t.Optional(t.String()),
      expiryDate: t.Optional(t.Date()),
      batchNumber: t.Optional(t.String()),
      restaurantFeatures: t.Optional(t.Object({
        spiceLevel: t.Optional(t.Union([
          t.Literal('mild'),
          t.Literal('medium'),
          t.Literal('hot'),
          t.Literal('extra_hot')
        ])),
        isTraditionalKhmer: t.Optional(t.Boolean()),
        region: t.Optional(t.Union([
          t.Literal('phnom_penh'),
          t.Literal('siem_reap'),
          t.Literal('battambang'),
          t.Literal('kampot'),
          t.Literal('other')
        ])),
        mealTimes: t.Optional(t.Array(t.Union([
          t.Literal('breakfast'),
          t.Literal('lunch'),
          t.Literal('dinner'),
          t.Literal('snack'),
          t.Literal('dessert')
        ]))),
        dietaryRestrictions: t.Optional(t.Array(t.Union([
          t.Literal('vegetarian'),
          t.Literal('vegan'),
          t.Literal('gluten_free'),
          t.Literal('dairy_free'),
          t.Literal('halal'),
          t.Literal('kosher')
        ]))),
        allergens: t.Optional(t.Array(t.Union([
          t.Literal('nuts'),
          t.Literal('seafood'),
          t.Literal('dairy'),
          t.Literal('eggs'),
          t.Literal('soy'),
          t.Literal('wheat'),
          t.Literal('sesame')
        ]))),
        preparationTime: t.Optional(t.Number({ minimum: 0 })),
        cookingMethod: t.Optional(t.Union([
          t.Literal('grilled'),
          t.Literal('fried'),
          t.Literal('steamed'),
          t.Literal('boiled'),
          t.Literal('raw'),
          t.Literal('baked'),
          t.Literal('stir_fried')
        ])),
        kitchenNotes: t.Optional(t.String()),
        availableForTakeout: t.Optional(t.Boolean()),
        availableForDelivery: t.Optional(t.Boolean()),
        availableForDineIn: t.Optional(t.Boolean()),
        isBBQItem: t.Optional(t.Boolean()),
        bbqCategory: t.Optional(t.Union([
          t.Literal('meat'),
          t.Literal('seafood'),
          t.Literal('vegetable'),
          t.Literal('side'),
          t.Literal('sauce')
        ])),
        servingSize: t.Optional(t.String()),
        servingUnit: t.Optional(t.Union([
          t.Literal('person'),
          t.Literal('plate'),
          t.Literal('bowl'),
          t.Literal('cup'),
          t.Literal('piece')
        ])),
        portionSizes: t.Optional(t.Object({
          small: t.Optional(t.Object({
            price: t.Number({ minimum: 0 }),
            priceUSD: t.Optional(t.Number({ minimum: 0 }))
          })),
          medium: t.Optional(t.Object({
            price: t.Number({ minimum: 0 }),
            priceUSD: t.Optional(t.Number({ minimum: 0 }))
          })),
          large: t.Optional(t.Object({
            price: t.Number({ minimum: 0 }),
            priceUSD: t.Optional(t.Number({ minimum: 0 }))
          }))
        }))
      })),
      images: t.Optional(t.Array(t.String())),
      thumbnail: t.Optional(t.String()),
      isAvailable: t.Optional(t.Boolean()),
      availableFrom: t.Optional(t.String()),
      availableUntil: t.Optional(t.String()),
      daysAvailable: t.Optional(t.Array(t.Union([
        t.Literal('monday'),
        t.Literal('tuesday'),
        t.Literal('wednesday'),
        t.Literal('thursday'),
        t.Literal('friday'),
        t.Literal('saturday'),
        t.Literal('sunday')
      ]))),
      modifiers: t.Optional(t.Array(t.Any()))
    })
  })

  // Update product
  .put('/:id', async ({ params, body, set }) => {
    try {
      const product = await productService.updateProduct(params.id, body as any);
      
      if (!product) {
        set.status = 404;
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }
      
      logger.info('Product updated successfully', { 
        productId: params.id,
        name: product.name
      });
      
      return {
        success: true,
        data: product,
        message: 'Product updated successfully'
      };
    } catch (error) {
      logger.error('Update product failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        productId: params.id
      });
      
      set.status = 400;
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update product',
        code: 'UPDATE_PRODUCT_FAILED'
      };
    }
  })

  // Delete product
  .delete('/:id', async ({ params, set }) => {
    try {
      const success = await productService.deleteProduct(params.id);
      
      if (!success) {
        set.status = 404;
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }
      
      logger.info('Product deleted successfully', { productId: params.id });
      
      return {
        success: true,
        message: 'Product deleted successfully'
      };
    } catch (error) {
      logger.error('Delete product failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        productId: params.id
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to delete product',
        code: 'DELETE_PRODUCT_FAILED'
      };
    }
  })

  // Update stock
  .patch('/:id/stock', async ({ params, body, set }) => {
    try {
      const product = await productService.updateStock(params.id, body.stock);
      
      if (!product) {
        set.status = 404;
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }
      
      return {
        success: true,
        data: product,
        message: 'Stock updated successfully'
      };
    } catch (error) {
      logger.error('Update stock failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        productId: params.id
      });
      
      set.status = 400;
      return {
        success: false,
        error: 'Failed to update stock',
        code: 'UPDATE_STOCK_FAILED'
      };
    }
  }, {
    body: t.Object({
      stock: t.Number({ minimum: 0 })
    })
  })

  // Toggle availability
  .patch('/:id/availability', async ({ params, body, set }) => {
    try {
      const product = await productService.toggleAvailability(params.id, body.isAvailable);
      
      if (!product) {
        set.status = 404;
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }
      
      return {
        success: true,
        data: product,
        message: 'Availability updated successfully'
      };
    } catch (error) {
      logger.error('Toggle availability failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        productId: params.id
      });
      
      set.status = 400;
      return {
        success: false,
        error: 'Failed to update availability',
        code: 'UPDATE_AVAILABILITY_FAILED'
      };
    }
  }, {
    body: t.Object({
      isAvailable: t.Boolean()
    })
  })

  // Duplicate product
  .post('/:id/duplicate', async ({ params, set }) => {
    try {
      const product = await productService.duplicateProduct(params.id);
      
      if (!product) {
        set.status = 404;
        return {
          success: false,
          error: 'Product not found',
          code: 'PRODUCT_NOT_FOUND'
        };
      }
      
      return {
        success: true,
        data: product,
        message: 'Product duplicated successfully'
      };
    } catch (error) {
      logger.error('Duplicate product failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        productId: params.id
      });
      
      set.status = 400;
      return {
        success: false,
        error: 'Failed to duplicate product',
        code: 'DUPLICATE_PRODUCT_FAILED'
      };
    }
  })

  // Get products by category
  .get('/category/:categoryId', async ({ params, query, set }) => {
    try {
      const products = await productService.getProductsByCategory(
        params.categoryId,
        query.companyId as string
      );
      
      return {
        success: true,
        data: products
      };
    } catch (error) {
      logger.error('Get products by category failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        categoryId: params.categoryId
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get products by category',
        code: 'GET_CATEGORY_PRODUCTS_FAILED'
      };
    }
  })

  // Get QR menu products
  .get('/qr-menu', async ({ query, set }) => {
    try {
      const products = await productService.getQRMenuProducts(query.companyId as string);
      
      return {
        success: true,
        data: products
      };
    } catch (error) {
      logger.error('Get QR menu products failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: query.companyId
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get QR menu products',
        code: 'GET_QR_MENU_PRODUCTS_FAILED'
      };
    }
  })

  // Get BBQ products
  .get('/bbq', async ({ query, set }) => {
    try {
      const products = await productService.getBBQProducts(query.companyId as string);
      
      return {
        success: true,
        data: products
      };
    } catch (error) {
      logger.error('Get BBQ products failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: query.companyId
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get BBQ products',
        code: 'GET_BBQ_PRODUCTS_FAILED'
      };
    }
  })

  // Get products by meal time
  .get('/meal-time/:mealTime', async ({ params, query, set }) => {
    try {
      const products = await productService.getProductsByMealTime(
        params.mealTime,
        query.companyId as string
      );
      
      return {
        success: true,
        data: products
      };
    } catch (error) {
      logger.error('Get products by meal time failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        mealTime: params.mealTime
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get products by meal time',
        code: 'GET_MEAL_TIME_PRODUCTS_FAILED'
      };
    }
  })

  // Get Khmer traditional products
  .get('/khmer-traditional', async ({ query, set }) => {
    try {
      const products = await productService.getKhmerTraditionalProducts(query.companyId as string);
      
      return {
        success: true,
        data: products
      };
    } catch (error) {
      logger.error('Get Khmer traditional products failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        companyId: query.companyId
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get Khmer traditional products',
        code: 'GET_KHMER_TRADITIONAL_PRODUCTS_FAILED'
      };
    }
  })

  // Get products by spice level
  .get('/spice-level/:spiceLevel', async ({ params, query, set }) => {
    try {
      const products = await productService.getProductsBySpiceLevel(
        params.spiceLevel,
        query.companyId as string
      );
      
      return {
        success: true,
        data: products
      };
    } catch (error) {
      logger.error('Get products by spice level failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        spiceLevel: params.spiceLevel
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get products by spice level',
        code: 'GET_SPICE_LEVEL_PRODUCTS_FAILED'
      };
    }
  })

  // Get products by region
  .get('/region/:region', async ({ params, query, set }) => {
    try {
      const products = await productService.getProductsByRegion(
        params.region,
        query.companyId as string
      );
      
      return {
        success: true,
        data: products
      };
    } catch (error) {
      logger.error('Get products by region failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        region: params.region
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to get products by region',
        code: 'GET_REGION_PRODUCTS_FAILED'
      };
    }
  })

  // Search products
  .get('/search', async ({ query, set }) => {
    try {
      const products = await productService.searchProducts(
        query.query as string,
        query.companyId as string
      );
      
      return {
        success: true,
        data: products
      };
    } catch (error) {
      logger.error('Search products failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        query: query.query
      });
      
      set.status = 500;
      return {
        success: false,
        error: 'Failed to search products',
        code: 'SEARCH_PRODUCTS_FAILED'
      };
    }
  })

  // Bulk update stock
  .patch('/bulk-stock', async ({ body, set }) => {
    try {
      await productService.bulkUpdateStock(body.updates);
      
      return {
        success: true,
        message: 'Stock updated successfully'
      };
    } catch (error) {
      logger.error('Bulk update stock failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        updates: body.updates
      });
      
      set.status = 400;
      return {
        success: false,
        error: 'Failed to update stock',
        code: 'BULK_UPDATE_STOCK_FAILED'
      };
    }
  }, {
    body: t.Object({
      updates: t.Array(t.Object({
        productId: t.String(),
        stock: t.Number({ minimum: 0 })
      }))
    })
  }); 