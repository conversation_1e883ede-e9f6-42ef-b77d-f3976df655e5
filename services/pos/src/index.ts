import { Elysia } from 'elysia';
import { cors } from '@elysiajs/cors';
import { swagger } from '@elysiajs/swagger';
import { connectToDatabase } from './db/client';
import { categoryController } from './modules/inventory/category.controller';
import { priceLevelController } from './modules/inventory/price-level.controller';
import { productController } from './modules/product.controller';
import { restaurantModule } from './modules/restaurant';

const app = new Elysia()
  .use(cors({
    origin: true,
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  }))
  .use(swagger({
    documentation: {
      info: {
        title: 'ElyPOS - POS Service API',
        description: 'Point of Sale service for inventory, sales, and category management',
        version: '1.0.0',
      },
      tags: [
        { name: 'Categories', description: 'Product category management endpoints' },
        { name: 'Price Levels', description: 'Price level and pricing management endpoints' },
        { name: 'Products', description: 'Product inventory endpoints' },
        { name: 'Sales', description: 'Sales transaction endpoints' },
        { name: 'Restaurant', description: 'Restaurant POS with table management and QR ordering' },
      ],
    },
  }))
  .get('/', () => {
    return {
      service: 'elypos-pos',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
    };
  })
  .get('/health', () => {
    return {
      status: 'healthy',
      service: 'elypos-pos',
      timestamp: new Date().toISOString(),
    };
  })
  .use(categoryController)
  .use(priceLevelController)
  .use(productController)
  .use(restaurantModule)
  .onError(({ error, code }) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    console.error('POS Service error occurred:', {
      code,
      error: errorMessage,
    });

    if (code === 'NOT_FOUND') {
      return {
        error: 'Route not found',
        message: 'The requested endpoint does not exist',
        timestamp: new Date().toISOString(),
      };
    }

    return {
      success: false,
      error: errorMessage,
      timestamp: new Date().toISOString(),
    };
  })
// Initialize database connection and start server
async function startServer() {
  try {
    await connectToDatabase();
    
    app.listen(parseInt(process.env.PORT || '3003'));

    console.log(`🚀 POS Service is running on port ${process.env.PORT || '3003'}`);

    console.log('Available routes:', {
  routes: [
    'GET /',
    'GET /health',
    'GET /swagger',
    // Category endpoints
    'GET /categories/tree',
    'POST /categories',
    'PUT /categories/:id',
    'DELETE /categories/:id',
    // Price level endpoints
    'GET /price-levels',
    'POST /price-levels',
    'PUT /price-levels/:id',
    'DELETE /price-levels/:id',
    'POST /price-levels/reorder',
    'GET /price-levels/default/info',
    // Product pricing endpoints
    'GET /price-levels/products/:productId/prices',
    'POST /price-levels/products/prices',
    'DELETE /price-levels/products/:productId/prices/:priceLevelId',
    // Restaurant endpoints
    'GET /restaurant/health',
    'GET /restaurant/tables',
    'POST /restaurant/tables',
    'PATCH /restaurant/tables/:tableId/status',
    'POST /restaurant/tables/:tableId/reserve',
    'POST /restaurant/tables/initialize-defaults',
    'POST /restaurant/qr-menu',
    'GET /restaurant/qr-menu/table/:tableId',
    'POST /restaurant/qr-menu/:qrMenuId/scan',
    'POST /restaurant/qr-menu/:qrMenuId/bbq/start',
    'POST /restaurant/qr-menu/bbq/:sessionId/order',
    'POST /restaurant/qr-menu/:qrMenuId/order',
    'POST /restaurant/qr-menu/khmer-categories/initialize',
  ],
}); 
  } catch (error) {
    console.error('❌ Failed to start POS service:', error);
    process.exit(1);
  }
}

startServer(); 