{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "strict": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "noEmit": true, "resolveJsonModule": true, "lib": ["ES2022", "DOM"], "paths": {"@shared/*": ["../shared/*"]}}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist"]}