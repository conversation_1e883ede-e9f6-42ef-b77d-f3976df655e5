import { createLogger } from '@shared/utils/logger';

const logger = createLogger('GATEWAY_ENV');

interface GatewayEnv {
  PORT: string;
  NODE_ENV: string;
  JWT_SECRET: string;
  MONGODB_URI: string;
  
  // Service URLs
  CORE_SERVICE_URL: string;
  POS_SERVICE_URL: string;
  LOAN_SERVICE_URL: string;
  
  // Logging
  LOG_LEVEL: string;
}

function validateEnv(): GatewayEnv {
  const requiredEnvVars = [
    'PORT',
    'NODE_ENV',
    'JWT_SECRET',
    'MONGODB_URI',
    'CORE_SERVICE_URL',
    'POS_SERVICE_URL',
    'LOAN_SERVICE_URL',
    'LOG_LEVEL'
  ];

  const missingVars: string[] = [];
  
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    }
  }

  if (missingVars.length > 0) {
    logger.error('Missing required environment variables', { missingVars });
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }

  return {
    PORT: process.env.PORT!,
    NODE_ENV: process.env.NODE_ENV!,
    JWT_SECRET: process.env.JWT_SECRET!,
    MONGODB_URI: process.env.MONGODB_URI!,
    CORE_SERVICE_URL: process.env.CORE_SERVICE_URL!,
    POS_SERVICE_URL: process.env.POS_SERVICE_URL!,
    LOAN_SERVICE_URL: process.env.LOAN_SERVICE_URL!,
    LOG_LEVEL: process.env.LOG_LEVEL!,
  };
}

export const env = validateEnv();

logger.info('Environment variables loaded successfully', {
  NODE_ENV: env.NODE_ENV,
  PORT: env.PORT,
  LOG_LEVEL: env.LOG_LEVEL,
}); 