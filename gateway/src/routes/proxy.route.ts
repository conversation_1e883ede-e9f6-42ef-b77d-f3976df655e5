import { Elysia } from 'elysia';
import { forwardRequest } from '../utils/forward';
import { createLogger } from '../../../shared/utils/logger';

const logger = createLogger('PROXY_ROUTE');

function extractHeaders(headers: Headers): Record<string, string> {
  const headersObj: Record<string, string> = {};
  headers.forEach((value, key) => {
    headersObj[key] = value;
  });
  return headersObj;
}

// Helper function to handle forwarded responses
async function handleForwardedResponse(
  service: 'core' | 'pos' | 'loan',
  method: string,
  path: string,
  body: any,
  headers: Record<string, string>
) {
  const result = await forwardRequest({
    service,
    method,
    path,
    body,
    headers,
  });
  
  return new Response(JSON.stringify(result.data), {
    status: result.status,
    headers: { 'Content-Type': 'application/json' },
  });
}

export const proxyRoutes = new Elysia()
  // Direct auth routes (no /api prefix) - proxy to core service
  .all('/auth/*', async ({ request, params }) => {
    const basePath = '/auth/' + (params['*'] || '');
    const url = new URL(request.url);
    const path = basePath + url.search; // Include query parameters
    const method = request.method as any;
    const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
    
    logger.debug(`Proxying auth request to core service`, {
      method,
      path,
      hasBody: !!body,
    });

    return handleForwardedResponse('core', method, path, body, extractHeaders(request.headers));
  })
  // Direct company routes (no /api prefix) - proxy to core service
  .options('/companies/*', async () => {
    return new Response(null, { status: 200 });
  })
  .all('/companies/*', async ({ request, params }) => {
    const basePath = '/companies/' + (params['*'] || '');
    const url = new URL(request.url);
    const path = basePath + url.search; // Include query parameters
    const method = request.method as any;
    const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
    
    logger.debug(`Proxying companies request to core service`, {
      method,
      path,
      hasBody: !!body,
    });

    return handleForwardedResponse('core', method, path, body, extractHeaders(request.headers));
  })
  // Direct companies root route (no /api prefix) - proxy to core service
  .options('/companies', async () => {
    return new Response(null, { status: 200 });
  })
  .all('/companies', async ({ request }) => {
    const method = request.method as any;
    const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
    
    logger.debug(`Proxying companies root request to core service`, {
      method,
      path: '/companies',
      hasBody: !!body,
    });

    return handleForwardedResponse('core', method, '/companies', body, extractHeaders(request.headers));
  })
  // Direct subscription routes (no /api prefix) - proxy to core service
  .all('/subscriptions/*', async ({ request, params }) => {
    const basePath = '/subscriptions/' + (params['*'] || '');
    const url = new URL(request.url);
    const path = basePath + url.search; // Include query parameters
    const method = request.method as any;
    const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
    
    logger.debug(`Proxying subscriptions request to core service`, {
      method,
      path,
      hasBody: !!body,
    });

    return handleForwardedResponse('core', method, path, body, extractHeaders(request.headers));
  })
  // Direct coupon routes (no /api prefix) - proxy to core service
  .all('/coupons/*', async ({ request, params }) => {
    const basePath = '/coupons/' + (params['*'] || '');
    const url = new URL(request.url);
    const path = basePath + url.search; // Include query parameters
    const method = request.method as any;
    const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
    
    logger.debug(`Proxying coupons request to core service`, {
      method,
      path,
      hasBody: !!body,
    });

    return handleForwardedResponse('core', method, path, body, extractHeaders(request.headers));
  })
  // Direct payment routes (no /api prefix) - proxy to core service
  .all('/payments/*', async ({ request, params }) => {
    const basePath = '/payments/' + (params['*'] || '');
    const url = new URL(request.url);
    const path = basePath + url.search; // Include query parameters
    const method = request.method as any;
    const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
    
    logger.debug(`Proxying payments request to core service`, {
      method,
      path,
      hasBody: !!body,
    });

    return handleForwardedResponse('core', method, path, body, extractHeaders(request.headers));
  })
  // Direct payment root route (no /api prefix) - proxy to core service
  .all('/payments', async ({ request }) => {
    const method = request.method as any;
    const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
    
    logger.debug(`Proxying payments root request to core service`, {
      method,
      path: '/payments',
      hasBody: !!body,
    });

    return handleForwardedResponse('core', method, '/payments', body, extractHeaders(request.headers));
  })
  
  // API prefixed routes for explicit service targeting
  .group('/api', (app) => 
    app
      .all('/core/*', async ({ request, params }) => {
        const basePath = '/' + (params['*'] || '');
        const url = new URL(request.url);
        const path = basePath + url.search; // Include query parameters
        const method = request.method as any;
        const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
        
        logger.debug(`Proxying request to core service`, {
          method,
          path,
          hasBody: !!body,
        });

        return handleForwardedResponse('core', method, path, body, extractHeaders(request.headers));
      })
      .all('/pos/*', async ({ request, params }) => {
        const basePath = '/' + (params['*'] || '');
        const url = new URL(request.url);
        const path = basePath + url.search; // Include query parameters
        const method = request.method as any;
        const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
        
        logger.debug(`Proxying request to pos service`, {
          method,
          path,
          hasBody: !!body,
        });

        return handleForwardedResponse('pos', method, path, body, extractHeaders(request.headers));
      })
      .all('/loan/*', async ({ request, params }) => {
        const basePath = '/' + (params['*'] || '');
        const url = new URL(request.url);
        const path = basePath + url.search; // Include query parameters
        const method = request.method as any;
        const body = method !== 'GET' && method !== 'DELETE' ? await request.json().catch(() => null) : undefined;
        
        logger.debug(`Proxying request to loan service`, {
          method,
          path,
          hasBody: !!body,
        });

        return handleForwardedResponse('loan', method, path, body, extractHeaders(request.headers));
      })
  )
  .get('/health', () => {
    return {
      status: 'ok',
      service: 'elypos-gateway',
      timestamp: new Date().toISOString(),
    };
  }); 