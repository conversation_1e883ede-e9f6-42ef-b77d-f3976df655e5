import { Elysia } from 'elysia';
import { cors } from '@elysiajs/cors';
import { swagger } from '@elysiajs/swagger';
import { env } from './config/env';
import { proxyRoutes } from './routes/proxy.route';
import { createLogger } from '../../shared/utils/logger';

const logger = createLogger('GATEWAY', env.LOG_LEVEL as any);

const app = new Elysia()
  .use(cors({
    origin: true, // Allow all origins in development
    credentials: true,
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept', 'Origin', 'X-Requested-With'],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  }))
  .use(swagger({
    documentation: {
      info: {
        title: 'ElyPOS Gateway API',
        description: 'API Gateway for ElyPOS microservices',
        version: '1.0.0',
      },
      tags: [
        { name: 'Gateway', description: 'Gateway health and info endpoints' },
        { name: 'Core', description: 'Core service endpoints (auth, companies, subscriptions)' },
        { name: 'POS', description: 'Point of Sale service endpoints' },
        { name: 'Loan', description: 'Loan management service endpoints' },
      ],
    },
  }))
  .get('/', () => {
    return {
      service: 'elypos-gateway',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
    };
  })
  .use(proxyRoutes)
  .onError(({ error, code }) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    logger.error('Gateway error occurred', {
      code,
      error: errorMessage,
      stack: errorStack,
    });

    if (code === 'NOT_FOUND') {
      return {
        error: 'Route not found',
        message: 'The requested endpoint does not exist',
        timestamp: new Date().toISOString(),
      };
    }

    return {
      error: 'Internal server error',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
    };
  })
  .listen(parseInt(env.PORT));

logger.info(`🚀 Gateway server is running on port ${env.PORT}`, {
  port: env.PORT,
  environment: env.NODE_ENV,
});

logger.info('Available routes:', {
  routes: [
    'GET /',
    'GET /swagger',
    'GET /health',
    // Direct routes (no /api prefix)
    'ALL /auth/*',
    'ALL /companies/*',
    'ALL /subscriptions/*',
    'ALL /coupons/*',
    'ALL /payments/*',
    'ALL /payments',
    // API prefixed routes
    'ALL /api/core/*',
    'ALL /api/pos/*',
    'ALL /api/loan/*',
  ],
}); 