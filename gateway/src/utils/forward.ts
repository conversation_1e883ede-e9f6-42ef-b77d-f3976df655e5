import { env } from '../config/env';
import { createLogger } from '../../../shared/utils/logger';

const logger = createLogger('FORWARD_UTIL');

interface ForwardRequestOptions {
  service: 'core' | 'pos' | 'loan';
  method: string;
  path: string;
  body?: any;
  headers?: Record<string, string>;
}

const serviceUrls = {
  core: env.CORE_SERVICE_URL,
  pos: env.POS_SERVICE_URL,
  loan: env.LOAN_SERVICE_URL,
};

export async function forwardRequest(options: ForwardRequestOptions): Promise<{ data: any; status: number }> {
  const { service, method, path, body, headers = {} } = options;
  
  const serviceUrl = serviceUrls[service];
  if (!serviceUrl) {
    throw new Error(`Unknown service: ${service}`);
  }

  const url = `${serviceUrl}${path}`;
  
  logger.debug('Forwarding request', {
    service,
    method,
    url,
    hasBody: !!body,
  });

  try {
    const requestOptions: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
    };

    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      requestOptions.body = JSON.stringify(body);
    }

    const response = await fetch(url, requestOptions);
    
    // For 5xx server errors, treat as actual failures
    if (response.status >= 500) {
      const errorText = await response.text();
      logger.error('Service request failed', {
        service,
        method,
        url,
        status: response.status,
        statusText: response.statusText,
        error: errorText,
      });
      
      throw new Error(`Service request failed: ${response.status} ${response.statusText}`);
    }

    // For 4xx client errors and 2xx success, parse JSON and return
    const responseData = await response.json();
    
    if (response.status >= 400) {
      logger.warn('Service returned client error', {
        service,
        method,
        url,
        status: response.status,
        statusText: response.statusText,
        data: responseData,
      });
    } else {
      logger.debug('Request forwarded successfully', {
        service,
        method,
        url,
        status: response.status,
      });
    }

    return { data: responseData, status: response.status };
  } catch (error) {
    logger.error('Forward request error', {
      service,
      method,
      url,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    
    throw error;
  }
} 