# Gateway Service Environment Variables

# Server Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=INFO

# Microservices URLs
CORE_SERVICE_URL=http://localhost:3001
POS_SERVICE_URL=http://localhost:3002
LOAN_SERVICE_URL=http://localhost:3003

# CORS Configuration
CORS_ORIGIN=http://localhost:5173,http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Request Timeout (milliseconds)
REQUEST_TIMEOUT=30000

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000

# Load Balancing
ENABLE_LOAD_BALANCING=false
MAX_RETRIES=3 