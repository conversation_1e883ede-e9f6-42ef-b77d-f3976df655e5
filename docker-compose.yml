version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:7.0
    container_name: elypos-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: elypos
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - elypos-network

  # Gateway Service
  gateway:
    build:
      context: .
      dockerfile: ./gateway/Dockerfile
    container_name: elypos-gateway
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      PORT: 3000
      NODE_ENV: development
      JWT_SECRET: your-super-secret-jwt-key-here
      MONGODB_URI: *****************************************************************
      CORE_SERVICE_URL: http://core:3001
      POS_SERVICE_URL: http://pos:3002
      LOAN_SERVICE_URL: http://loan:3003
      LOG_LEVEL: INFO
    depends_on:
      - mongodb
      - core
      - pos
      - loan
    networks:
      - elypos-network

  # Core Service
  core:
    build:
      context: .
      dockerfile: ./services/core/Dockerfile
    container_name: elypos-core
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      PORT: 3001
      NODE_ENV: development
      JWT_SECRET: your-super-secret-jwt-key-here
      JWT_EXPIRES_IN: 24h
      MONGODB_URI: *****************************************************************
      MONGODB_DB_NAME: elypos_core
      LOG_LEVEL: INFO
    depends_on:
      - mongodb
    networks:
      - elypos-network

  # POS Service
  pos:
    build:
      context: .
      dockerfile: ./services/pos/Dockerfile
    container_name: elypos-pos
    restart: unless-stopped
    ports:
      - "3002:3002"
    environment:
      PORT: 3002
      NODE_ENV: development
      MONGODB_URI: *****************************************************************
      MONGODB_DB_NAME: elypos_pos
      CORE_SERVICE_URL: http://core:3001
      LOG_LEVEL: INFO
    depends_on:
      - mongodb
      - core
    networks:
      - elypos-network

  # Loan Service
  loan:
    build:
      context: .
      dockerfile: ./services/loan/Dockerfile
    container_name: elypos-loan
    restart: unless-stopped
    ports:
      - "3003:3003"
    environment:
      PORT: 3003
      NODE_ENV: development
      MONGODB_URI: *****************************************************************
      MONGODB_DB_NAME: elypos_loan
      CORE_SERVICE_URL: http://core:3001
      LOG_LEVEL: INFO
    depends_on:
      - mongodb
      - core
    networks:
      - elypos-network

volumes:
  mongodb_data:

networks:
  elypos-network:
    driver: bridge 