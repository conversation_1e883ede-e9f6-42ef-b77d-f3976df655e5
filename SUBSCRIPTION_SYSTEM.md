# 💳 ElyPOS Subscription Pricing System

## Overview

The ElyPOS system now supports a comprehensive subscription-based pricing model with granular pricing for different business types and modules.

## 📊 Pricing Structure

### Standard Modules (Flat Rate)
| Module     | Price ($/month) | Description                    |
|------------|-----------------|--------------------------------|
| ERP        | $50            | Enterprise Resource Planning   |
| LOAN       | $25            | Loan Management System         |
| ACCOUNTING | $15            | Accounting & Financial Tools   |

### POS Module (Business Type-Based Pricing)
| POS Business Type    | Price ($/month) | Use Case                      |
|---------------------|-----------------|-------------------------------|
| RESTAURANT          | $30            | Full-service restaurants      |
| HOTEL               | $35            | Hotel & hospitality           |
| FURNITURE_STORE     | $28            | Furniture & large items       |
| BAR                 | $25            | Bars & nightlife              |
| CLOTHING_STORE      | $25            | Fashion & apparel             |
| ELECTRONICS_STORE   | $24            | Electronics & tech            |
| PHARMACY            | $23            | Medical & pharmaceutical      |
| RETAIL_SHOP         | $22            | General retail                |
| GROCERY_STORE       | $22            | Grocery & food retail         |
| BEAUTY_SALON        | $21            | Beauty & wellness services    |
| BAKERY              | $20            | Bakeries & cafes              |
| SERVICE             | $20            | Service-based businesses      |
| GENERIC             | $18            | Basic POS functionality       |

## 🔧 API Endpoints

### Get Available Modules & Pricing
```bash
GET /subscriptions/modules
```

### Subscribe to Standard Module
```bash
POST /subscriptions/company/{companyId}/subscribe
Content-Type: application/json

{
  "module": "ERP|LOAN|ACCOUNTING",
  "currency": "USD|KHR"  // optional, defaults to USD
}
```

### Subscribe to POS Module
```bash
POST /subscriptions/company/{companyId}/subscribe-pos
Content-Type: application/json

{
  "posType": "RESTAURANT|BAKERY|HOTEL|...",
  "currency": "USD|KHR"  // optional, defaults to USD
}
```

### View Company Subscriptions
```bash
GET /subscriptions/company/{companyId}
```

### View Monthly Billing
```bash
GET /subscriptions/company/{companyId}/billing
```

### Unsubscribe from Module
```bash
DELETE /subscriptions/company/{companyId}/unsubscribe/{module}
```

### Check Module Status
```bash
GET /subscriptions/company/{companyId}/module/{module}/status
```

### Get Company's POS Type
```bash
GET /subscriptions/company/{companyId}/pos-type
```

### Process Monthly Billing
```bash
POST /subscriptions/company/{companyId}/process-billing
```

## 📈 Example Usage

### Complete Multi-Module Subscription
A restaurant company subscribing to multiple modules:

1. **Subscribe to POS (Restaurant)**: $30/month
```bash
curl -X POST http://localhost:3001/subscriptions/company/COMPANY_ID/subscribe-pos \
  -H "Content-Type: application/json" \
  -d '{"posType": "RESTAURANT", "currency": "USD"}'
```

2. **Subscribe to ERP**: $50/month
```bash
curl -X POST http://localhost:3001/subscriptions/company/COMPANY_ID/subscribe \
  -H "Content-Type: application/json" \
  -d '{"module": "ERP", "currency": "USD"}'
```

3. **Subscribe to LOAN**: $25/month
```bash
curl -X POST http://localhost:3001/subscriptions/company/COMPANY_ID/subscribe \
  -H "Content-Type: application/json" \
  -d '{"module": "LOAN", "currency": "USD"}'
```

**Total Monthly Cost**: $105 ($30 + $50 + $25)

### Billing Response Example
```json
{
  "success": true,
  "data": {
    "companyId": "7d3bf322e93d59ad409c3a09",
    "totalMonthlyAmount": 105,
    "currency": "USD",
    "activeModules": [
      {
        "module": "POS",
        "price": 30,
        "posType": "RESTAURANT"
      },
      {
        "module": "ERP",
        "price": 50
      },
      {
        "module": "LOAN",
        "price": 25
      }
    ],
    "nextBillingDate": "2025-08-08T15:04:57.984Z"
  }
}
```

## 🛠 Database Schema

### CompanySubscription Interface
```typescript
interface CompanySubscription {
  _id: string;
  companyId: string;
  module: 'POS' | 'LOAN' | 'ACCOUNTING' | 'ERP';
  active: boolean;
  price: number; // Calculated price per month
  currency: 'KHR' | 'USD';
  nextBillingDate: Date;
  posType?: PosBusinessType; // required if module === 'POS'
  subscribedAt: Date;
  lastBilledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}
```

### POS Business Types
```typescript
type PosBusinessType =
  | 'RESTAURANT' | 'BAR' | 'BAKERY' | 'RETAIL_SHOP'
  | 'CLOTHING_STORE' | 'FURNITURE_STORE' | 'PHARMACY'
  | 'ELECTRONICS_STORE' | 'GROCERY_STORE' | 'BEAUTY_SALON'
  | 'SERVICE' | 'HOTEL' | 'GENERIC';
```

## ✅ Features

- **Granular Pricing**: Different prices based on business types for POS module
- **Duplicate Prevention**: Cannot subscribe to the same module twice
- **Flexible Currency**: Support for USD and KHR currencies
- **Billing Calculation**: Automatic monthly billing calculation
- **Module Status Checking**: Check if specific modules are active
- **Subscription Management**: Easy subscribe/unsubscribe functionality
- **Billing Processing**: Monthly billing cycle management
- **Pricing Configuration**: Easily configurable pricing in code

## 🔄 Billing Cycle

1. **Subscription Creation**: Immediate activation with next billing date set to +1 month
2. **Monthly Billing**: Process billing to update next billing dates and record charges
3. **Active Monitoring**: Only active subscriptions count toward billing
4. **Flexible Unsubscription**: Can unsubscribe at any time (deactivates immediately)

## 🎯 Business Logic

- **POS Module**: Requires business type selection, pricing varies by type
- **Standard Modules**: Flat rate pricing regardless of company size/type
- **Multi-Module Support**: Companies can subscribe to any combination of modules
- **Currency Flexibility**: Supports both Cambodian Riel (KHR) and US Dollar (USD)
- **Billing Accuracy**: Calculates exact monthly costs based on active subscriptions
- **Admin Functions**: Price updates and subscription management for administrators

## 🔧 Testing Results

Successfully tested with real companies:
- ✅ POS subscription (RESTAURANT): $30/month
- ✅ ERP subscription: $50/month  
- ✅ LOAN subscription: $25/month
- ✅ Total billing: $105/month
- ✅ Unsubscription: Correctly updated to $80/month
- ✅ Different POS types: BAKERY at $20/month
- ✅ Duplicate prevention: Proper error handling
- ✅ Monthly billing processing: Updates billing dates and records charges

The subscription system is fully operational and ready for production use. 