#!/usr/bin/env node

/**
 * Environment Configuration Test Script
 * Tests ACLEDA Bank integration environment variables
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}✓${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}⚠${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}✗${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.bright}${colors.cyan}${msg}${colors.reset}`)
};

// ACLEDA Bank environment variables to test
const acledaEnvVars = {
  required: [
    'ACLEDA_MERCHANT_ID',
    'ACLEDA_LOGIN_ID',
    'ACLEDA_PASSWORD',
    'ACLEDA_SIGNATURE'
  ],
  optional: [
    'ACLEDA_ENVIRONMENT',
    'ACLEDA_MERCHANT_NAME',
    'ACLEDA_BASE_URL',
    'ACLEDA_PAYMENT_PAGE_URL',
    'ACLEDA_OPEN_SESSION_URL',
    'ACLEDA_GET_STATUS_URL',
    'ACLEDA_SUCCESS_URL',
    'ACLEDA_ERROR_URL',
    'ACLEDA_CALLBACK_URL'
  ]
};

// Default values for UAT environment
const defaultValues = {
  ACLEDA_ENVIRONMENT: 'UAT',
  ACLEDA_MERCHANT_ID: '/wRUtOaUXhK1l9JkMygbMW44ms0=',
  ACLEDA_LOGIN_ID: 'cosmouser',
  ACLEDA_PASSWORD: 'cosmouser',
  ACLEDA_SIGNATURE: 'demo_signature',
  ACLEDA_MERCHANT_NAME: 'COSMOSDIGITAL',
  ACLEDA_BASE_URL: 'https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL',
  ACLEDA_PAYMENT_PAGE_URL: 'https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/paymentPage.jsp',
  ACLEDA_OPEN_SESSION_URL: 'https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/openSessionV2',
  ACLEDA_GET_STATUS_URL: 'https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/getTxnStatus',
  ACLEDA_SUCCESS_URL: 'http://localhost:5174/payment/success',
  ACLEDA_ERROR_URL: 'http://localhost:5174/payment/failed',
  ACLEDA_CALLBACK_URL: 'http://localhost:3001/payments/callback'
};

function checkEnvironmentFile(filePath) {
  if (fs.existsSync(filePath)) {
    log.success(`Found environment file: ${filePath}`);
    return true;
  } else {
    log.warning(`Environment file not found: ${filePath}`);
    return false;
  }
}

function loadEnvironmentVariables() {
  const envFiles = [
    path.join(__dirname, '../services/core/.env'),
    path.join(__dirname, '../services/core/.env.local'),
    path.join(__dirname, '../.env'),
    path.join(__dirname, '../.env.local')
  ];

  log.header('Environment Files Check');
  
  let envFound = false;
  envFiles.forEach(file => {
    if (checkEnvironmentFile(file)) {
      envFound = true;
      try {
        const envContent = fs.readFileSync(file, 'utf8');
        const lines = envContent.split('\n');
        lines.forEach(line => {
          const match = line.match(/^([A-Z_]+)=(.*)$/);
          if (match && !process.env[match[1]]) {
            process.env[match[1]] = match[2];
          }
        });
      } catch (error) {
        log.error(`Failed to read ${file}: ${error.message}`);
      }
    }
  });

  if (!envFound) {
    log.warning('No environment files found, using system environment variables only');
  }
}

function validateACLEDAConfig() {
  log.header('ACLEDA Bank Configuration Validation');

  const results = {
    required: { passed: 0, failed: 0 },
    optional: { passed: 0, failed: 0 },
    warnings: []
  };

  // Check required variables
  log.info('Checking required ACLEDA Bank environment variables...');
  acledaEnvVars.required.forEach(varName => {
    const value = process.env[varName];
    if (value && value !== 'undefined' && value !== '') {
      log.success(`${varName}: Set`);
      results.required.passed++;
      
      // Check for default/demo values in production
      if (process.env.NODE_ENV === 'production' && 
          (value.includes('demo_') || value.includes('default_') || value === defaultValues[varName])) {
        log.warning(`${varName}: Using default/demo value in production environment`);
        results.warnings.push(`${varName} should be set to production value`);
      }
    } else {
      log.error(`${varName}: Missing or empty`);
      results.required.failed++;
    }
  });

  // Check optional variables
  log.info('\nChecking optional ACLEDA Bank environment variables...');
  acledaEnvVars.optional.forEach(varName => {
    const value = process.env[varName];
    if (value && value !== 'undefined' && value !== '') {
      log.success(`${varName}: ${value}`);
      results.optional.passed++;
    } else {
      log.warning(`${varName}: Using default value (${defaultValues[varName] || 'N/A'})`);
      results.optional.failed++;
    }
  });

  return results;
}

function validateConfiguration() {
  log.header('Configuration Validation Summary');

  const environment = process.env.ACLEDA_ENVIRONMENT || 'UAT';
  const merchantId = process.env.ACLEDA_MERCHANT_ID || defaultValues.ACLEDA_MERCHANT_ID;
  const signature = process.env.ACLEDA_SIGNATURE || defaultValues.ACLEDA_SIGNATURE;

  log.info(`Environment: ${environment}`);
  log.info(`Merchant ID: ${merchantId.substring(0, 10)}...`);
  log.info(`Signature Set: ${signature !== 'demo_signature' ? 'Yes' : 'No (using demo)'}`);

  // Validate URL configuration
  const baseUrl = process.env.ACLEDA_BASE_URL || defaultValues.ACLEDA_BASE_URL;
  const isProduction = environment === 'PRODUCTION';
  const isUsingProdUrl = !baseUrl.includes('uat') && !baseUrl.includes('UAT');

  if (isProduction && !isUsingProdUrl) {
    log.error('Production environment but using UAT URLs');
    return false;
  }

  if (!isProduction && isUsingProdUrl) {
    log.warning('Development/UAT environment but using production URLs');
  }

  return true;
}

function generateSamplePaymentRequest() {
  log.header('Sample Payment Request Configuration');

  const config = {
    amount: 25,
    currency: 'USD',
    description: 'Sample ACLEDA Bank Payment',
    companyId: 'sample_company_id',
    module: 'POS',
    paymentMethod: 'ACLEDA_ECOMMERCE',
    customerInfo: {
      name: 'Test User',
      email: '<EMAIL>',
      phone: '+************'
    },
    trialConversion: true
  };

  console.log('Sample payment request:');
  console.log(JSON.stringify(config, null, 2));

  const expectedResponse = {
    paymentId: 'PAY_timestamp_randomhex',
    status: 'pending',
    amount: config.amount,
    currency: config.currency,
    paymentMethod: config.paymentMethod,
    expiresAt: new Date(Date.now() + 15 * 60 * 1000),
    redirectUrl: `${process.env.ACLEDA_PAYMENT_PAGE_URL || defaultValues.ACLEDA_PAYMENT_PAGE_URL}?merchantID=...`,
    sessionId: 'generated_session_id',
    paymentTokenId: 'generated_payment_token',
    instructions: 'You will be redirected to ACLEDA Bank to complete your payment.'
  };

  console.log('\nExpected response format:');
  console.log(JSON.stringify(expectedResponse, null, 2));
}

function main() {
  console.log(`${colors.bright}${colors.cyan}ACLEDA Bank Environment Configuration Test${colors.reset}\n`);

  // Load environment variables
  loadEnvironmentVariables();

  // Validate ACLEDA configuration
  const validationResults = validateACLEDAConfig();

  // Validate overall configuration
  const configValid = validateConfiguration();

  // Generate sample request
  generateSamplePaymentRequest();

  // Summary
  log.header('Test Results Summary');

  const totalRequired = acledaEnvVars.required.length;
  const totalOptional = acledaEnvVars.optional.length;

  log.info(`Required variables: ${validationResults.required.passed}/${totalRequired} configured`);
  log.info(`Optional variables: ${validationResults.optional.passed}/${totalOptional} configured`);

  if (validationResults.warnings.length > 0) {
    log.warning(`Warnings: ${validationResults.warnings.length}`);
    validationResults.warnings.forEach(warning => {
      log.warning(`  - ${warning}`);
    });
  }

  if (validationResults.required.failed === 0 && configValid) {
    log.success('✅ ACLEDA Bank configuration is ready for use!');
    
    if (process.env.NODE_ENV === 'production') {
      log.info('Production environment detected - ensure all credentials are from ACLEDA Bank');
    } else {
      log.info('Development/UAT environment - using test credentials');
    }
    
    process.exit(0);
  } else {
    log.error('❌ Configuration issues found - please fix before deployment');
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  main();
}

module.exports = {
  validateACLEDAConfig,
  validateConfiguration,
  acledaEnvVars,
  defaultValues
}; 