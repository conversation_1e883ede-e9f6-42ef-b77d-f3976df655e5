#!/usr/bin/env node

/**
 * ElyPOS Payment Flow Test Script
 * Tests the complete subscription payment flow with ACLEDA Bank integration
 */

const API_BASE = 'http://localhost:3000';

// Test data
const testPayment = {
  amount: 35.00,
  currency: 'USD',
  description: 'Restaurant Monthly Subscription',
  companyId: '7e6eda10d92411f6994e5be7', // From your logs
  module: 'POS',
  paymentMethod: 'ACLEDA_ECOMMERCE',
  customerInfo: {
    name: 'Test Restaurant',
    email: '<EMAIL>',
    phone: '+************'
  },
  trialConversion: false
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  console.log('\n' + '='.repeat(60));
  log(title, 'cyan');
  console.log('='.repeat(60));
}

function logStep(step, message) {
  log(`[${step}] ${message}`, 'blue');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function makeRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const config = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
    ...options
  };

  log(`Making ${config.method} request to: ${url}`, 'magenta');
  
  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (response.ok) {
      logSuccess(`Response: ${response.status} ${response.statusText}`);
    } else {
      logError(`Response: ${response.status} ${response.statusText}`);
    }

    return { response, data };
  } catch (error) {
    logError(`Request failed: ${error.message}`);
    throw error;
  }
}

async function testGatewayHealth() {
  logSection('🏥 TESTING API GATEWAY HEALTH');
  
  try {
    logStep('1', 'Testing gateway root endpoint');
    const { data } = await makeRequest('/');
    logSuccess(`Gateway is running: ${data.service} v${data.version}`);
    
    logStep('2', 'Testing health endpoint');
    await makeRequest('/health');
    logSuccess('Health endpoint is accessible');
    
    return true;
  } catch (error) {
    logError('Gateway health check failed');
    return false;
  }
}

async function testSubscriptionEndpoints() {
  logSection('📦 TESTING SUBSCRIPTION ENDPOINTS');
  
  try {
    logStep('1', 'Testing subscription modules endpoint');
    const { data: modules } = await makeRequest('/subscriptions/modules');
    logSuccess(`Loaded ${modules.modules?.length || 0} modules and ${modules.posTypes?.length || 0} POS types`);
    
    logStep('2', 'Testing company subscription endpoint');
    const { data: companyData } = await makeRequest(`/subscriptions/company/${testPayment.companyId}`);
    logSuccess(`Company subscription data loaded: ${companyData.companyId || 'N/A'}`);
    
    return true;
  } catch (error) {
    logError('Subscription endpoints test failed');
    return false;
  }
}

async function testPaymentCreation() {
  logSection('💳 TESTING PAYMENT CREATION');
  
  try {
    logStep('1', 'Creating payment with ACLEDA Bank');
    log(`Request payload: ${JSON.stringify(testPayment, null, 2)}`, 'blue');
    
    const { response, data } = await makeRequest('/payments', {
      method: 'POST',
      body: JSON.stringify(testPayment)
    });
    
    log(`Response data: ${JSON.stringify(data, null, 2)}`, 'blue');
    
    if (response.ok && data.success) {
      logSuccess('Payment created successfully');
      log(`Payment ID: ${data.data.paymentId}`, 'yellow');
      log(`Amount: ${data.data.amount} ${data.data.currency}`, 'yellow');
      log(`Status: ${data.data.status}`, 'yellow');
      
      if (data.data.redirectUrl) {
        log(`Redirect URL: ${data.data.redirectUrl}`, 'yellow');
        logSuccess('ACLEDA redirect URL generated');
      } else {
        logWarning('No redirect URL (development mode simulation)');
      }
      
      return data.data;
    } else {
      logError(`Payment creation failed: ${data.message || data.error || 'Unknown error'}`);
      if (data.code) {
        log(`Error code: ${data.code}`, 'red');
      }
      if (data.details) {
        log(`Error details: ${JSON.stringify(data.details, null, 2)}`, 'red');
      }
      return null;
    }
  } catch (error) {
    logError(`Payment creation test failed: ${error.message}`);
    return null;
  }
}

async function testPaymentStatus(paymentId) {
  logSection('🔍 TESTING PAYMENT STATUS');
  
  try {
    logStep('1', 'Checking payment status');
    const { response, data } = await makeRequest(`/payments/${paymentId}/status`);
    
    if (response.ok && data.success) {
      logSuccess('Payment status retrieved successfully');
      log(`Payment ID: ${data.data.paymentId}`, 'yellow');
      log(`Status: ${data.data.status}`, 'yellow');
      log(`Amount: ${data.data.amount} ${data.data.currency}`, 'yellow');
      
      if (data.data.paidAt) {
        log(`Paid at: ${data.data.paidAt}`, 'yellow');
      }
      
      return data.data;
    } else {
      logError(`Payment status check failed: ${data.message || 'Unknown error'}`);
      return null;
    }
  } catch (error) {
    logError('Payment status test failed');
    return null;
  }
}

async function testPaymentHistory() {
  logSection('📋 TESTING PAYMENT HISTORY');
  
  try {
    logStep('1', 'Fetching payment history');
    const { response, data } = await makeRequest(`/payments/history?page=1&limit=10&companyId=${testPayment.companyId}`);
    
    if (response.ok && data.success) {
      logSuccess('Payment history retrieved successfully');
      log(`Total payments: ${data.data.totalCount}`, 'yellow');
      log(`Current page: ${data.data.currentPage}/${data.data.totalPages}`, 'yellow');
      
      if (data.data.payments && data.data.payments.length > 0) {
        log(`Recent payments:`, 'yellow');
        data.data.payments.slice(0, 3).forEach((payment, index) => {
          log(`  ${index + 1}. ${payment.paymentId} - ${payment.amount} ${payment.currency} (${payment.status})`, 'yellow');
        });
      } else {
        log('No payment history found (this is normal for new systems)', 'yellow');
      }
      
      return data.data;
    } else {
      logError(`Payment history fetch failed: ${data.message || 'Unknown error'}`);
      return null;
    }
  } catch (error) {
    logError('Payment history test failed');
    return null;
  }
}

async function runFullTest() {
  logSection('🚀 ELYPOS PAYMENT SYSTEM TEST');
  log('Testing complete subscription and payment flow with ACLEDA Bank integration', 'bright');
  
  const results = {
    gateway: false,
    subscriptions: false,
    paymentCreation: false,
    paymentStatus: false,
    paymentHistory: false
  };
  
  // Test 1: Gateway Health
  results.gateway = await testGatewayHealth();
  
  // Test 2: Subscription Endpoints
  if (results.gateway) {
    results.subscriptions = await testSubscriptionEndpoints();
  }
  
  // Test 3: Payment Creation
  let paymentData = null;
  if (results.subscriptions) {
    paymentData = await testPaymentCreation();
    results.paymentCreation = !!paymentData;
  }
  
  // Test 4: Payment Status
  if (results.paymentCreation && paymentData) {
    const statusData = await testPaymentStatus(paymentData.paymentId);
    results.paymentStatus = !!statusData;
  }
  
  // Test 5: Payment History
  results.paymentHistory = await testPaymentHistory();
  
  // Summary
  logSection('📊 TEST RESULTS SUMMARY');
  
  const tests = [
    { name: 'API Gateway Health', status: results.gateway },
    { name: 'Subscription Endpoints', status: results.subscriptions },
    { name: 'Payment Creation', status: results.paymentCreation },
    { name: 'Payment Status Check', status: results.paymentStatus },
    { name: 'Payment History', status: results.paymentHistory }
  ];
  
  tests.forEach(test => {
    if (test.status) {
      logSuccess(`${test.name}: PASSED`);
    } else {
      logError(`${test.name}: FAILED`);
    }
  });
  
  const passedTests = tests.filter(t => t.status).length;
  const totalTests = tests.length;
  
  console.log('\n' + '='.repeat(60));
  if (passedTests === totalTests) {
    logSuccess(`ALL TESTS PASSED! (${passedTests}/${totalTests})`);
    log('✨ Your ElyPOS payment system is ready for production!', 'green');
  } else {
    logWarning(`PARTIAL SUCCESS: ${passedTests}/${totalTests} tests passed`);
    log('🔧 Some components need attention before production deployment.', 'yellow');
  }
  console.log('='.repeat(60));
  
  // Next Steps
  if (paymentData && paymentData.redirectUrl) {
    console.log('\n' + '🔗 NEXT STEPS:');
    log('1. Open the following URL in your browser to complete payment:', 'cyan');
    log(`   ${paymentData.redirectUrl}`, 'blue');
    log('2. Complete the payment on ACLEDA Bank\'s payment page', 'cyan');
    log('3. You will be redirected back to /payment/success or /payment/failure', 'cyan');
    log('4. Check the payment status using the API or frontend', 'cyan');
  }
}

// Run the test
if (require.main === module) {
  runFullTest().catch(error => {
    logError(`Test runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runFullTest }; 