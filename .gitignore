# Dependencies
node_modules/
bun.lockb
package-lock.json

# Environment files
.env
.env.local
.env.development
.env.production

# Build outputs
dist/
build/
*.tsbuildinfo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
docker-compose.override.yml

# Temporary files
tmp/
temp/ 
services/core/node_modules/
services/pos/node_modules/
services/loan/node_modules/
gateway/node_modules/