# Environment Variables Configuration

This document describes all environment variables used in the ElyPOS system for proper configuration and deployment.

## Core Service (`services/core/.env`)

### Database Configuration
```env
MONGODB_URI=mongodb://localhost:27017
MONGODB_DB_NAME=elypos_core
```

### Server Configuration
```env
NODE_ENV=development|production
PORT=3001
LOG_LEVEL=DEBUG|INFO|WARN|ERROR
```

### JWT Authentication
```env
JWT_SECRET=your_super_secure_jwt_secret_key
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
```

### ACLEDA Bank Payment Integration

#### Required for Production
```env
# Environment (UAT for testing, PRODUCTION for live)
ACLEDA_ENVIRONMENT=UAT|PRODUCTION

# Merchant credentials (provided by ACLEDA Bank)
ACLEDA_MERCHANT_ID=your_merchant_id_from_acleda
ACLEDA_LOGIN_ID=your_login_id_from_acleda
ACLEDA_PASSWORD=your_password_from_acleda
ACLEDA_SIGNATURE=your_signature_from_acleda_email
ACLEDA_MERCHANT_NAME=YOUR_MERCHANT_ALIAS
```

#### UAT Configuration (Testing)
```env
ACLEDA_ENVIRONMENT=UAT
ACLEDA_MERCHANT_ID=/wRUtOaUXhK1l9JkMygbMW44ms0=
ACLEDA_LOGIN_ID=cosmouser
ACLEDA_PASSWORD=cosmouser
ACLEDA_SIGNATURE=provided_by_acleda_via_email
ACLEDA_MERCHANT_NAME=COSMOSDIGITAL

# UAT API URLs
ACLEDA_BASE_URL=https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL
ACLEDA_PAYMENT_PAGE_URL=https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/paymentPage.jsp
ACLEDA_OPEN_SESSION_URL=https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/openSessionV2
ACLEDA_GET_STATUS_URL=https://epaymentuat.acledabank.com.kh/COSMOSDIGITAL/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/getTxnStatus
```

#### Production Configuration
```env
ACLEDA_ENVIRONMENT=PRODUCTION
ACLEDA_MERCHANT_ID=your_production_merchant_id
ACLEDA_LOGIN_ID=your_production_login
ACLEDA_PASSWORD=your_production_password
ACLEDA_SIGNATURE=your_production_signature
ACLEDA_MERCHANT_NAME=YOUR_PRODUCTION_ALIAS

# Production API URLs (replace YOUR_MERCHANT_ALIAS)
ACLEDA_BASE_URL=https://epayment.acledabank.com.kh/YOUR_MERCHANT_ALIAS
ACLEDA_PAYMENT_PAGE_URL=https://epayment.acledabank.com.kh/YOUR_MERCHANT_ALIAS/paymentPage.jsp
ACLEDA_OPEN_SESSION_URL=https://epayment.acledabank.com.kh/YOUR_MERCHANT_ALIAS/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/openSessionV2
ACLEDA_GET_STATUS_URL=https://epayment.acledabank.com.kh/YOUR_MERCHANT_ALIAS/XPAYConnectorServiceInterfaceImplV2/XPAYConnectorServiceInterfaceImplV2RS/getTxnStatus
```

#### Payment Callback URLs
```env
# Development
ACLEDA_SUCCESS_URL=http://localhost:5174/payment/success
ACLEDA_ERROR_URL=http://localhost:5174/payment/failed
ACLEDA_CALLBACK_URL=http://localhost:3001/payments/callback

# Production
ACLEDA_SUCCESS_URL=https://yourdomain.com/payment/success
ACLEDA_ERROR_URL=https://yourdomain.com/payment/failed
ACLEDA_CALLBACK_URL=https://yourdomain.com/api/payments/callback
```

### Security Configuration
```env
CORS_ORIGIN=http://localhost:5174,http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Gateway Service (`gateway/.env`)

```env
# Server Configuration
NODE_ENV=development|production
PORT=3000
LOG_LEVEL=DEBUG|INFO|WARN|ERROR

# Microservices URLs
CORE_SERVICE_URL=http://localhost:3001
POS_SERVICE_URL=http://localhost:3002
LOAN_SERVICE_URL=http://localhost:3003

# Security
CORS_ORIGIN=http://localhost:5174,http://localhost:3000
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Performance
REQUEST_TIMEOUT=30000
HEALTH_CHECK_INTERVAL=30000
ENABLE_LOAD_BALANCING=false
MAX_RETRIES=3
```

## Frontend (`web/.env`)

```env
# Development
VITE_API_BASE_URL=http://localhost:3000
VITE_ENVIRONMENT=development

# Production
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_ENVIRONMENT=production
```

## Getting ACLEDA Bank Credentials

### 1. Contact ACLEDA Bank
- Email: Contact ACLEDA Bank's digital banking team
- Phone: +855 (0) 23 998 777
- Visit: ACLEDA Bank headquarters

### 2. Required Documents
- Business registration certificate
- Tax certificate
- Company profile
- Technical contact information
- Server IP addresses for whitelisting

### 3. Integration Process
1. **Application**: Submit merchant application
2. **Approval**: ACLEDA Bank reviews and approves
3. **UAT Credentials**: Receive test credentials for UAT environment
4. **Integration**: Develop and test using UAT
5. **Production Credentials**: Receive production credentials after testing
6. **Go-Live**: Switch to production configuration

### 4. Security Requirements
- Use HTTPS only in production
- Secure signature handling
- IP whitelisting at ACLEDA Bank
- Regular credential rotation

## Deployment Checklist

### Development Environment
- [ ] Copy `.env.example` to `.env`
- [ ] Set basic configuration values
- [ ] Use UAT ACLEDA Bank credentials
- [ ] Configure local MongoDB
- [ ] Set development JWT secrets

### Production Environment
- [ ] Use production ACLEDA Bank credentials
- [ ] Configure production MongoDB cluster
- [ ] Use strong JWT secrets
- [ ] Set production URLs for callbacks
- [ ] Configure proper CORS origins
- [ ] Enable appropriate logging level
- [ ] Set up environment variable management (e.g., AWS Secrets Manager, Azure Key Vault)

### Security Best Practices
- [ ] Never commit `.env` files to version control
- [ ] Use environment variable management systems in production
- [ ] Rotate JWT secrets regularly
- [ ] Monitor ACLEDA Bank signature usage
- [ ] Set up alerts for failed payments
- [ ] Regular backup of environment configurations

## Monitoring and Logging

### Environment-Specific Logging
```env
# Development - Verbose logging
LOG_LEVEL=DEBUG

# Staging - Moderate logging
LOG_LEVEL=INFO

# Production - Error focused
LOG_LEVEL=WARN
```

### ACLEDA Bank Integration Monitoring
The system automatically logs:
- Configuration validation on startup
- Payment session creation attempts
- ACLEDA Bank API call results
- Payment status updates
- Environment variable warnings

### Health Checks
All services include health check endpoints that verify:
- Database connectivity
- Environment variable presence
- External service availability
- ACLEDA Bank API reachability (in UAT/Production)

## Troubleshooting

### Common Issues

#### Missing Environment Variables
```
Error: Missing required environment variables: JWT_SECRET, ACLEDA_SIGNATURE
```
**Solution**: Set all required environment variables as documented above.

#### ACLEDA Bank Authentication Failure
```
Error: ACLEDA session creation failed: INVALID_SIGNATURE
```
**Solution**: Verify ACLEDA_SIGNATURE matches the value provided by ACLEDA Bank via email.

#### Payment Callback URL Issues
```
Error: Payment callback validation failed
```
**Solution**: Ensure ACLEDA_CALLBACK_URL is publicly accessible and matches the URL configured with ACLEDA Bank.

#### Database Connection Issues
```
Error: MongoDB connection failed
```
**Solution**: Verify MONGODB_URI is correct and the database is accessible.

### Testing Environment Variables
```bash
# Test core service configuration
cd services/core
npm run test:env

# Test gateway configuration  
cd gateway
npm run test:env

# Test ACLEDA Bank connectivity
curl -X POST http://localhost:3001/payments/test-acleda-config
``` 