# ElyPOS Frontend-Backend Integration

This document outlines the integration between the Vue.js frontend and Elysia backend services.

## 🚀 Quick Start

### 1. Start Backend Services
```bash
# From project root
make dev
```

This starts all backend services:
- **API Gateway**: http://localhost:3000
- **Core Service**: http://localhost:3001 (Auth, Companies, Subscriptions)
- **POS Service**: http://localhost:3002
- **Loan Service**: http://localhost:3003

### 2. Start Frontend Development Server
```bash
# From web directory
cd web
npm run dev
```

The frontend will be available at http://localhost:5173

## 🏗️ Architecture Overview

### Backend Services
- **API Gateway** (Port 3000): Routes requests to appropriate services
- **Core Service** (Port 3001): Authentication, companies, subscriptions, coupons
- **POS Service** (Port 3002): Point of sale operations
- **Loan Service** (Port 3003): Loan management

### Frontend Structure
```
web/src/
├── lib/
│   └── api-client.ts          # HTTP client for API communication
├── services/
│   ├── auth.service.ts        # Authentication operations
│   └── company.service.ts     # Company & subscription operations
├── stores/
│   ├── auth.ts               # Authentication state management
│   └── company.ts            # Company & subscription state
├── types/
│   ├── auth.ts               # Authentication type definitions
│   └── company.ts            # Company & subscription types
└── views/
    ├── auth/                 # Login, Register pages
    └── dashboard/            # Protected dashboard pages
```

## 🔌 API Integration

### Authentication Flow
1. User logs in via `/auth/login`
2. Backend returns JWT tokens
3. Frontend stores tokens and sets Authorization header
4. Protected routes require authentication

### State Management (Pinia)
- **Auth Store**: User session, login/logout, token management
- **Company Store**: Current company, subscriptions, billing

### API Clients
- **Primary**: API Gateway (http://localhost:3000)
- **Direct**: Individual services for specific operations

## 🎯 Key Features Implemented

### ✅ Authentication System
- Login/Register forms with validation
- JWT token management
- Automatic token refresh
- Route protection (auth guards)
- User session persistence

### ✅ API Client Architecture
- Centralized HTTP client with interceptors
- Error handling and loading states
- TypeScript type safety
- Multiple service endpoints

### ✅ State Management
- Pinia stores for auth and company data
- Reactive state updates
- Persistent storage integration
- Computed properties for derived state

### ✅ Type Safety
- Shared TypeScript interfaces
- API response typing
- Form validation schemas
- Component prop types

## 🔐 Environment Configuration

Create `web/.env`:
```env
# API Configuration
VITE_API_GATEWAY_URL=http://localhost:3000
VITE_CORE_API_URL=http://localhost:3001
VITE_POS_API_URL=http://localhost:3002
VITE_LOAN_API_URL=http://localhost:3003

# App Configuration
VITE_APP_TITLE=ElyPOS - Point of Sale & Business Management
VITE_APP_DESCRIPTION=Comprehensive business management solution
```

## 📱 Available Routes

### Public Routes
- `/` - Landing page
- `/auth/login` - User login
- `/auth/register` - User registration

### Protected Routes (Require Authentication)
- `/dashboard` - Main dashboard
- `/dashboard/projects` - Project management
- `/dashboard/tasks` - Task management  
- `/dashboard/calendar` - Calendar view
- `/dashboard/settings` - User settings

## 🛠️ Development Workflow

### Adding New API Endpoints
1. Add method to appropriate service class (`auth.service.ts`, `company.service.ts`)
2. Update TypeScript types if needed
3. Add to Pinia store if state management required
4. Use in Vue components

### Adding New Components
1. Create component in `src/components/`
2. Import and use in views
3. Add proper TypeScript typing
4. Follow Vue 3 Composition API patterns

## 🧪 Testing the Integration

### Test Authentication
1. Start backend: `make dev`
2. Start frontend: `cd web && npm run dev`
3. Navigate to http://localhost:5173/auth/login
4. Try logging in with test credentials

### Test API Communication
```bash
# Test backend health
curl http://localhost:3001/health

# Test authentication endpoint
curl -X POST http://localhost:3001/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

## 🎨 UI Components

The frontend uses **reka-ui** (Vue version of shadcn):
- Pre-built, accessible components
- TailwindCSS styling
- Dark mode support
- Form validation integration

## 📦 Key Dependencies

### Frontend
- **Vue 3** - Framework
- **Pinia** - State management
- **Vue Router** - Routing
- **TailwindCSS** - Styling
- **reka-ui** - UI components
- **VeeValidate + Zod** - Form validation
- **TypeScript** - Type safety

### Backend
- **Elysia** - Web framework
- **MongoDB** - Database
- **JWT** - Authentication
- **Bun** - Runtime

## 🚧 Next Steps

### Immediate Tasks
1. Implement company creation flow
2. Add subscription management UI
3. Create billing dashboard
4. Add POS interface
5. Implement loan management

### Future Enhancements
1. Real-time updates (WebSockets)
2. Offline support (PWA)
3. Mobile responsive design
4. Advanced reporting
5. Multi-language support

## 🐛 Troubleshooting

### Common Issues
1. **CORS Errors**: Ensure backend CORS is configured for frontend origin
2. **401 Unauthorized**: Check JWT token validity and refresh logic
3. **Connection Refused**: Verify backend services are running
4. **TypeScript Errors**: Ensure path aliases are configured correctly

### Debug Commands
```bash
# Check backend services
make status

# View backend logs
tmux attach -t elypos-dev

# Check frontend build
cd web && npm run build

# Type check frontend
cd web && npm run type-check
```

## 📚 Resources

- [Vue 3 Documentation](https://vuejs.org/guide/)
- [Pinia Documentation](https://pinia.vuejs.org/)
- [TailwindCSS Documentation](https://tailwindcss.com/docs)
- [Elysia Documentation](https://elysiajs.com/)
- [reka-ui Documentation](https://www.reka-ui.com/)

---

**Happy coding! 🎉** 