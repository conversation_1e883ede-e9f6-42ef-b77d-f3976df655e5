# 🚀 ElyPOS Tmux Development Guide

## Quick Start

Start all services with hot reload:
```bash
make dev
```

This will:
1. Kill any existing `elypos-dev` tmux session
2. Create a new session with 3 windows:
   - **core**: Core authentication and company services
   - **modules**: Split window with Loan (top) and POS (bottom) services  
   - **gateway**: API Gateway with service routing
3. Auto-attach to the session

## 🎛️ Tmux Navigation

### Window Navigation
- `Ctrl+b + 0` - Switch to core window
- `Ctrl+b + 1` - Switch to modules window  
- `Ctrl+b + 2` - Switch to gateway window
- `Ctrl+b + n` - Next window
- `Ctrl+b + p` - Previous window

### Pane Navigation (in modules window)
- `Ctrl+b + ↑` - Switch to loan service pane (top)
- `Ctrl+b + ↓` - Switch to POS service pane (bottom)
- `Ctrl+b + o` - Switch between panes

### Session Management
- `Ctrl+b + d` - Detach from session (services keep running)
- `tmux attach -t elypos-dev` - Reattach to session
- `tmux list-sessions` - List all tmux sessions

## 📋 Available Make Commands

### Development
```bash
make dev          # Start all services with tmux and hot reload
make stop         # Stop all running services  
make restart      # Restart all services
make logs         # Attach to tmux session to view logs
make status       # Check service status
```

### Building & Testing
```bash
make install      # Install all dependencies
make build        # Build all services
make clean        # Clean build artifacts  
make test         # Run API health tests
```

### Docker
```bash
make docker-dev   # Start with Docker Compose
make docker-stop  # Stop Docker services
```

### Utilities
```bash
make help         # Show help message
```

## 🔍 Service Layout

```
┌─ Window 0: core ─────────────────────────┐
│ Core Service (Port 3001)                 │
│ - Authentication                         │
│ - User management                        │
│ - Company management                     │
└──────────────────────────────────────────┘

┌─ Window 1: modules ──────────────────────┐
│ ┌─ Loan Service (Port 3003) ────────────┐│
│ │ - Loan applications                   ││
│ │ - Repayments                          ││
│ │ - Payment schedules                   ││
│ └───────────────────────────────────────┘│
│ ┌─ POS Service (Port 3002) ─────────────┐│
│ │ - Inventory management                ││
│ │ - Sales processing                    ││
│ │ - Invoice generation                  ││
│ └───────────────────────────────────────┘│
└──────────────────────────────────────────┘

┌─ Window 2: gateway ──────────────────────┐
│ API Gateway (Port 3000)                  │
│ - Request routing                        │
│ - Service discovery                      │
│ - API documentation                      │
└──────────────────────────────────────────┘
```

## 🌐 Service Endpoints

| Service | Port | Health Check | Swagger Docs |
|---------|------|--------------|--------------|
| Gateway | 3000 | `/api/health` | `/swagger` |
| Core    | 3001 | `/health` | `/swagger` |
| POS     | 3002 | `/health` | `/swagger` |
| Loan    | 3003 | `/health` | `/swagger` |

## 📱 Gateway Proxy Routes

All services are accessible through the gateway:
- `http://localhost:3000/api/core/*` → Core Service
- `http://localhost:3000/api/pos/*` → POS Service  
- `http://localhost:3000/api/loan/*` → Loan Service

## 🐛 Debugging Tips

### View Service Logs
```bash
# Attach to tmux session
tmux attach -t elypos-dev

# Or use make command
make logs
```

### Check Individual Service
```bash
# Check specific service health
curl http://localhost:3001/health  # Core
curl http://localhost:3002/health  # POS
curl http://localhost:3003/health  # Loan

# Or check all at once
make status
```

### Restart Services
```bash
# Restart all services
make restart

# Or restart individual service (in tmux)
# 1. Switch to service window
# 2. Ctrl+C to stop
# 3. ↑ arrow to get last command
# 4. Enter to restart
```

### Kill Stuck Sessions
```bash
# Force kill session
tmux kill-session -t elypos-dev

# Then restart
make dev
```

## 🔧 Customization

### Add New Service Window
Edit `Makefile` and add:
```makefile
@tmux new-window -t elypos-dev -n "newservice" "cd services/newservice && bun run dev"
```

### Change Window Layout
The modules window uses `even-vertical` layout. Available layouts:
- `even-horizontal`
- `even-vertical`  
- `main-horizontal`
- `main-vertical`
- `tiled`

### Modify Ports
Update ports in service `.env` files and corresponding Makefile references.

## 🚨 Troubleshooting

### Port Already in Use
```bash
# Find process using port
lsof -i :3000

# Kill process
kill -9 <PID>
```

### Service Won't Start
```bash
# Check dependencies
make install

# Check environment variables
cat services/core/.env

# View detailed logs in tmux
make logs
```

### Database Connection Issues
```bash
# Check MongoDB is running
brew services list | grep mongodb

# Start MongoDB
brew services start mongodb-community

# Or use Docker
docker run -d -p 27017:27017 mongo:7.0
```

---

**Happy coding with ElyPOS! 🎉** 