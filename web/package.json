{"name": "muxi_ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@neodrag/vue": "^2.3.1", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.10.0", "google-fonts-helper": "^3.7.3", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.3", "reka-ui": "^2.3.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-i18n": "9", "vue-router": "^4.5.1", "vue-sonner": "^2.0.1", "zod": "^3.25.74"}, "devDependencies": {"@playwright/test": "^1.53.1", "@tsconfig/node22": "^22.0.2", "@types/node": "^24.0.10", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-playwright": "^2.2.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "oxlint": "~1.1.0", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}