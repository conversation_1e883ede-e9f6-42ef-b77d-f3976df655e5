import https from 'https'
import { writeFileSync, mkdirSync } from 'fs'
import { join } from 'path'

async function downloadFile(url, filepath) {
  return new Promise((resolve, reject) => {
    const file = writeFileSync
    https
      .get(url, (response) => {
        let data = ''
        response.on('data', (chunk) => {
          data += chunk
        })
        response.on('end', () => {
          writeFileSync(filepath, data)
          resolve(data)
        })
      })
      .on('error', (err) => {
        reject(err)
      })
  })
}

async function downloadFonts() {
  try {
    // Create fonts directory
    mkdirSync('src/assets/fonts/noto-sans-khmer', { recursive: true })

    console.log('📥 Downloading Noto Sans Khmer font...')

    // Download Noto Sans Khmer CSS
    const fontUrl =
      'https://fonts.googleapis.com/css2?family=Noto+Sans+Khmer:wght@300;400;500;600;700&display=swap'
    const cssContent = await downloadFile(
      fontUrl,
      'src/assets/fonts/noto-sans-khmer/noto-sans-khmer.css',
    )

    console.log('✅ Noto Sans Khmer font CSS downloaded successfully!')
    console.log(
      `📁 Saved to: src/assets/fonts/noto-sans-khmer/noto-sans-khmer.css (${cssContent.length} chars)`,
    )

    // Also create a local CSS file with proper font family
    const localCSS = `
/* Noto Sans Khmer Font */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Khmer:wght@300;400;500;600;700&display=swap');

.font-khmer {
  font-family: 'Noto Sans Khmer', sans-serif;
}

/* Apply Khmer font when language is Khmer */
html[lang="km"],
html[lang="km"] * {
  font-family: 'Noto Sans Khmer', sans-serif;
}
`

    writeFileSync('src/assets/fonts/noto-sans-khmer/khmer-fonts.css', localCSS)
    console.log('✅ Local Khmer font CSS created!')
  } catch (error) {
    console.error('❌ Error downloading fonts:', error)
  }
}

downloadFonts()
