<script setup lang="ts">
import { onMounted } from 'vue'
import FloatingControls from './components/FloatingControlsCompact.vue'
import LoadingScreen from './components/LoadingScreen.vue'
import { useTheme } from './composables/useTheme'
import { useAuthStore } from './stores/auth'
import { Toaster } from 'vue-sonner'

const authStore = useAuthStore()

// Initialize theme system
useTheme()

// Initialize auth when app mounts
onMounted(async () => {
  await authStore.initializeAuth()
})
</script>

<template>
  <div id="app" class="relative">
    <!-- Loading Screen during initialization -->
    <LoadingScreen
      v-if="authStore.isInitializing"
      :title="authStore.loadingMessage || 'Loading...'"
      subtitle="Please wait while we prepare your workspace"
    />

    <!-- Main App Content -->
    <template v-else>
      <!-- Main Content -->
      <main class="relative">
        <RouterView />
      </main>
      <!-- Unified Floating Controls -->
      <FloatingControls />
    </template>

    <!-- Toast Notifications -->
    <Toaster
      position="top-right"
      :richColors="true"
      :theme="'light'"
      :closeButton="true"
      :duration="4000"
    />
  </div>
</template>
