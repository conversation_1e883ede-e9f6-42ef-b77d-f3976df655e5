<script setup lang="ts">
import { RouterView, RouterLink } from 'vue-router'
</script>

<template>
  <div class="relative min-h-screen">
    <!-- Grid Background -->
    <div class="fixed inset-0 bg-[#fafafa] -z-10">
      <div
        class="absolute inset-0"
        style="
          background-image: radial-gradient(circle at center, #e2e8f0 1px, transparent 1px);
          background-size: 40px 40px;
        "
      ></div>
      <div
        class="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-blue-50/30"
      ></div>
    </div>

    <!-- Simple Header -->
    <header class="bg-white/0 backdrop-blur-sm sticky top-0 z-50 border-b border-gray-100">
      <div class="max-w-7xl mx-auto px-4 sm:px-6">
        <nav class="flex justify-between items-center h-16">
          <RouterLink to="/" class="flex items-center gap-2">
            <div class="w-8 h-8 bg-[#1C1C1C] rounded-lg flex items-center justify-center">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M15.5 9L15.6716 9.17157C17.0049 10.5049 17.0049 12.6716 15.6716 14.0049L15.5 14.1716M15.5 9L13.9393 7.43934C13.1512 6.65118 11.8488 6.65118 11.0607 7.43934L7.43934 11.0607C6.65118 11.8488 6.65118 13.1512 7.43934 13.9393L9 15.5M15.5 9L19.5 5M9 15.5L9.17157 15.6716C10.5049 17.0049 12.6716 17.0049 14.0049 15.6716L14.1716 15.5M9 15.5L5 19.5"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <span class="text-[#1C1C1C] text-lg font-semibold tracking-tight">Manageko.</span>
          </RouterLink>
        </nav>
      </div>
    </header>

    <!-- Main Content -->
    <main class="relative">
      <RouterView />
    </main>
  </div>
</template>
