<script setup lang="ts">
import { RouterView } from 'vue-router'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'
import { useLanguage } from '@/composables/useLanguage'
import { ref } from 'vue'

const { t } = useLanguage()
const showMobileMenu = ref(false)

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}
</script>

<template>
  <div class="relative min-h-screen">
    <!-- Grid Background -->
    <div class="fixed inset-0 bg-[#fafafa] -z-10">
      <div
        class="absolute inset-0"
        style="
          background-image: radial-gradient(circle at center, #e2e8f0 1px, transparent 1px);
          background-size: 40px 40px;
        "
      ></div>
      <div
        class="absolute inset-0 bg-gradient-to-br from-white/50 via-transparent to-blue-50/30"
      ></div>
    </div>

    <!-- Floating Header/Navigation -->
    <header class="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 w-fit">
      <div
        class="bg-white/90 backdrop-blur-md border border-gray-200/50 rounded-full shadow-lg shadow-black/5"
      >
        <nav class="flex items-center px-6 py-3">
          <!-- Logo -->
          <RouterLink to="/" class="flex items-center gap-2 mr-8">
            <div class="w-8 h-8 bg-[#1C1C1C] rounded-lg flex items-center justify-center">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="white" opacity="0.8" />
                <path
                  d="M2 17L12 22L22 17"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M2 12L12 17L22 12"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <span class="text-[#1C1C1C] font-semibold text-lg tracking-tight">Manageko.</span>
          </RouterLink>

          <!-- Navigation Menu -->
          <div class="hidden md:flex items-center gap-8 mr-8">
            <a
              href="#"
              class="text-[#1C1C1C] text-[15px] font-medium hover:text-black transition-colors"
            >
              {{ t('navigation.features') }}
            </a>
            <a
              href="#"
              class="text-[#1C1C1C]/70 text-[15px] font-medium hover:text-black transition-colors"
            >
              {{ t('navigation.useCase') }}
            </a>
            <a
              href="#"
              class="text-[#1C1C1C]/70 text-[15px] font-medium hover:text-black transition-colors"
            >
              {{ t('navigation.pricing') }}
            </a>
            <a
              href="#"
              class="text-[#1C1C1C]/70 text-[15px] font-medium hover:text-black transition-colors"
            >
              {{ t('navigation.blogs') }}
            </a>
          </div>

          <!-- Action Buttons -->
          <div class="hidden md:flex items-center gap-3">
            <RouterLink
              to="/auth/login"
              class="text-[#1C1C1C]/70 text-[15px] font-medium py-2 px-4 hover:text-black transition-colors"
            >
              {{ t('common.login') }}
            </RouterLink>
            <RouterLink
              to="/auth/register"
              class="bg-[#1C1C1C] text-white text-[15px] font-medium py-2 px-4 rounded-full hover:bg-black transition-colors"
            >
              {{ t('common.getStarted') }}
            </RouterLink>
          </div>

          <!-- Mobile Menu Button -->
          <button
            @click="toggleMobileMenu"
            class="md:hidden ml-4 p-2 text-[#1C1C1C] hover:text-black transition-colors"
          >
            <DuotoneIcon :name="showMobileMenu ? 'x' : 'menu'" size="sm" />
          </button>
        </nav>
      </div>

      <!-- Mobile Menu -->
      <div
        v-if="showMobileMenu"
        class="md:hidden absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-md border border-gray-200/50 rounded-2xl shadow-lg shadow-black/5 p-6"
      >
        <div class="flex flex-col space-y-4">
          <a
            href="#"
            class="text-[#1C1C1C] text-[15px] font-medium hover:text-black transition-colors"
            @click="closeMobileMenu"
          >
            {{ t('navigation.features') }}
          </a>
          <a
            href="#"
            class="text-[#1C1C1C]/70 text-[15px] font-medium hover:text-black transition-colors"
            @click="closeMobileMenu"
          >
            {{ t('navigation.useCase') }}
          </a>
          <a
            href="#"
            class="text-[#1C1C1C]/70 text-[15px] font-medium hover:text-black transition-colors"
            @click="closeMobileMenu"
          >
            {{ t('navigation.pricing') }}
          </a>
          <a
            href="#"
            class="text-[#1C1C1C]/70 text-[15px] font-medium hover:text-black transition-colors"
            @click="closeMobileMenu"
          >
            {{ t('navigation.blogs') }}
          </a>
          <hr class="border-gray-200" />
          <div class="flex items-center justify-between">
            <span class="text-[#1C1C1C] text-[15px] font-medium">{{
              t('theme.themeSettings')
            }}</span>
            <ThemeSwitcher />
          </div>
          <hr class="border-gray-200" />
          <RouterLink
            to="/auth/login"
            class="text-[#1C1C1C]/70 text-[15px] font-medium hover:text-black transition-colors"
            @click="closeMobileMenu"
          >
            {{ t('common.login') }}
          </RouterLink>
          <RouterLink
            to="/auth/register"
            class="bg-[#1C1C1C] text-white text-[15px] font-medium py-2 px-4 rounded-full hover:bg-black transition-colors text-center"
            @click="closeMobileMenu"
          >
            {{ t('common.getStarted') }}
          </RouterLink>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="">
      <RouterView />
    </main>

    <!-- Fixed Floating Language Switcher -->
    <!-- <FixedLanguageSwitcher /> -->
  </div>
</template>
