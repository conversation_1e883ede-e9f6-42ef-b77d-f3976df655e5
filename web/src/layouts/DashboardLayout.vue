<script setup lang="ts">
import { RouterView, RouterLink, useRoute } from 'vue-router'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { computed, ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import DuotoneIcon from '../components/DuotoneIcon.vue'
import CompanySwitcher from '../components/CompanySwitcher.vue'
import UserMenu from '../components/UserMenu.vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Header state
const showMobileMenu = ref(false)

// Navigation items matching the screenshot
const navigationItems = [
  { name: 'Dashboard', to: '/dashboard/modules', icon: 'home' },
  { name: 'Products', to: '/dashboard/products', icon: 'package' },
  { name: 'Categories', to: '/dashboard/categories', icon: 'folder' },
  { name: 'Price Levels', to: '/dashboard/price-levels', icon: 'tag' },
  // { name: 'Calendar', to: '/dashboard/calendar', icon: 'calendar' },
  // { name: 'Finances', to: '/dashboard/payment-history', icon: 'money' },
  { name: 'Managers', to: '/dashboard/companies', icon: 'users' },
  // { name: 'Projects', to: '/dashboard/projects', icon: 'folder' },
]

// Update active state based on current route
const activeNavigationItems = computed(() => {
  return navigationItems.map(item => ({
    ...item,
    active: route.path === item.to || route.path.startsWith(item.to + '/')
  }))
})

// Check if settings is active
const isSettingsActive = computed(() => {
  return route.path === '/dashboard/settings' || route.path.startsWith('/dashboard/settings/')
})

// Current user info
const currentUser = computed(() => authStore.currentUser)

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

// Close mobile menu when route changes
watch(route, () => {
  showMobileMenu.value = false
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Enhanced Dashboard Header -->
    <header class="bg-gray-900 px-4 sm:px-6 py-4 sticky top-0 z-50 shadow-lg">
      <div class="flex items-center justify-between max-wfull mx-auto">

        <!-- Left Section: Brand & Company Switcher -->
        <div class="flex items-center gap-4 lg:gap-6">
          <!-- Compact Brand Section -->


          <!-- Company Switcher -->
          <div class="hidden md:block">
            <CompanySwitcher />
          </div>

          <!-- User Avatar (Mobile) -->
          <div class="sm:hidden">
            <img
              :src="currentUser?.profilePicture || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80'"
              :alt="currentUser?.firstName + ' ' + currentUser?.lastName"
              class="w-8 h-8 rounded-full object-cover border border-gray-600"
            />
          </div>
        </div>

        <!-- Center Section: Navigation Tabs -->
        <nav class="hidden lg:flex items-center bg-gray-800 rounded-2xl p-1 space-x-1" >
          <RouterLink
            v-for="item in activeNavigationItems"
            :key="item.to"
            :to="item.to"
            class="px-4 xl:px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 whitespace-nowrap flex items-center gap-2"
            :class="item.active
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-300 hover:text-white hover:bg-gray-700'"
          >
            <DuotoneIcon :name="item.icon" size="sm" />
            {{ item.name }}
          </RouterLink>
        </nav>

        <!-- Right Section: Actions & Settings -->
        <div class="flex items-center gap-2 lg:gap-3">
          <!-- Quick Actions (Desktop) -->
          <!-- <div class="hidden xl:flex items-center gap-2">
            <Button variant="ghost" size="sm" class="text-gray-400 hover:text-white hover:bg-gray-800">
              <DuotoneIcon name="search" size="sm" />
            </Button>
            <Button variant="ghost" size="sm" class="text-gray-400 hover:text-white hover:bg-gray-800">
              <DuotoneIcon name="notification" size="sm" />
            </Button>
          </div> -->

          <!-- Mobile Menu Button -->
          <Button
            variant="ghost"
            size="sm"
            class="lg:hidden text-gray-400 hover:text-white hover:bg-gray-800"
            @click="toggleMobileMenu"
          >
            <DuotoneIcon name="menu" size="sm" />
          </Button>

          <!-- Settings Button with Active State -->
          <!-- <RouterLink to="/dashboard/settings">
            <Button
              variant="ghost"
              size="sm"
              :class="isSettingsActive
                ? 'bg-purple-600 text-white hover:bg-purple-700'
                : 'text-gray-400 hover:text-white hover:bg-gray-800'"
            >
              <DuotoneIcon name="setting" :size="isSettingsActive ? 'md' : 'sm'" />
            </Button>
          </RouterLink> -->

          <!-- User Menu (Desktop) -->
          <div class="hidden sm:block lg:ml-2">
            <UserMenu />
          </div>
        </div>
      </div>

            <!-- Mobile Navigation -->
      <div v-if="showMobileMenu" class="lg:hidden mt-4 pt-4 border-t border-gray-700">
        <!-- Company Switcher (Mobile) -->
        <div class="px-4 pb-4 md:hidden">
          <div class="text-xs text-gray-400 mb-2 font-medium">Switch Company</div>
          <CompanySwitcher />
        </div>

                <div class="border-t border-gray-700 pt-4">
          <nav class="space-y-1">
            <RouterLink
              v-for="item in activeNavigationItems"
              :key="item.to"
              :to="item.to"
              class="flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors"
              :class="item.active
                ? 'bg-white text-gray-900'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'"
              @click="showMobileMenu = false"
            >
              <DuotoneIcon :name="item.icon" size="sm" />
              {{ item.name }}
            </RouterLink>

            <!-- Settings in Mobile Menu -->
            <RouterLink
              to="/dashboard/settings"
              class="flex items-center gap-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors"
              :class="isSettingsActive
                ? 'bg-purple-600 text-white'
                : 'text-gray-300 hover:text-white hover:bg-gray-700'"
              @click="showMobileMenu = false"
            >
              <DuotoneIcon name="setting" size="sm" />
              Settings
            </RouterLink>
          </nav>

          <!-- User Menu (Mobile) -->
          <div class="border-t border-gray-700 mt-4 pt-4">
            <div class="text-xs text-gray-400 mb-2 font-medium px-4">User Menu</div>
            <div class="px-4">
              <UserMenu />
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1">
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
@reference "tailwindcss";

/* Custom gradient for brand section */
.bg-gradient-to-br {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

/* Smooth transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Sticky header backdrop */
header {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Focus states for accessibility */
.router-link-active {
  @apply bg-white text-gray-900 shadow-sm;
}

/* Hover effects */
button:hover, a:hover {
  transform: translateY(-1px);
}
</style>

