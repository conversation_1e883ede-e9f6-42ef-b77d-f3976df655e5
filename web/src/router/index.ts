import { createRouter, createWebHistory } from 'vue-router'
import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import DefaultLayout from '@/layouts/DefaultLayout.vue'
import AuthLayout from '@/layouts/AuthLayout.vue'
import DashboardLayout from '@/layouts/DashboardLayout.vue'
import HomeView from '@/views/HomeView.vue'
import IconDemo from '@/views/IconDemo.vue'
import { useAuthStore } from '@/stores/auth'
import { useRestaurantAccess } from '@/composables/useRestaurantAccess'

// Auth guard
const requireAuth = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  const authStore = useAuthStore()

  if (authStore.isAuthenticated) {
    next()
  } else {
    next('/auth/login')
  }
}

// Guest guard (for auth pages)
const requireGuest = (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  const authStore = useAuthStore()

  if (!authStore.isAuthenticated) {
    next()
  } else {
    next('/dashboard/modules')
  }
}

// Restaurant access guard
const requireRestaurantAccess = async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  const { checkRestaurantAccess } = useRestaurantAccess()

  try {
    const access = await checkRestaurantAccess()

    if (access.hasAccess) {
      next()
    } else {
      // Redirect to billing page with restaurant context
      next({
        path: '/dashboard/billing',
        query: {
          module: 'POS',
          type: 'RESTAURANT',
          reason: access.isExpired ? 'trial_expired' : 'subscription_required',
          returnTo: to.fullPath
        }
      })
    }
  } catch (error) {
    console.error('Restaurant access check failed:', error)
    // Redirect to modules page on error
    next('/dashboard/modules')
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: DefaultLayout,
      children: [
        {
          path: '',
          name: 'home',
          component: HomeView,
        },
        {
          path: 'icons',
          name: 'icons',
          component: IconDemo,
        },
      ],
    },
    {
      path: '/auth',
      component: AuthLayout,
      beforeEnter: requireGuest,
      children: [
        {
          path: 'login',
          name: 'login',
          component: () => import('@/views/auth/LoginView.vue'),
        },
        {
          path: 'register',
          name: 'register',
          component: () => import('@/views/auth/RegisterView.vue'),
        },
      ],
    },
    {
      path: '/dashboard',
      component: DashboardLayout,
      beforeEnter: requireAuth,
      children: [
        {
          path: '',
          name: 'dashboard',
          redirect: '/dashboard/modules',
        },
        {
          path: 'projects',
          name: 'projects',
          component: () => import('@/views/dashboard/ProjectsView.vue'),
        },
        {
          path: 'tasks',
          name: 'tasks',
          component: () => import('@/views/dashboard/TasksView.vue'),
        },
        {
          path: 'calendar',
          name: 'calendar',
          component: () => import('@/views/dashboard/CalendarView.vue'),
        },
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('../views/dashboard/SettingsView.vue'),
        },
        {
          path: 'companies',
          name: 'Companies',
          component: () => import('../views/dashboard/CompaniesView.vue'),
        },
        {
          path: 'modules',
          name: 'modules',
          component: () => import('@/views/dashboard/ModulesView.vue'),
        },
        {
          path: 'payment-history',
          name: 'payment-history',
          component: () => import('@/views/dashboard/PaymentHistoryView.vue'),
        },
        {
          path: 'categories',
          name: 'categories',
          component: () => import('@/views/dashboard/CategoryView.vue'),
        },
        {
          path: 'price-levels',
          name: 'price-levels',
          component: () => import('@/views/dashboard/PriceLevelView.vue'),
        },
        {
          path: 'products',
          name: 'products',
          component: () => import('@/views/dashboard/ProductsView.vue'),
        },
        {
          path: 'pos/restaurant',
          name: 'restaurant-pos',
          component: () => import('@/views/dashboard/pos/RestaurantView.vue'),
          beforeEnter: requireRestaurantAccess,
        },
        {
          path: 'pos/qr-menus',
          name: 'qr-menus',
          component: () => import('@/views/dashboard/pos/QRMenusView.vue'),
          beforeEnter: requireRestaurantAccess,
        },
        {
          path: 'pos/orders',
          name: 'orders',
          component: () => import('@/views/dashboard/pos/OrdersView.vue'),
          beforeEnter: requireRestaurantAccess,
        },
        {
          path: 'pos/kitchen',
          name: 'kitchen',
          component: () => import('@/views/dashboard/pos/KitchenView.vue'),
          beforeEnter: requireRestaurantAccess,
        },
        {
          path: 'billing',
          name: 'billing',
          component: () => import('@/views/dashboard/BillingView.vue'),
        },
      ],
    },
    // Payment routes (outside dashboard to avoid auth issues during payment flow)
    {
      path: '/payment',
      children: [
        {
          path: 'success',
          name: 'payment-success',
          component: () => import('@/views/PaymentSuccessView.vue'),
        },
        {
          path: 'failure',
          name: 'payment-failure',
          component: () => import('@/views/PaymentFailureView.vue'),
        },
      ],
    },
  ],
})

export default router
