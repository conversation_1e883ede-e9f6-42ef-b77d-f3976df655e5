export type PosBusinessType =
  | 'RESTAURANT'
  | 'BAR'
  | 'BAKERY'
  | 'RETAIL_SHOP'
  | 'CLOTHING_STORE'
  | 'FURNITURE_STORE'
  | 'PHARMACY'
  | 'ELECTRONICS_STORE'
  | 'GROCERY_STORE'
  | 'BEAUTY_SALON'
  | 'SERVICE'
  | 'HOTEL'
  | 'GENERIC';

export interface Company {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  ownerId: string;
  foundedDate?: string;
  businessType: 'sole_proprietorship' | 'partnership' | 'private_limited' | 'public_limited' | 'branch_office' | 'representative_office' | 'ngo' | 'cooperative';
  businessLicense?: string;
  taxId?: string;
  vatNumber?: string;
  address?: {
    street?: string;
    commune?: string;
    district?: string;
    province?: string;
    postalCode?: string;
    country?: string;
  };
  bankInfo?: {
    bankName: string;
    accountNumber: string;
    accountName: string;
    swift?: string;
  };
  isActive: boolean;
  settings: {
    timezone?: string;
    currency?: 'KHR' | 'USD';
    language?: 'en' | 'km' | 'both';
    taxRate?: number;
    witholdingTaxRate?: number;
    dateFormat?: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
    numberFormat?: 'US' | 'EU' | 'KH';
  };
  createdAt: string;
  updatedAt: string;
}

export interface AppliedCoupon {
  couponId: string;
  couponCode: string;
  discountAmount: number;
  appliedAt: string;
}

export interface CompanySubscription {
  _id: string;
  companyId: string;
  module: 'POS' | 'LOAN' | 'ACCOUNTING' | 'ERP';
  active: boolean;
  price: number;
  originalPrice: number;
  currency: 'KHR' | 'USD';
  nextBillingDate: string;
  posType?: PosBusinessType;
  subscribedAt: string;
  lastBilledAt?: string;
  isTrialMode: boolean;
  trialStartDate?: string;
  trialEndDate?: string;
  trialDuration: number;
  appliedCoupons: AppliedCoupon[];
  createdAt: string;
  updatedAt: string;
}

export interface TrialInfo {
  companyId: string;
  module: string;
  posType?: PosBusinessType;
  isInTrial: boolean;
  trialStartDate?: string;
  trialEndDate?: string;
  daysRemaining?: number;
  canStartTrial: boolean;
  trialsUsed: number;
  trialsRemaining: number;
}

export interface BillingInfo {
  companyId: string;
  totalMonthlyAmount: number;
  totalMonthlyAmountUSD?: number;
  originalAmount: number;
  totalDiscount: number;
  currency: 'KHR' | 'USD';
  activeModules: Array<{
    module: string;
    price: number;
    originalPrice: number;
    discount: number;
    appliedCoupons: AppliedCoupon[];
    posType?: PosBusinessType;
  }>;
  nextBillingDate: string;
  lastBilledAmount?: number;
  lastBilledDate?: string;
}

export interface CreateCompanyRequest {
  name: string;
  email: string;
  ownerId: string;
  phone?: string;
  businessType?: Company['businessType'];
  businessLicense?: string;
  taxId?: string;
  vatNumber?: string;
  address?: Company['address'];
  bankInfo?: Company['bankInfo'];
  settings?: Partial<Company['settings']>;
}

export interface UpdateCompanyRequest extends Partial<Omit<CreateCompanyRequest, 'ownerId'>> {}

export interface SubscribeRequest {
  module: 'POS' | 'LOAN' | 'ACCOUNTING' | 'ERP';
  posType?: PosBusinessType;
  currency?: 'KHR' | 'USD';
  startTrial?: boolean;
  couponCode?: string;
}

export interface StartTrialRequest {
  module: 'POS' | 'LOAN' | 'ACCOUNTING' | 'ERP';
  posType?: PosBusinessType;
  trialDuration?: number;
}
