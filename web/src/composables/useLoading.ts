import { ref, computed } from 'vue'

interface LoadingState {
  id: string
  message?: string
  progress?: number
}

const loadingStates = ref<LoadingState[]>([])

export function useLoading() {
  const startLoading = (id: string, message?: string, progress?: number): void => {
    const existingIndex = loadingStates.value.findIndex(state => state.id === id)

    if (existingIndex >= 0) {
      // Update existing loading state
      loadingStates.value[existingIndex] = { id, message, progress }
    } else {
      // Add new loading state
      loadingStates.value.push({ id, message, progress })
    }
  }

  const updateLoading = (id: string, message?: string, progress?: number): void => {
    const existingIndex = loadingStates.value.findIndex(state => state.id === id)

    if (existingIndex >= 0) {
      loadingStates.value[existingIndex] = {
        ...loadingStates.value[existingIndex],
        ...(message !== undefined && { message }),
        ...(progress !== undefined && { progress })
      }
    }
  }

  const stopLoading = (id: string): void => {
    const index = loadingStates.value.findIndex(state => state.id === id)
    if (index >= 0) {
      loadingStates.value.splice(index, 1)
    }
  }

  const isLoading = computed(() => loadingStates.value.length > 0)

  const getLoadingState = (id: string) => {
    return computed(() => loadingStates.value.find(state => state.id === id))
  }

  const getGlobalLoadingMessage = computed(() => {
    const state = loadingStates.value[0]
    return state?.message || 'Loading...'
  })

  const clearAllLoading = (): void => {
    loadingStates.value = []
  }

  // Helper for async operations
  const withLoading = async <T>(
    id: string,
    operation: () => Promise<T>,
    message?: string
  ): Promise<T> => {
    startLoading(id, message)
    try {
      return await operation()
    } finally {
      stopLoading(id)
    }
  }

  return {
    // State
    loadingStates: computed(() => loadingStates.value),
    isLoading,
    getGlobalLoadingMessage,

    // Actions
    startLoading,
    updateLoading,
    stopLoading,
    getLoadingState,
    clearAllLoading,
    withLoading
  }
}
