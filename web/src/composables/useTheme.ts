import { ref, computed, watch, onMounted } from 'vue'

export type Theme = 'light' | 'dark' | 'system'

// Reactive state
const theme = ref<Theme>('system')
const systemPrefersDark = ref(false)

// Computed current theme (resolves 'system' to actual theme)
const currentTheme = computed(() => {
  if (theme.value === 'system') {
    return systemPrefersDark.value ? 'dark' : 'light'
  }
  return theme.value
})

// Check system preference
const checkSystemPreference = () => {
  systemPrefersDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
}

// Apply theme to DOM
const applyTheme = (targetTheme: 'light' | 'dark') => {
  const html = document.documentElement

  if (targetTheme === 'dark') {
    html.classList.add('dark')
  } else {
    html.classList.remove('dark')
  }
}

// Save to localStorage
const saveTheme = (newTheme: Theme) => {
  try {
    localStorage.setItem('theme', newTheme)
  } catch (error) {
    console.warn('Failed to save theme preference:', error)
  }
}

// Load from localStorage
const loadTheme = (): Theme => {
  try {
    const savedTheme = localStorage.getItem('theme') as Theme
    return savedTheme && ['light', 'dark', 'system'].includes(savedTheme) ? savedTheme : 'system'
  } catch (error) {
    console.warn('Failed to load theme preference:', error)
    return 'system'
  }
}

// Set theme and persist
const setTheme = (newTheme: Theme) => {
  theme.value = newTheme
  saveTheme(newTheme)
  applyTheme(currentTheme.value)
}

// Toggle between light and dark (ignores system)
const toggleTheme = () => {
  if (theme.value === 'system') {
    setTheme(systemPrefersDark.value ? 'light' : 'dark')
  } else {
    setTheme(theme.value === 'light' ? 'dark' : 'light')
  }
}

// Cycle through all themes
const cycleTheme = () => {
  const themes: Theme[] = ['light', 'dark', 'system']
  const currentIndex = themes.indexOf(theme.value)
  const nextIndex = (currentIndex + 1) % themes.length
  setTheme(themes[nextIndex])
}

// Get theme display information
const getThemeInfo = (targetTheme: Theme) => {
  const info = {
    light: {
      label: 'Light',
      icon: 'sun',
      description: 'Light theme',
    },
    dark: {
      label: 'Dark',
      icon: 'moon',
      description: 'Dark theme',
    },
    system: {
      label: 'System',
      icon: 'screen',
      description: 'Follow system preference',
    },
  }

  return info[targetTheme]
}

export function useTheme() {
  // Initialize theme on first use
  onMounted(() => {
    // Check system preference
    checkSystemPreference()

    // Load saved theme
    theme.value = loadTheme()

    // Apply initial theme
    applyTheme(currentTheme.value)

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemChange = (e: MediaQueryListEvent) => {
      systemPrefersDark.value = e.matches
      if (theme.value === 'system') {
        applyTheme(currentTheme.value)
      }
    }

    // Add listener with both new and legacy methods for broader compatibility
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleSystemChange)
    } else {
      // Legacy support
      mediaQuery.addListener(handleSystemChange)
    }

    // Watch for theme changes and apply them
    watch(
      currentTheme,
      (newTheme) => {
        applyTheme(newTheme)
      },
      { immediate: true },
    )
  })

  return {
    theme: computed(() => theme.value),
    currentTheme,
    systemPrefersDark: computed(() => systemPrefersDark.value),
    setTheme,
    toggleTheme,
    cycleTheme,
    getThemeInfo,

    // Theme checks
    isLight: computed(() => currentTheme.value === 'light'),
    isDark: computed(() => currentTheme.value === 'dark'),
    isSystem: computed(() => theme.value === 'system'),
  }
}
