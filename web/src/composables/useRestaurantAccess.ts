import { computed } from 'vue'
import { useAuthStore } from '../stores/auth'
import { companyService } from '../services/company.service'
import type { CompanySubscription } from '../types/company'

export function useRestaurantAccess() {
  const authStore = useAuthStore()

  const checkSubscriptionStatus = (subscription: CompanySubscription) => {
    const now = new Date()

    if (subscription.isTrialMode && subscription.trialEndDate) {
      const trialEnd = new Date(subscription.trialEndDate)
      const diffTime = trialEnd.getTime() - now.getTime()
      const daysRemaining = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      return {
        type: 'trial' as const,
        active: daysRemaining > 0,
        daysRemaining: Math.max(0, daysRemaining),
        label: daysRemaining > 0 ? `${daysRemaining}d trial` : 'Expired',
        badgeColor: daysRemaining > 0 ? 'bg-green-500' : 'bg-red-500'
      }
    }

    if (!subscription.isTrialMode && subscription.nextBillingDate) {
      const nextBilling = new Date(subscription.nextBillingDate)
      const isPastDue = nextBilling < now

      return {
        type: 'paid' as const,
        active: !isPastDue,
        daysRemaining: 0,
        label: isPastDue ? 'Past Due' : 'Active',
        badgeColor: isPastDue ? 'bg-orange-500' : 'bg-blue-500'
      }
    }

    return {
      type: 'active' as const,
      active: true,
      daysRemaining: 0,
      label: 'Active',
      badgeColor: 'bg-blue-500'
    }
  }

  const checkRestaurantAccess = async () => {
    if (!authStore.currentUser?.currentCompanyId) {
      return {
        hasAccess: false,
        isExpired: false,
        needsSubscription: true,
        message: 'User not authenticated',
        subscription: null
      }
    }

    try {
      const subscriptions = await companyService.getCompanySubscriptions(authStore.currentUser.currentCompanyId)
      const restaurantSubscription = subscriptions.find(sub =>
        sub.module === 'POS' && sub.posType === 'RESTAURANT'
      )

      if (!restaurantSubscription) {
        return {
          hasAccess: false,
          isExpired: false,
          needsSubscription: true,
          message: 'Restaurant features require a POS subscription',
          subscription: null
        }
      }

      const status = checkSubscriptionStatus(restaurantSubscription)

      if (!status.active) {
        return {
          hasAccess: false,
          isExpired: status.type === 'trial',
          needsSubscription: false,
          message: status.type === 'trial'
            ? `Restaurant trial expired ${status.daysRemaining === 0 ? 'today' : `${Math.abs(status.daysRemaining)} days ago`}. Upgrade to continue using restaurant features.`
            : 'Restaurant subscription payment is past due. Please update your payment to continue.',
          subscription: restaurantSubscription
        }
      }

      return {
        hasAccess: true,
        isExpired: false,
        needsSubscription: false,
        message: '',
        subscription: restaurantSubscription
      }
    } catch (error) {
      console.error('Failed to check restaurant access:', error)
      return {
        hasAccess: false,
        isExpired: false,
        needsSubscription: true,
        message: 'Failed to verify restaurant access',
        subscription: null
      }
    }
  }

  return {
    checkRestaurantAccess,
    checkSubscriptionStatus
  }
}
