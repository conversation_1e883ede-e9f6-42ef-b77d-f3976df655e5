import { computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'

export function useLanguage() {
  const { locale, t } = useI18n()

  const currentLanguage = computed(() => locale.value)

  const isKhmer = computed(() => locale.value === 'km')
  const isEnglish = computed(() => locale.value === 'en')

  const switchToEnglish = () => {
    locale.value = 'en'
    localStorage.setItem('locale', 'en')
    updateHtmlLang('en')
    updateFontClass('en')
  }

  const switchToKhmer = () => {
    locale.value = 'km'
    localStorage.setItem('locale', 'km')
    updateHtmlLang('km')
    updateFontClass('km')
  }

  const switchLanguage = () => {
    if (locale.value === 'en') {
      switchToKhmer()
    } else {
      switchToEnglish()
    }
  }

  const updateHtmlLang = (lang: string) => {
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('lang', lang)
    }
  }

  const updateFontClass = (lang: string) => {
    if (typeof document !== 'undefined') {
      const body = document.body
      // Remove existing font classes
      body.classList.remove('font-khmer', 'font-english')

      // Add appropriate font class
      if (lang === 'km') {
        body.classList.add('font-khmer')
      } else {
        body.classList.add('font-english')
      }
    }
  }

  const getLanguageFlag = (lang: string) => {
    switch (lang) {
      case 'en':
        return '🇺🇸'
      case 'km':
        return '🇰🇭'
      default:
        return '🌐'
    }
  }

  const getLanguageName = (lang: string) => {
    switch (lang) {
      case 'en':
        return 'English'
      case 'km':
        return 'ខ្មែរ'
      default:
        return 'Unknown'
    }
  }

  const availableLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'km', name: 'ខ្មែរ', flag: '🇰🇭' },
  ]

  // Watch for language changes and update accordingly
  watch(
    currentLanguage,
    (newLang) => {
      updateHtmlLang(newLang)
      updateFontClass(newLang)
    },
    { immediate: true },
  )

  return {
    currentLanguage,
    isKhmer,
    isEnglish,
    switchToEnglish,
    switchToKhmer,
    switchLanguage,
    getLanguageFlag,
    getLanguageName,
    availableLanguages,
    t,
  }
}
