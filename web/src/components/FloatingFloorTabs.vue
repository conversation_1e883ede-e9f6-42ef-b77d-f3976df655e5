<template>
  <div class="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
    <!-- Empty State - No Floors -->
    <div v-if="floors.length === 0" class="bg-white/95 backdrop-blur-md rounded-full border shadow-lg px-4 py-3 flex items-center gap-3">
      <div class="flex items-center gap-2 text-sm text-muted-foreground">
        <DuotoneIcon name="building" class="w-4 h-4" />
        <span>{{ $t('restaurant.floors.empty.title') }}</span>
      </div>

      <!-- Create First Floor Button -->
      <button
        @click="$emit('create-floor')"
        :class="[
          'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2',
          'bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm'
        ]"
        :disabled="loading"
      >
        <DuotoneIcon name="plus" class="w-4 h-4" />
        <span>{{ $t('restaurant.floors.selector.createFloor') }}</span>
      </button>

      <!-- Refresh Button -->
      <button
        @click="$emit('refresh-floors')"
        :class="[
          'px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2',
          'text-muted-foreground hover:text-foreground hover:bg-muted/50',
          loading && 'cursor-not-allowed opacity-50'
        ]"
        :title="$t('restaurant.floors.selector.refresh')"
        :disabled="loading"
      >
        <DuotoneIcon
          :name="loading ? 'loading-circle' : 'refresh'"
          :class="`w-4 h-4 ${loading ? 'animate-spin' : ''}`"
        />
      </button>
    </div>

    <!-- Floating Floor Tabs Container (when floors exist) -->
    <div v-else class="bg-white/95 backdrop-blur-md rounded-full border shadow-lg px-2 py-2 flex items-center gap-1">
      <!-- All Floors Tab -->
      <button
        @click="onFloorSelect('')"
        :class="[
          'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2',
          !selectedFloorId
            ? 'bg-primary text-primary-foreground shadow-sm'
            : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
        ]"
        :disabled="loading"
      >
        <DuotoneIcon name="layers" class="w-4 h-4" />
        <span>All</span>
        <span
          v-if="!selectedFloorId"
          class="bg-primary-foreground/20 text-primary-foreground px-2 py-0.5 rounded-full text-xs font-bold"
        >
          {{ totalTables }}
        </span>
      </button>

      <!-- Individual Floor Tabs -->
      <button
        v-for="floor in floors"
        :key="floor._id"
        @click="onFloorSelect(floor._id)"
        :class="[
          'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2 relative',
          selectedFloorId === floor._id
            ? 'bg-primary text-primary-foreground shadow-sm'
            : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
        ]"
        :disabled="loading"
      >
        <!-- Floor Color Indicator -->
        <div
          class="w-3 h-3 rounded-full border border-white/50 shadow-sm"
          :style="{ backgroundColor: floor.color || '#3B82F6' }"
        />

        <!-- Floor Name -->
        <span>{{ floor.name }}</span>

        <!-- Table Count Badge -->
        <span
          v-if="typeof floor.tableCount === 'number'"
          :class="[
            'px-2 py-0.5 rounded-full text-xs font-bold',
            selectedFloorId === floor._id
              ? 'bg-primary-foreground/20 text-primary-foreground'
              : 'bg-muted text-muted-foreground'
          ]"
        >
          {{ floor.tableCount }}
        </span>
      </button>

      <!-- Separator -->
      <div class="w-px h-6 bg-border mx-1" />

      <!-- Floor Management Button -->
      <button
        @click="$emit('manage-floors')"
        :class="[
          'px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2',
          'text-muted-foreground hover:text-foreground hover:bg-muted/50'
        ]"
        :title="$t('restaurant.floors.selector.manageFloors')"
        :disabled="loading"
      >
        <DuotoneIcon name="settings" class="w-4 h-4" />
      </button>

      <!-- Create Floor Button -->
      <button
        @click="$emit('create-floor')"
        :class="[
          'px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2',
          'text-primary hover:text-primary/80 hover:bg-primary/10'
        ]"
        :title="$t('restaurant.floors.selector.createFloor')"
        :disabled="loading"
      >
        <DuotoneIcon name="plus" class="w-4 h-4" />
      </button>

      <!-- Refresh Button -->
      <button
        @click="$emit('refresh-floors')"
        :class="[
          'px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center gap-2',
          'text-muted-foreground hover:text-foreground hover:bg-muted/50',
          loading && 'cursor-not-allowed opacity-50'
        ]"
        :title="$t('restaurant.floors.selector.refresh')"
        :disabled="loading"
      >
        <DuotoneIcon
          :name="loading ? 'loading-circle' : 'refresh'"
          :class="`w-4 h-4 ${loading ? 'animate-spin' : ''}`"
        />
      </button>
    </div>

    <!-- Loading Indicator -->
    <div
      v-if="loading"
      class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-muted/90 backdrop-blur-sm rounded-full px-3 py-1"
    >
      <div class="flex items-center gap-2 text-xs text-muted-foreground">
        <DuotoneIcon name="loading-circle" class="w-3 h-3 animate-spin" />
        <span>{{ $t('common.loading') }}</span>
      </div>
    </div>

    <!-- Selected Floor Indicator (when a floor is selected) -->
    <div
      v-if="selectedFloor && selectedFloorId && floors.length > 0"
      class="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-black/80 text-white rounded-lg px-3 py-2 text-xs"
    >
      <div class="flex items-center gap-2">
        <div
          class="w-2 h-2 rounded-full"
          :style="{ backgroundColor: selectedFloor.color || '#3B82F6' }"
        />
        <span>{{ selectedFloor.name }}</span>
        <span v-if="typeof selectedFloor.tableCount === 'number'" class="text-white/70">
          ({{ selectedFloor.tableCount }} tables)
        </span>
      </div>
      <!-- Arrow pointing down -->
      <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { RestaurantFloor } from '../services/restaurant.service';
import DuotoneIcon from './DuotoneIcon.vue';

const { t } = useI18n();

// Props
interface Props {
  floors: RestaurantFloor[];
  selectedFloorId: string;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// Emits
interface Emits {
  (e: 'floor-changed', floorId: string | null): void;
  (e: 'manage-floors'): void;
  (e: 'refresh-floors'): void;
  (e: 'create-floor'): void;
}

const emit = defineEmits<Emits>();

// Computed properties
const selectedFloor = computed(() => {
  if (!props.selectedFloorId || !Array.isArray(props.floors)) return null;
  return props.floors.find(floor => floor._id === props.selectedFloorId) || null;
});

const totalTables = computed(() => {
  if (!Array.isArray(props.floors)) return 0;
  return props.floors.reduce((total, floor) => {
    return total + (typeof floor.tableCount === 'number' ? floor.tableCount : 0);
  }, 0);
});

// Handle floor selection
function onFloorSelect(floorId: string) {
  try {
    emit('floor-changed', floorId || null);
  } catch (error) {
    console.error('Error in floor selection:', error);
  }
}
</script>

<style scoped>
/* Additional styles for better visual effects */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

/* Smooth hover transitions */
button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar for overflow (if needed on mobile) */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>
