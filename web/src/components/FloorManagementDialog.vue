<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <DuotoneIcon name="building-4" class="w-5 h-5 text-primary" />
          {{ $t('restaurant.floors.management.title') }}
        </DialogTitle>
        <DialogDescription>
          {{ $t('restaurant.floors.management.description') }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Create New Floor Section -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">{{ $t('restaurant.floors.create.title') }}</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="space-y-2">
              <Label for="floor-name">{{ $t('restaurant.floors.create.name') }}</Label>
              <Input
                id="floor-name"
                v-model="newFloor.name"
                :placeholder="$t('restaurant.floors.create.namePlaceholder')"
                @keydown.enter="createFloor"
              />
            </div>

            <div class="space-y-2">
              <Label for="floor-color">{{ $t('restaurant.floors.create.color') }}</Label>
              <div class="flex gap-2">
                <Input
                  id="floor-color"
                  v-model="newFloor.color"
                  type="color"
                  class="w-12 h-10 p-1 border rounded cursor-pointer"
                />
                <Input
                  v-model="newFloor.color"
                  :placeholder="$t('restaurant.floors.create.colorPlaceholder')"
                  class="flex-1"
                />
              </div>
            </div>
          </div>

          <div class="space-y-2">
            <Label for="floor-description">{{ $t('restaurant.floors.create.description') }}</Label>
            <Textarea
              id="floor-description"
              v-model="newFloor.description"
              :placeholder="$t('restaurant.floors.create.descriptionPlaceholder')"
              rows="2"
            />
          </div>

          <Button
            @click="createFloor"
            :disabled="!newFloor.name.trim() || creating"
            class="w-full"
          >
            <DuotoneIcon
              :name="creating ? 'loading-circle' : 'plus'"
              :class="`w-4 h-4 mr-2 ${creating ? 'animate-spin' : ''}`"
            />
            {{ creating ? $t('common.creating') : $t('restaurant.floors.create.button') }}
          </Button>
        </div>

        <!-- Existing Floors Section -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">{{ $t('restaurant.floors.existing.title') }}</h3>
            <span class="text-sm text-muted-foreground">
              {{ floors.length }} {{ $t('restaurant.floors.existing.count') }}
            </span>
          </div>

          <div v-if="floorsLoading" class="flex items-center justify-center py-8">
            <DuotoneIcon name="loading-circle" class="w-6 h-6 animate-spin text-primary" />
            <span class="ml-2">{{ $t('common.loading') }}</span>
          </div>

          <div v-else-if="floors.length === 0" class="text-center py-8 text-muted-foreground">
            <DuotoneIcon name="building-4" class="w-12 h-12 mx-auto mb-2 opacity-50" />
            <p>{{ $t('restaurant.floors.existing.empty') }}</p>
          </div>

          <div v-else class="space-y-2">
            <div
              v-for="(floor, index) in floors"
              :key="floor._id"
              class="flex items-center gap-3 p-3 border rounded-lg hover:bg-accent/50 transition-colors"
            >
              <!-- Floor Color Indicator -->
              <div
                class="w-4 h-4 rounded-full border-2 border-white shadow-sm flex-shrink-0"
                :style="{ backgroundColor: floor.color }"
              />

              <!-- Floor Info -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center gap-2">
                  <h4 class="font-medium truncate">{{ floor.name }}</h4>
                  <Badge variant="secondary" class="text-xs">
                    {{ floor.tableCount || 0 }} {{ $t('restaurant.floors.existing.tables') }}
                  </Badge>
                </div>
                <p v-if="floor.description" class="text-sm text-muted-foreground truncate">
                  {{ floor.description }}
                </p>
              </div>

              <!-- Floor Actions -->
              <div class="flex items-center gap-1">
                <!-- Move Up -->
                <Button
                  v-if="index > 0"
                  variant="ghost"
                  size="sm"
                  @click="moveFloor(index, index - 1)"
                  :disabled="reordering"
                >
                  <DuotoneIcon name="arrow-up" class="w-4 h-4" />
                </Button>

                <!-- Move Down -->
                <Button
                  v-if="index < floors.length - 1"
                  variant="ghost"
                  size="sm"
                  @click="moveFloor(index, index + 1)"
                  :disabled="reordering"
                >
                  <DuotoneIcon name="arrow-down" class="w-4 h-4" />
                </Button>

                <!-- Edit Floor -->
                <Button
                  variant="ghost"
                  size="sm"
                  @click="startEditFloor(floor)"
                >
                  <DuotoneIcon name="edit" class="w-4 h-4" />
                </Button>

                <!-- Delete Floor -->
                <Button
                  variant="ghost"
                  size="sm"
                  @click="deleteFloor(floor)"
                  :disabled="floor.tableCount && floor.tableCount > 0"
                  class="text-destructive hover:text-destructive"
                >
                  <DuotoneIcon name="trash" class="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="$emit('update:open', false)">
          {{ $t('common.close') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- Edit Floor Dialog -->
  <Dialog :open="editDialog.open" @update:open="editDialog.open = $event">
    <DialogContent class="sm:max-w-[400px]">
      <DialogHeader>
        <DialogTitle>{{ $t('restaurant.floors.edit.title') }}</DialogTitle>
        <DialogDescription>
          {{ $t('restaurant.floors.edit.description') }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <div class="space-y-2">
          <Label for="edit-floor-name">{{ $t('restaurant.floors.create.name') }}</Label>
          <Input
            id="edit-floor-name"
            v-model="editDialog.floor.name"
            :placeholder="$t('restaurant.floors.create.namePlaceholder')"
          />
        </div>

        <div class="space-y-2">
          <Label for="edit-floor-color">{{ $t('restaurant.floors.create.color') }}</Label>
          <div class="flex gap-2">
            <Input
              id="edit-floor-color"
              v-model="editDialog.floor.color"
              type="color"
              class="w-12 h-10 p-1 border rounded cursor-pointer"
            />
            <Input
              v-model="editDialog.floor.color"
              :placeholder="$t('restaurant.floors.create.colorPlaceholder')"
              class="flex-1"
            />
          </div>
        </div>

        <div class="space-y-2">
          <Label for="edit-floor-description">{{ $t('restaurant.floors.create.description') }}</Label>
          <Textarea
            id="edit-floor-description"
            v-model="editDialog.floor.description"
            :placeholder="$t('restaurant.floors.create.descriptionPlaceholder')"
            rows="2"
          />
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="editDialog.open = false">
          {{ $t('common.cancel') }}
        </Button>
        <Button
          @click="updateFloor"
          :disabled="!editDialog.floor.name?.trim() || updating"
        >
          <DuotoneIcon
            :name="updating ? 'loading-circle' : 'check'"
            :class="`w-4 h-4 mr-2 ${updating ? 'animate-spin' : ''}`"
          />
          {{ updating ? $t('common.updating') : $t('common.update') }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAuthStore } from '../stores/auth';
import type { RestaurantFloor } from '../services/restaurant.service';
import { restaurantService } from '../services/restaurant.service';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { Badge } from './ui/badge';
import DuotoneIcon from './DuotoneIcon.vue';
// @ts-ignore - vue-sonner types may not be fully available
import { toast } from 'vue-sonner';

interface Props {
  open: boolean;
}

interface Emits {
  (e: 'update:open', value: boolean): void;
  (e: 'floors-updated'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t } = useI18n();
const authStore = useAuthStore();

// State
const floors = ref<RestaurantFloor[]>([]);
const floorsLoading = ref(false);
const creating = ref(false);
const updating = ref(false);
const reordering = ref(false);

// New floor form
const newFloor = reactive({
  name: '',
  description: '',
  color: '#3B82F6'
});

// Edit floor dialog
const editDialog = reactive({
  open: false,
  floor: {
    _id: '',
    name: '',
    description: '',
    color: '#3B82F6'
  } as Partial<RestaurantFloor>
});

// Watch for dialog open/close
watch(() => props.open, async (isOpen) => {
  if (isOpen) {
    await loadFloors();
  } else {
    // Reset form when dialog closes
    Object.assign(newFloor, {
      name: '',
      description: '',
      color: '#3B82F6'
    });
  }
});

// Load floors
async function loadFloors() {
  if (!authStore.currentUser?.currentCompanyId) return;

  floorsLoading.value = true;
  try {
    floors.value = await restaurantService.getFloors(authStore.currentUser.currentCompanyId);
  } catch (error) {
    console.error('Failed to load floors:', error);
    // TODO: Show error toast
  } finally {
    floorsLoading.value = false;
  }
}

// Create new floor
async function createFloor() {
  if (!authStore.currentUser?.currentCompanyId || !newFloor.name.trim()) return;

  try {
    creating.value = true;

    const floorData = {
      companyId: authStore.currentUser.currentCompanyId,
      name: newFloor.name.trim(),
      description: newFloor.description.trim() || undefined,
      color: newFloor.color || '#3B82F6'
    };

    console.log('Creating floor:', floorData);

    const createdFloor = await restaurantService.createFloor(
      floorData.companyId,
      floorData
    );

    if (createdFloor) {
      // Add to local floors array
      floors.value.push(createdFloor);

      // Reset form
      Object.assign(newFloor, {
        name: '',
        description: '',
        color: '#3B82F6'
      });

      toast.success('Floor created successfully!');

      // Notify parent component
      emit('floors-updated');
    }
  } catch (err) {
    console.error('Failed to create floor:', err);
    toast.error('Failed to create floor', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    });
  } finally {
    creating.value = false;
  }
}

// Start editing floor
function startEditFloor(floor: RestaurantFloor) {
  editDialog.floor = {
    _id: floor._id,
    name: floor.name,
    description: floor.description || '',
    color: floor.color
  };
  editDialog.open = true;
}

// Update floor
async function updateFloor() {
  if (!authStore.currentUser?.currentCompanyId || !editDialog.floor._id || !editDialog.floor.name?.trim()) return;

  updating.value = true;
  try {
    await restaurantService.updateFloor(authStore.currentUser.currentCompanyId, editDialog.floor._id, {
      name: editDialog.floor.name.trim(),
      description: editDialog.floor.description?.trim() || undefined,
      color: editDialog.floor.color
    });

    await loadFloors();
    editDialog.open = false;
    emit('floors-updated');

    // TODO: Show success toast
  } catch (error) {
    console.error('Failed to update floor:', error);
    // TODO: Show error toast
  } finally {
    updating.value = false;
  }
}

// Delete floor
async function deleteFloor(floor: RestaurantFloor) {
  if (!authStore.currentUser?.currentCompanyId) return;

  if (floor.tableCount && floor.tableCount > 0) {
    // TODO: Show error toast that floor has tables
    return;
  }

  if (!confirm(t('restaurant.floors.delete.confirm', { name: floor.name }))) {
    return;
  }

  try {
    await restaurantService.deleteFloor(authStore.currentUser.currentCompanyId, floor._id);
    await loadFloors();
    emit('floors-updated');

    // TODO: Show success toast
  } catch (error) {
    console.error('Failed to delete floor:', error);
    // TODO: Show error toast
  }
}

// Move floor (reorder)
async function moveFloor(fromIndex: number, toIndex: number) {
  if (!authStore.currentUser?.currentCompanyId) return;

  reordering.value = true;
  try {
    // Calculate new display orders
    const floorOrders = floors.value.map((floor, index) => {
      let newIndex = index;
      if (index === fromIndex) {
        newIndex = toIndex;
      } else if (fromIndex < toIndex && index > fromIndex && index <= toIndex) {
        newIndex = index - 1;
      } else if (fromIndex > toIndex && index >= toIndex && index < fromIndex) {
        newIndex = index + 1;
      }

      return {
        floorId: floor._id,
        displayOrder: newIndex + 1
      };
    });

    // Update on server
    await restaurantService.reorderFloors(authStore.currentUser.currentCompanyId, floorOrders);

    // Update local state
    await loadFloors();
    emit('floors-updated');

    // TODO: Show success toast
  } catch (error) {
    console.error('Failed to reorder floors:', error);
    // TODO: Show error toast
  } finally {
    reordering.value = false;
  }
}
</script>
