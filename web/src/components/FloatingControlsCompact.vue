<script setup lang="ts">
import { ref, computed } from 'vue'
import DuotoneIcon from './DuotoneIcon.vue'
import { useTheme } from '@/composables/useTheme'
import { useLanguage } from '@/composables/useLanguage'

const { theme, currentTheme, cycleTheme, getThemeInfo } = useTheme()
const { currentLanguage, switchLanguage, getLanguageFlag, getLanguageName } = useLanguage()

// Control panel state
const isExpanded = ref(false)

// Get current theme info
const currentThemeInfo = computed(() => getThemeInfo(theme.value))

// Get theme icon
const themeIcon = computed(() => {
  if (theme.value === 'system') {
    return 'devices'
  }
  return currentTheme.value === 'dark' ? 'moon' : 'sun'
})

// Get next language for preview
const nextLanguage = computed(() => {
  return currentLanguage.value === 'en' ? 'km' : 'en'
})

// Toggle expanded state
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// Handle outside click to close
const handleOutsideClick = () => {
  if (isExpanded.value) {
    isExpanded.value = false
  }
}
</script>

<template>
  <!-- Backdrop for outside click -->
  <div v-if="isExpanded" @click="handleOutsideClick" class="fixed inset-0 z-40"></div>

  <!-- Floating Controls Panel -->
  <div class="fixed bottom-6 right-6 z-50">
    <div class="relative">
      <!-- Expanded Controls -->
      <Transition
        enter-active-class="transition-all duration-300 ease-out"
        enter-from-class="opacity-0 scale-95 translate-y-2"
        enter-to-class="opacity-100 scale-100 translate-y-0"
        leave-active-class="transition-all duration-200 ease-in"
        leave-from-class="opacity-100 scale-100 translate-y-0"
        leave-to-class="opacity-0 scale-95 translate-y-2"
      >
        <div v-if="isExpanded" class="absolute bottom-full right-0 mb-4 flex gap-3">
          <!-- Language Control -->
          <div class="group/lang relative top-16 -right-6">
            <button
              @click="switchLanguage"
              class="flex items-center gap-2 px-3 py-2.5 bg-white/95 hover:bg-white dark:bg-gray-900/95 dark:hover:bg-gray-900 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
            >
              <!-- Current Language -->
              <div class="flex items-center gap-2">
                <span class="text-lg">{{ getLanguageFlag(currentLanguage) }}</span>
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ getLanguageName(currentLanguage) }}
                </span>
              </div>

              <!-- Arrow & Next Language Preview -->
              <div class="flex items-center gap-1.5 text-gray-400 dark:text-gray-500">
                <DuotoneIcon name="arrow-right" size="sm" />
                <span class="text-sm">{{ getLanguageFlag(nextLanguage) }}</span>
              </div>
            </button>

            <!-- Language Tooltip -->
            <div
              class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1.5 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover/lang:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none"
            >
              Switch Language
              <div
                class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"
              ></div>
            </div>
          </div>

          <!-- Theme Control -->
          <div class="group/theme relative">
            <button
              @click="cycleTheme"
              class="flex items-center gap-2 px-3 py-2.5 bg-white/95 hover:bg-white dark:bg-gray-900/95 dark:hover:bg-gray-900 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105"
            >
              <!-- Current Theme -->
              <div class="flex items-center gap-2">
                <DuotoneIcon
                  :name="themeIcon"
                  size="md"
                  :class="`transition-colors duration-200 ${
                    theme === 'light'
                      ? 'text-yellow-500'
                      : theme === 'dark'
                        ? 'text-blue-400'
                        : 'text-green-500'
                  }`"
                />
                <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
                  {{ currentThemeInfo.label }}
                </span>
              </div>

              <!-- Cycle Indicator -->
              <DuotoneIcon
                name="refresh"
                size="sm"
                class="text-gray-400 dark:text-gray-500 group-hover/theme:text-purple-500 transition-colors duration-200"
              />
            </button>

            <!-- Theme Tooltip -->
            <div
              class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1.5 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover/theme:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none"
            >
              {{ currentThemeInfo.description }}
              <div
                class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"
              ></div>
            </div>
          </div>
        </div>
      </Transition>

      <!-- Main Control Button -->
      <button
        @click="toggleExpanded"
        class="group relative flex items-center justify-center w-14 h-14 bg-white/95 hover:bg-white dark:bg-gray-900/95 dark:hover:bg-gray-900 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        :class="{ 'rotate-90': isExpanded }"
        :title="isExpanded ? 'Close controls' : 'Open controls'"
      >
        <!-- Main Icon -->
        <DuotoneIcon
          name="setting"
          size="lg"
          :class="`text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-all duration-300 ${isExpanded ? 'rotate-180' : ''}`"
        />

        <!-- Status Indicators -->
        <div class="absolute -top-1 -right-1 flex gap-0.5">
          <!-- Language indicator -->
          <div
            class="w-2.5 h-2.5 rounded-full border-2 border-white dark:border-gray-900 transition-all duration-200"
            :class="currentLanguage === 'en' ? 'bg-blue-500' : 'bg-orange-500'"
          ></div>
          <!-- Theme indicator -->
          <div
            class="w-2.5 h-2.5 rounded-full border-2 border-white dark:border-gray-900 transition-all duration-200"
            :class="{
              'bg-yellow-400': theme === 'light',
              'bg-blue-600': theme === 'dark',
              'bg-green-500': theme === 'system',
            }"
          ></div>
        </div>

        <!-- Expanded State Ring -->
        <div
          v-if="isExpanded"
          class="absolute inset-0 rounded-2xl border-2 border-blue-400 dark:border-blue-500 animate-pulse"
        ></div>
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Custom animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
