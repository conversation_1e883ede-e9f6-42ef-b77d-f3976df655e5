<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'primary' | 'secondary' | 'white'
  text?: string
  fullscreen?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  variant: 'primary',
  text: '',
  fullscreen: false
})

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }
  return sizes[props.size]
})

const colorClasses = computed(() => {
  const colors = {
    primary: 'text-gray-900',
    secondary: 'text-gray-500',
    white: 'text-white'
  }
  return colors[props.variant]
})

const textSizeClasses = computed(() => {
  const sizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  }
  return sizes[props.size]
})
</script>

<template>
  <div
    :class="[
      'flex flex-col items-center justify-center',
      fullscreen ? 'fixed inset-0 bg-white bg-opacity-90 backdrop-blur-sm z-50' : ''
    ]"
  >
    <div class="flex items-center space-x-2">
      <!-- Spinning Circle -->
      <svg
        :class="[sizeClasses, colorClasses, 'animate-spin']"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        />
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>

      <!-- Loading Text -->
      <span
        v-if="text"
        :class="[textSizeClasses, colorClasses, 'font-medium']"
      >
        {{ text }}
      </span>
    </div>
  </div>
</template>
