<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { categoryService } from '@/services/category.service'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Textarea } from '@/components/ui/textarea'
import {
  ChevronRight,
  ChevronDown,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  FolderPlus,
  Package,
  Move3d
} from 'lucide-vue-next'
import type { Category, CategoryTree as CategoryTreeType } from '../../../shared/types/pos'
import CategoryNode from './CategoryNode.vue'

const authStore = useAuthStore()

// State
const categoryTree = ref<CategoryTreeType | null>(null)
const isLoading = ref(true)
const error = ref('')
const expandedCategories = ref<Set<string>>(new Set())
const selectedCategory = ref<Category | null>(null)

// Dialog states
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDeleteDialog = ref(false)

// Form states
const createForm = ref({
  name: '',
  description: '',
  parentId: '',
  icon: '',
  color: '#3B82F6'
})

const editForm = ref({
  name: '',
  description: '',
  icon: '',
  color: '#3B82F6'
})

// Colors for categories
const categoryColors = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#EC4899', '#14B8A6', '#F97316', '#84CC16', '#6366F1'
]

// Icons for categories
const categoryIcons = [
  'package', 'laptop', 'smartphone', 'shirt', 'coffee',
  'car', 'home', 'book', 'music', 'camera'
]

const companyId = computed(() => authStore.currentUser?.currentCompanyId || '')

const loadCategoryTree = async () => {
  if (!companyId.value) return

  try {
    isLoading.value = true
    error.value = ''

    // Use real API data
    categoryTree.value = await categoryService.getCategoryTree(companyId.value)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load categories'
    console.error('Failed to load category tree:', err)
  } finally {
    isLoading.value = false
  }
}

const toggleExpand = (categoryId: string) => {
  if (expandedCategories.value.has(categoryId)) {
    expandedCategories.value.delete(categoryId)
  } else {
    expandedCategories.value.add(categoryId)
  }
}

const handleCreateCategory = async () => {
  if (!companyId.value || !createForm.value.name.trim()) return

  try {
    await categoryService.createCategory(companyId.value, {
      name: createForm.value.name.trim(),
      description: createForm.value.description.trim() || undefined,
      parentId: createForm.value.parentId || undefined,
      icon: createForm.value.icon || undefined,
      color: createForm.value.color
    })

    // Reset form and reload
    createForm.value = {
      name: '',
      description: '',
      parentId: '',
      icon: '',
      color: '#3B82F6'
    }
    showCreateDialog.value = false
    await loadCategoryTree()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to create category'
  }
}

const handleEditCategory = async () => {
  if (!companyId.value || !selectedCategory.value || !editForm.value.name.trim()) return

  try {
    await categoryService.updateCategory(companyId.value, selectedCategory.value._id, {
      name: editForm.value.name.trim(),
      description: editForm.value.description.trim() || undefined,
      icon: editForm.value.icon || undefined,
      color: editForm.value.color
    })

    showEditDialog.value = false
    selectedCategory.value = null
    await loadCategoryTree()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to update category'
  }
}

const handleDeleteCategory = async () => {
  if (!companyId.value || !selectedCategory.value) return

  try {
    await categoryService.deleteCategory(companyId.value, selectedCategory.value._id)

    showDeleteDialog.value = false
    selectedCategory.value = null
    await loadCategoryTree()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to delete category'
  }
}

const openEditDialog = (category: Category) => {
  selectedCategory.value = category
  editForm.value = {
    name: category.name,
    description: category.description || '',
    icon: category.icon || '',
    color: category.color || '#3B82F6'
  }
  showEditDialog.value = true
}

const openDeleteDialog = (category: Category) => {
  selectedCategory.value = category
  showDeleteDialog.value = true
}

const openCreateSubcategory = (parentCategory: Category) => {
  createForm.value.parentId = parentCategory._id
  showCreateDialog.value = true
}

const getCategoryDepth = (category: Category): number => {
  return category.level
}

const getCategoryPath = (category: Category): string => {
  return category.path?.join(' > ') || category.name
}

onMounted(() => {
  loadCategoryTree()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold tracking-tight">Category Management</h2>
        <p class="text-muted-foreground">
          Organize your products into hierarchical categories
        </p>
      </div>

      <Dialog v-model:open="showCreateDialog">
        <DialogTrigger as-child>
          <Button>
            <Plus class="w-4 h-4 mr-2" />
            Add Category
          </Button>
        </DialogTrigger>
        <DialogContent class="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Category</DialogTitle>
            <DialogDescription>
              Add a new category to organize your products.
            </DialogDescription>
          </DialogHeader>
          <div class="space-y-4">
            <div>
              <Label for="name">Category Name</Label>
              <Input
                id="name"
                v-model="createForm.name"
                placeholder="Enter category name"
                class="mt-1"
              />
            </div>
            <div>
              <Label for="description">Description (Optional)</Label>
              <Textarea
                id="description"
                v-model="createForm.description"
                placeholder="Enter category description"
                class="mt-1"
              />
            </div>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <Label for="icon">Icon (Optional)</Label>
                <Input
                  id="icon"
                  v-model="createForm.icon"
                  placeholder="e.g., laptop"
                  class="mt-1"
                />
              </div>
              <div>
                <Label for="color">Color</Label>
                <div class="flex gap-2 mt-1">
                  <input
                    id="color"
                    v-model="createForm.color"
                    type="color"
                    class="w-full h-10 rounded border border-input"
                  />
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" @click="showCreateDialog = false">
              Cancel
            </Button>
            <Button @click="handleCreateCategory" :disabled="!createForm.name.trim()">
              Create Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center py-12">
      <div class="flex items-center gap-3">
        <div class="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
        <span class="text-muted-foreground">Loading categories...</span>
      </div>
    </div>

    <!-- Error State -->
    <Card v-else-if="error" class="border-red-200 bg-red-50">
      <CardContent class="pt-6">
        <div class="flex items-center gap-2 text-red-600">
          <span>{{ error }}</span>
          <Button variant="outline" size="sm" @click="loadCategoryTree">
            Retry
          </Button>
        </div>
      </CardContent>
    </Card>

    <!-- Category Tree -->
    <Card v-else-if="categoryTree">
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <FolderPlus class="w-5 h-5" />
            Categories
          </div>
          <Badge variant="secondary">
            {{ categoryTree.totalCount }} categories
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div class="space-y-2">
          <!-- Recursive Category Tree Component -->
          <template v-for="category in categoryTree.categories" :key="category._id">
            <CategoryNode
              :category="category"
              :expanded-categories="expandedCategories"
              @toggle-expand="toggleExpand"
              @edit="openEditDialog"
              @delete="openDeleteDialog"
              @create-subcategory="openCreateSubcategory"
            />
          </template>

          <!-- Empty State -->
          <div v-if="categoryTree.categories.length === 0" class="text-center py-8">
            <FolderPlus class="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 class="text-lg font-medium">No categories yet</h3>
            <p class="text-muted-foreground mb-4">Create your first category to organize products</p>
            <Button @click="showCreateDialog = true">
              <Plus class="w-4 h-4 mr-2" />
              Create Category
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Edit Dialog -->
    <Dialog v-model:open="showEditDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Category</DialogTitle>
          <DialogDescription>
            Update category information.
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div>
            <Label for="edit-name">Category Name</Label>
            <Input
              id="edit-name"
              v-model="editForm.name"
              placeholder="Enter category name"
              class="mt-1"
            />
          </div>
          <div>
            <Label for="edit-description">Description (Optional)</Label>
            <Textarea
              id="edit-description"
              v-model="editForm.description"
              placeholder="Enter category description"
              class="mt-1"
            />
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label for="edit-icon">Icon (Optional)</Label>
              <Input
                id="edit-icon"
                v-model="editForm.icon"
                placeholder="e.g., laptop"
                class="mt-1"
              />
            </div>
            <div>
              <Label for="edit-color">Color</Label>
              <input
                id="edit-color"
                v-model="editForm.color"
                type="color"
                class="w-full h-10 rounded border border-input mt-1"
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showEditDialog = false">
            Cancel
          </Button>
          <Button @click="handleEditCategory" :disabled="!editForm.name.trim()">
            Update Category
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Delete Dialog -->
    <Dialog v-model:open="showDeleteDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Delete Category</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete "{{ selectedCategory?.name }}"? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" @click="showDeleteDialog = false">
            Cancel
          </Button>
          <Button variant="destructive" @click="handleDeleteCategory">
            Delete Category
          </Button>
        </DialogFooter>
      </DialogContent>
        </Dialog>
  </div>
</template>
