<script setup lang="ts">
import { computed } from 'vue'
import DuotoneIcon from './DuotoneIcon.vue'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useTheme, type Theme } from '@/composables/useTheme'
import { useLanguage } from '@/composables/useLanguage'

const { theme, currentTheme, setTheme, getThemeInfo } = useTheme()
const { t } = useLanguage()

// Theme options
const themeOptions: Theme[] = ['light', 'dark', 'system']

// Get current theme icon
const currentThemeIcon = computed(() => {
  if (theme.value === 'system') {
    return 'screen'
  }
  return currentTheme.value === 'dark' ? 'moon' : 'sun'
})

// Get theme icon for each option
const getThemeIcon = (themeName: Theme) => {
  const info = getThemeInfo(themeName)
  return info.icon
}

// Check if theme is active
const isThemeActive = (themeName: Theme) => {
  return theme.value === themeName
}
</script>

<template>
  <DropdownMenu>
    <DropdownMenuTrigger asChild>
      <Button variant="outline" size="sm" class="w-9 px-0" :title="t('theme.themeSettings')">
        <DuotoneIcon :name="currentThemeIcon" size="sm" class="h-[1.2rem] w-[1.2rem]" />
        <span class="sr-only">{{ t('theme.toggleTheme') }}</span>
      </Button>
    </DropdownMenuTrigger>

    <DropdownMenuContent align="end" class="w-56">
      <div class="px-2 py-1.5 text-sm font-semibold text-muted-foreground">
        {{ t('theme.themeSettings') }}
      </div>

      <div class="h-px bg-border my-1"></div>

      <DropdownMenuItem
        v-for="themeName in themeOptions"
        :key="themeName"
        @click="setTheme(themeName)"
        class="flex items-center gap-3 px-2 py-2 cursor-pointer"
        :class="{ 'bg-accent text-accent-foreground': isThemeActive(themeName) }"
      >
        <DuotoneIcon :name="getThemeIcon(themeName)" size="sm" class="h-4 w-4" />

        <div class="flex-1">
          <div class="font-medium">
            {{ t(`theme.${themeName}`) }}
          </div>
          <div class="text-xs text-muted-foreground">
            {{ t(`theme.${themeName}Description`) }}
          </div>
        </div>

        <!-- Active indicator -->
        <DuotoneIcon
          v-if="isThemeActive(themeName)"
          name="check"
          size="sm"
          class="h-4 w-4 text-primary"
        />
      </DropdownMenuItem>
    </DropdownMenuContent>
  </DropdownMenu>
</template>
