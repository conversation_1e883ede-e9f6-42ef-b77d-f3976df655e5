<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCompanyStore } from '@/stores/company'
import { paymentService, type CreatePaymentRequest } from '@/services/payment.service'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import DuotoneIcon from './DuotoneIcon.vue'
import type { PaymentMethod, PaymentResponse, Currency } from '@/types/payment'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'

interface Props {
  open: boolean
  subscriptionId: string
  module: string
  amount: number
  currency: Currency
  description: string
  trialConversion?: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'payment-success', result: any): void
  (e: 'payment-failed', error: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const { t } = useI18n()
const authStore = useAuthStore()
const companyStore = useCompanyStore()
const { currentUser } = storeToRefs(authStore)
const { currentCompany } = storeToRefs(companyStore)

// State
const selectedMethod = ref<PaymentMethod>('ACLEDA_ECOMMERCE')
const customerInfo = ref({
  name: '',
  email: '',
  phone: ''
})
const paymentResponse = ref<PaymentResponse | null>(null)
const paymentResult = ref<any>(null)
const error = ref('')
const step = ref<'details' | 'processing' | 'payment' | 'success' | 'failed'>('details')
const isProcessing = ref(false)

// Constants
const steps = ['details', 'processing', 'payment', 'success']
const isDevelopment = import.meta.env.MODE === 'development'

// Payment methods configuration
const paymentMethods = [
  {
    value: 'ACLEDA_ECOMMERCE' as PaymentMethod,
    label: 'ACLEDA E-commerce',
    description: 'Secure payment via ACLEDA Bank',
    icon: 'credit-card',
    recommended: true
  },
  {
    value: 'ACLEDA_REDIRECT' as PaymentMethod,
    label: 'ACLEDA Redirect',
    description: 'Alternative ACLEDA payment method',
    icon: 'arrow-top-right-on-square',
    recommended: false
  },
  {
    value: 'BANK_TRANSFER' as PaymentMethod,
    label: 'Bank Transfer',
    description: 'Manual bank transfer',
    icon: 'building-library',
    recommended: false
  }
]

// Computed
const canProceed = computed(() => {
  return selectedMethod.value &&
         customerInfo.value.name.trim() &&
         customerInfo.value.email.trim() &&
         currentUser.value &&
         currentCompany.value
})

const formatAmount = (amount: number, currency: Currency) => {
  if (currency === 'USD') {
    return `$${amount.toFixed(2)}`
  } else {
    return `${amount.toLocaleString()} ៛`
  }
}

// Initialize customer info from current user
watch(() => currentUser.value, (user) => {
  if (user && !customerInfo.value.name) {
    customerInfo.value.name = `${user.firstName} ${user.lastName}`
    customerInfo.value.email = user.email
    customerInfo.value.phone = user.phone || ''
  }
}, { immediate: true })

// Reset when dialog opens
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    step.value = 'details'
    error.value = ''
    paymentResponse.value = null
    paymentResult.value = null
    isProcessing.value = false
  }
})

// Methods
const handleClose = () => {
  emit('update:open', false)
}

const handlePayment = async () => {
  if (!currentUser.value || !currentCompany.value) {
    error.value = 'User not authenticated'
    return
  }

  try {
    isProcessing.value = true
    error.value = ''
    step.value = 'processing'

    const request: CreatePaymentRequest = {
      amount: props.amount,
      currency: props.currency,
      description: props.description,
      companyId: currentCompany.value._id,
      module: props.module,
      paymentMethod: selectedMethod.value,
      customerInfo: customerInfo.value,
      subscriptionId: props.subscriptionId,
      trialConversion: props.trialConversion,
      userId: currentUser.value._id
    }

    const response = await paymentService.createPayment(request)
    paymentResponse.value = response
    step.value = 'payment'

  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Payment failed'
    step.value = 'failed'
    emit('payment-failed', error.value)
  } finally {
    isProcessing.value = false
  }
}

const redirectToPayment = async () => {
  if (!paymentResponse.value) return

  try {
    await paymentService.redirectToACLEDA(paymentResponse.value)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Redirect failed'
    step.value = 'failed'
    emit('payment-failed', error.value)
  }
}

const simulateSuccess = async () => {
  if (!paymentResponse.value) return

  try {
    await paymentService.simulatePaymentSuccess(paymentResponse.value.paymentId)

    // Check payment status
    const status = await paymentService.checkPaymentStatus(paymentResponse.value.paymentId)
    paymentResult.value = status
    step.value = 'success'
    emit('payment-success', status)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Simulation failed'
    step.value = 'failed'
    emit('payment-failed', error.value)
  }
}

const getStepIndex = () => {
  return steps.indexOf(step.value)
}

const getPaymentTitle = () => {
  if (props.trialConversion) {
    return t('payment.convertTrial', { module: props.module })
  }
  return t('payment.subscribeToModule', { module: props.module })
}

const formatCurrency = (amount: number, currency: Currency = 'USD') => {
  return paymentService.formatCurrency(amount, currency)
}

const formatExpiry = (date: Date) => {
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

const formatDate = (date?: Date | string) => {
  if (!date) return ''
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

// Auto-check payment status every 5 seconds when in payment step
let statusInterval: ReturnType<typeof setInterval> | null = null

const checkPaymentStatusPoll = async () => {
  if (!paymentResponse.value) return

  try {
    const status = await paymentService.checkPaymentStatus(paymentResponse.value.paymentId)
    if (status.status === 'completed') {
      paymentResult.value = status
      step.value = 'success'
      emit('payment-success', status)
      stopStatusPolling()
    } else if (status.status === 'failed' || status.status === 'cancelled') {
      step.value = 'failed'
      emit('payment-failed', 'Payment failed')
      stopStatusPolling()
    }
  } catch (err) {
    console.error('Payment status polling error:', err)
  }
}

const startStatusPolling = () => {
  if (statusInterval) clearInterval(statusInterval)
  statusInterval = setInterval(checkPaymentStatusPoll, 5000)
}

const stopStatusPolling = () => {
  if (statusInterval) {
    clearInterval(statusInterval)
    statusInterval = null
  }
}

onMounted(() => {
  // Start polling when in payment step
  if (step.value === 'payment') {
    startStatusPolling()
  }
})

// Watch step changes
const handleStepChange = (newStep: string) => {
  if (newStep === 'payment') {
    startStatusPolling()
  } else {
    stopStatusPolling()
  }
}

// Cleanup on unmount
onMounted(() => {
  return () => {
    stopStatusPolling()
  }
})
</script>

<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>{{ $t('payment.title') }}</DialogTitle>
        <DialogDescription>
          {{ $t('payment.description') }}
        </DialogDescription>
      </DialogHeader>

      <!-- Payment Steps -->
      <div class="flex items-center justify-center space-x-2 mb-6">
        <div
          v-for="(stepName, index) in steps"
          :key="stepName"
          class="flex items-center"
        >
          <div
            :class="[
              'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors',
              getStepIndex() >= index
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
            ]"
          >
            {{ index + 1 }}
          </div>
          <div
            v-if="index < steps.length - 1"
            :class="[
              'w-8 h-0.5 mx-2 transition-colors',
              getStepIndex() > index
                ? 'bg-blue-600'
                : 'bg-gray-200 dark:bg-gray-700'
            ]"
          />
        </div>
      </div>

      <!-- Step Content -->
      <div class="space-y-6">
        <!-- Step 1: Payment Details -->
        <div v-if="step === 'details'" class="space-y-4">
          <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h3 class="font-semibold text-blue-900 dark:text-blue-100 mb-2">
              {{ getPaymentTitle() }}
            </h3>
            <div class="space-y-2 text-sm text-blue-800 dark:text-blue-200">
              <div class="flex justify-between">
                <span>{{ $t('payment.amount') }}:</span>
                <span class="font-medium">{{ formatCurrency(amount, currency) }}</span>
              </div>
              <div class="flex justify-between">
                <span>{{ $t('payment.module') }}:</span>
                <span class="font-medium">{{ module }}</span>
              </div>
              <div v-if="trialConversion" class="flex justify-between">
                <span>{{ $t('payment.type') }}:</span>
                <span class="font-medium text-orange-600 dark:text-orange-400">
                  {{ $t('payment.trialConversion') }}
                </span>
              </div>
            </div>
          </div>

          <!-- Payment Method Selection -->
          <div class="space-y-3">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ $t('payment.paymentMethod') }}
            </label>
            <div class="space-y-2">
              <label
                v-for="method in paymentMethods"
                :key="method.value"
                class="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:border-blue-500 transition-colors"
                :class="{
                  'border-blue-500 bg-blue-50 dark:bg-blue-900/20': selectedMethod === method.value
                }"
              >
                <input
                  type="radio"
                  :value="method.value"
                  v-model="selectedMethod"
                  class="text-blue-600"
                />
                <div class="flex items-center space-x-2">
                  <DuotoneIcon :name="method.icon" class="w-5 h-5" />
                  <div>
                    <div class="font-medium">{{ method.label }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      {{ method.description }}
                    </div>
                  </div>
                </div>
                <div v-if="method.recommended" class="ml-auto">
                  <span class="px-2 py-1 text-xs bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded">
                    {{ $t('payment.recommended') }}
                  </span>
                </div>
              </label>
            </div>
          </div>

          <!-- Customer Information -->
          <div class="space-y-3">
            <label class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ $t('payment.customerInfo') }}
            </label>
            <div class="grid grid-cols-1 gap-3">
              <Input
                v-model="customerInfo.name"
                :placeholder="$t('payment.customerName')"
                required
              />
              <Input
                v-model="customerInfo.email"
                type="email"
                :placeholder="$t('payment.customerEmail')"
                required
              />
              <Input
                v-model="customerInfo.phone"
                :placeholder="$t('payment.customerPhone')"
              />
            </div>
          </div>
        </div>

        <!-- Step 2: Processing -->
        <div v-else-if="step === 'processing'" class="text-center space-y-4">
          <div class="animate-spin w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full mx-auto"></div>
          <div>
            <h3 class="font-semibold text-gray-900 dark:text-white">
              {{ $t('payment.processing.title') }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ $t('payment.processing.description') }}
            </p>
          </div>
        </div>

        <!-- Step 3: Payment -->
        <div v-else-if="step === 'payment'" class="space-y-4">
          <div class="text-center space-y-4">
            <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mx-auto">
              <DuotoneIcon name="credit-card" class="w-8 h-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900 dark:text-white">
                {{ $t('payment.redirect.title') }}
              </h3>
              <p class="text-sm text-gray-600 dark:text-gray-400">
                {{ paymentResponse?.instructions || $t('payment.redirect.description') }}
              </p>
            </div>
          </div>

          <!-- Payment Details -->
          <div v-if="paymentResponse" class="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">{{ $t('payment.paymentId') }}:</span>
                <span class="font-mono text-gray-900 dark:text-white">{{ paymentResponse.paymentId }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">{{ $t('payment.amount') }}:</span>
                <span class="font-medium text-gray-900 dark:text-white">
                  {{ formatCurrency(paymentResponse.amount, paymentResponse.currency) }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600 dark:text-gray-400">{{ $t('payment.expiresAt') }}:</span>
                <span class="text-gray-900 dark:text-white">{{ formatExpiry(paymentResponse.expiresAt) }}</span>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-3">
            <Button
              @click="redirectToPayment"
              class="flex-1"
              :disabled="!paymentResponse?.redirectUrl"
            >
              <DuotoneIcon name="arrow-top-right-on-square" class="w-4 h-4 mr-2" />
              {{ $t('payment.proceedToPayment') }}
            </Button>

            <!-- Development: Simulate Success -->
            <Button
              v-if="isDevelopment"
              @click="simulateSuccess"
              variant="outline"
              size="sm"
            >
              {{ $t('payment.simulate') }}
            </Button>
          </div>
        </div>

        <!-- Step 4: Success -->
        <div v-else-if="step === 'success'" class="text-center space-y-4">
          <div class="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
            <DuotoneIcon name="check-circle" class="w-8 h-8 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 class="font-semibold text-green-900 dark:text-green-100">
              {{ $t('payment.success.title') }}
            </h3>
            <p class="text-sm text-green-700 dark:text-green-300">
              {{ $t('payment.success.description') }}
            </p>
          </div>
          <div v-if="paymentResult" class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-green-700 dark:text-green-300">{{ $t('payment.transactionId') }}:</span>
                <span class="font-mono text-green-900 dark:text-green-100">{{ paymentResult.transactionId }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-green-700 dark:text-green-300">{{ $t('payment.paidAt') }}:</span>
                <span class="text-green-900 dark:text-green-100">{{ formatDate(paymentResult.paidAt) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 5: Failed -->
        <div v-else-if="step === 'failed'" class="text-center space-y-4">
          <div class="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto">
            <DuotoneIcon name="x-circle" class="w-8 h-8 text-red-600 dark:text-red-400" />
          </div>
          <div>
            <h3 class="font-semibold text-red-900 dark:text-red-100">
              {{ $t('payment.failed.title') }}
            </h3>
            <p class="text-sm text-red-700 dark:text-red-300">
              {{ error || $t('payment.failed.description') }}
            </p>
          </div>
        </div>
      </div>

      <!-- Dialog Footer -->
      <DialogFooter>
        <div class="flex justify-between w-full">
          <Button
            v-if="step === 'details'"
            variant="outline"
            @click="$emit('update:open', false)"
          >
            {{ $t('common.cancel') }}
          </Button>

          <Button
            v-if="step === 'details'"
            @click="handlePayment"
            :disabled="!canProceed || isProcessing"
            class="ml-auto"
          >
            <DuotoneIcon
              v-if="isProcessing"
              name="refresh"
              class="w-4 h-4 mr-2 animate-spin"
            />
            {{ $t('payment.createPayment') }}
          </Button>

          <Button
            v-if="step === 'success' || step === 'failed'"
            @click="handleClose"
            class="ml-auto"
          >
            {{ $t('common.close') }}
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
