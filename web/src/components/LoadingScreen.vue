<script setup lang="ts">
import { computed } from 'vue'
import LoadingSpinner from './LoadingSpinner.vue'

interface Props {
  title?: string
  subtitle?: string
  showLogo?: boolean
  variant?: 'light' | 'dark'
}

const props = withDefaults(defineProps<Props>(), {
  title: 'Loading...',
  subtitle: '',
  showLogo: true,
  variant: 'light'
})

const bgClasses = computed(() => {
  return props.variant === 'dark'
    ? 'bg-gray-900 text-white'
    : 'bg-white text-gray-900'
})

const logoClasses = computed(() => {
  return props.variant === 'dark'
    ? 'text-white'
    : 'text-gray-900'
})
</script>

<template>
  <div
    :class="[
      'fixed inset-0 z-50 flex flex-col items-center justify-center',
      bgClasses
    ]"
  >
    <!-- Logo Section -->
    <div v-if="showLogo" class="mb-8">
      <div :class="['text-4xl font-bold', logoClasses]">
        ElyPOS
      </div>
      <div :class="['text-sm text-center mt-2', logoClasses, 'opacity-60']">
        Point of Sale System
      </div>
    </div>

    <!-- Loading Spinner -->
    <div class="mb-6">
      <LoadingSpinner
        size="xl"
        :variant="variant === 'dark' ? 'white' : 'primary'"
      />
    </div>

    <!-- Loading Text -->
    <div class="text-center space-y-2">
      <h2 :class="['text-xl font-semibold', logoClasses]">
        {{ title }}
      </h2>
      <p
        v-if="subtitle"
        :class="['text-sm', logoClasses, 'opacity-70']"
      >
        {{ subtitle }}
      </p>
    </div>

    <!-- Progress Dots Animation -->
    <div class="mt-8 flex space-x-2">
      <div
        v-for="i in 3"
        :key="i"
        :class="[
          'w-2 h-2 rounded-full',
          logoClasses,
          'opacity-70 animate-pulse'
        ]"
        :style="{ animationDelay: `${(i - 1) * 200}ms` }"
      />
    </div>
  </div>
</template>

<style scoped>
@keyframes pulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}
</style>
