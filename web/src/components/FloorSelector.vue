<template>
  <div class="flex items-center gap-3">
    <!-- Floor Selector Label -->
    <div class="flex items-center gap-2">
      <DuotoneIcon name="building-4" class="w-4 h-4 text-primary" />
      <span class="text-sm font-medium">{{ $t('restaurant.floors.selector.label') }}:</span>
    </div>

    <!-- Simple Native Select Dropdown -->
    <div class="relative">
      <select
        :value="selectedFloorId"
        @change="onFloorChange(($event.target as HTMLSelectElement)?.value || '')"
        :disabled="loading"
        class="w-48 h-10 px-3 pr-8 text-sm border rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
      >
        <!-- All Floors Option -->
        <option value="">
          {{ $t('restaurant.floors.selector.allFloors') }} ({{ totalTables }})
        </option>

        <!-- Individual Floors -->
        <option
          v-for="floor in floors"
          :key="floor._id"
          :value="floor._id"
        >
          {{ floor.name }} ({{ floor.tableCount || 0 }})
        </option>
      </select>

      <!-- Floor Color Indicator -->
      <div
        v-if="selectedFloor"
        class="absolute left-3 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full border border-white shadow-sm pointer-events-none"
        :style="{ backgroundColor: selectedFloor.color || '#3B82F6' }"
      />

      <!-- Loading Indicator -->
      <div
        v-if="loading"
        class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none"
      >
        <DuotoneIcon name="loading-circle" class="w-4 h-4 animate-spin text-muted-foreground" />
      </div>
    </div>

    <!-- Floor Quick Actions -->
    <div class="flex items-center gap-1">
      <!-- Refresh Floors -->
      <button
        @click="$emit('refresh-floors')"
        :disabled="loading"
        :title="$t('restaurant.floors.selector.refresh')"
        class="p-2 text-muted-foreground hover:text-foreground hover:bg-muted rounded-md transition-colors disabled:opacity-50"
      >
        <DuotoneIcon
          :name="loading ? 'loading-circle' : 'refresh'"
          :class="`w-4 h-4 ${loading ? 'animate-spin' : ''}`"
        />
      </button>

      <!-- Create New Floor (Quick Access) -->
      <button
        @click="$emit('create-floor')"
        :title="$t('restaurant.floors.selector.createFloor')"
        class="p-2 text-primary hover:text-primary/80 hover:bg-primary/10 rounded-md transition-colors"
      >
        <DuotoneIcon name="plus" class="w-4 h-4" />
      </button>

      <!-- Manage Floors -->
      <button
        @click="$emit('manage-floors')"
        :title="$t('restaurant.floors.selector.manageFloors')"
        class="p-2 text-muted-foreground hover:text-foreground hover:bg-muted rounded-md transition-colors"
      >
        <DuotoneIcon name="settings" class="w-4 h-4" />
      </button>
    </div>

    <!-- Loading State Message -->
    <div v-if="loading" class="text-xs text-muted-foreground">
      {{ $t('common.loading') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { RestaurantFloor } from '../services/restaurant.service';
import DuotoneIcon from './DuotoneIcon.vue';

const { t } = useI18n();

// Props
interface Props {
  floors: RestaurantFloor[];
  selectedFloorId: string;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

// Emits
interface Emits {
  (e: 'update:selectedFloorId', value: string): void;
  (e: 'floor-changed', floorId: string | null): void;
  (e: 'manage-floors'): void;
  (e: 'refresh-floors'): void;
  (e: 'create-floor'): void;
}

const emit = defineEmits<Emits>();

// Computed properties
const selectedFloor = computed(() => {
  if (!props.selectedFloorId || !Array.isArray(props.floors)) return null;
  return props.floors.find(floor => floor._id === props.selectedFloorId) || null;
});

const totalTables = computed(() => {
  if (!Array.isArray(props.floors)) return 0;
  return props.floors.reduce((total, floor) => {
    return total + (typeof floor.tableCount === 'number' ? floor.tableCount : 0);
  }, 0);
});

// Handle floor selection change
function onFloorChange(floorId: string) {
  try {
    emit('update:selectedFloorId', floorId);
    emit('floor-changed', floorId || null);
  } catch (error) {
    console.error('Error in floor change handler:', error);
  }
}
</script>
