<script setup lang="ts">
import { computed } from 'vue'
import { useLanguage } from '@/composables/useLanguage'

const { currentLanguage, switchLanguage, getLanguageFlag, getLanguageName } = useLanguage()

const nextLanguage = computed(() => {
  return currentLanguage.value === 'en' ? 'km' : 'en'
})

const nextLanguageFlag = computed(() => {
  return getLanguageFlag(nextLanguage.value)
})

const nextLanguageName = computed(() => {
  return getLanguageName(nextLanguage.value)
})

const handleSwitch = () => {
  switchLanguage()
}
</script>

<template>
  <button
    @click="handleSwitch"
    class="flex items-center gap-2 px-4 py-2 bg-white/90 backdrop-blur-md border border-gray-200/50 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group"
    :title="`Switch to ${nextLanguageName}`"
  >
    <!-- Current Language -->
    <div class="flex items-center gap-2">
      <span class="text-lg">{{ getLanguageFlag(currentLanguage) }}</span>
      <span class="text-sm font-medium text-gray-700 hidden sm:inline">
        {{ getLanguageName(currentLanguage) }}
      </span>
    </div>

    <!-- Switch Arrow -->
    <div class="flex items-center">
      <svg
        class="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M8 7l4-4m0 0l4 4m-4-4v18"
        />
      </svg>
    </div>

    <!-- Next Language (Preview) -->
    <div class="flex items-center gap-2 opacity-60 group-hover:opacity-100 transition-opacity">
      <span class="text-lg">{{ nextLanguageFlag }}</span>
      <span class="text-sm font-medium text-gray-500 hidden sm:inline">
        {{ nextLanguageName }}
      </span>
    </div>
  </button>
</template>
