<script setup lang="ts">
import { ref } from 'vue'
import { useI18n } from 'vue-i18n'
import { useLanguage } from '@/composables/useLanguage'
import DuotoneIcon from './DuotoneIcon.vue'

const { currentLanguage, availableLanguages, getLanguageFlag, getLanguageName } = useLanguage()
const { locale } = useI18n()
const showDropdown = ref(false)

const switchLanguage = (langCode: string) => {
  locale.value = langCode
  localStorage.setItem('locale', langCode)
  showDropdown.value = false
}

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

// Close dropdown when clicking outside
const closeDropdown = () => {
  showDropdown.value = false
}
</script>

<template>
  <div class="relative">
    <!-- Language Toggle Button -->
    <button
      @click="toggleDropdown"
      class="flex items-center gap-2 px-3 py-2 text-[#1C1C1C]/70 hover:text-[#1C1C1C] text-[15px] font-medium transition-colors rounded-lg hover:bg-gray-50"
    >
      <span class="text-lg">{{ getLanguageFlag(currentLanguage) }}</span>
      <span class="hidden md:inline">{{ getLanguageName(currentLanguage) }}</span>
      <DuotoneIcon
        name="chevron-down"
        size="sm"
        :class="showDropdown ? 'rotate-180 transition-transform' : 'transition-transform'"
      />
    </button>

    <!-- Dropdown Menu -->
    <div
      v-if="showDropdown"
      @click.stop
      class="absolute top-full right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50"
    >
      <button
        v-for="language in availableLanguages"
        :key="language.code"
        @click="switchLanguage(language.code)"
        class="w-full flex items-center gap-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors"
        :class="{
          'bg-blue-50 text-blue-600': currentLanguage === language.code,
          'text-gray-700': currentLanguage !== language.code,
        }"
      >
        <span class="text-lg">{{ language.flag }}</span>
        <span class="font-medium">{{ language.name }}</span>
        <DuotoneIcon
          v-if="currentLanguage === language.code"
          name="check"
          size="sm"
          class="ml-auto text-blue-600"
        />
      </button>
    </div>

    <!-- Backdrop to close dropdown -->
    <div v-if="showDropdown" @click="closeDropdown" class="fixed inset-0 z-40"></div>
  </div>
</template>
