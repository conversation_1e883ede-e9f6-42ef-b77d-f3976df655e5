<script setup lang="ts">
import { computed, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { ChevronsUpDown, Plus, Building2 } from 'lucide-vue-next'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import CreateCompanyDialog from './CreateCompanyDialog.vue'

const authStore = useAuthStore()
const router = useRouter()

// Dialog state
const showCreateDialog = ref(false)

// Only show if user has companies
const shouldShow = computed(() => {
  const hasCompanies = authStore.userCompanies.length > 0
  console.log('CompanySwitcher shouldShow:', hasCompanies, 'companies:', authStore.userCompanies.length)
  return hasCompanies
})

// Active company (current company)
const activeCompany = computed(() => {
  const currentCompanyId = authStore.currentUser?.currentCompanyId
  const company = authStore.userCompanies.find(c => c._id === currentCompanyId) || authStore.userCompanies[0]
  console.log('Active company:', company?.name, 'from', authStore.userCompanies.length, 'total companies')
  return company
})

const handleSwitchCompany = async (companyId: string) => {
  if (companyId === authStore.currentUser?.currentCompanyId) {
    return // Already selected
  }

  try {
    await authStore.switchCompany(companyId)
    // Navigate to dashboard modules to show the new company's modules
    router.push('/dashboard/modules')
    // Small delay before refresh to show loading state
    setTimeout(() => {
      router.go(0) // Refresh page to update all company-specific data
    }, 500)
  } catch (error) {
    console.error('Failed to switch company:', error)
    // Show error message or handle error appropriately
  }
}

const handleCreateCompany = () => {
  showCreateDialog.value = true
}

const handleCompanyCreated = (company: any) => {
  // The company is automatically set as current in the dialog
  // Refresh the page to show new company data
  router.go(0)
}

// Get role display name
const getRoleDisplay = (role: string) => {
  return role.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<template>
  <div v-if="shouldShow" class="min-w-[240px]">
    <!-- Navigation Tab Style Container -->
    <div class="bg-gray-800 rounded-2xl p-1">
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button
            variant="ghost"
            class="group h-14 w-full justify-start gap-3 px-4 py-3 rounded-xl transition-all duration-200 bg-transparent hover:bg-gray-700 text-gray-200 hover:text-white border-0 data-[state=open]:bg-white data-[state=open]:text-gray-900 data-[state=open]:shadow-sm hover:shadow-md"
            :disabled="authStore.isSwitchingCompany"
          >
                    <!-- Company Logo/Icon -->
          <div class="flex aspect-square size-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 text-white shrink-0 shadow-sm transition-transform duration-200 group-hover:scale-105">
            <Building2 class="size-5" v-if="!authStore.isSwitchingCompany" />
            <div
              v-else
              class="size-5 border-2 border-white border-t-transparent rounded-full animate-spin"
            />
          </div>

          <!-- Company Info -->
          <div class="grid flex-1 text-left leading-tight ml-1">
            <span class="truncate font-semibold text-sm tracking-wide">
              {{ authStore.isSwitchingCompany ? 'Switching...' : (activeCompany?.name || 'No Company') }}
            </span>
            <span class="truncate text-xs opacity-70 font-medium">
              {{ authStore.isSwitchingCompany ? 'Please wait' : getRoleDisplay(activeCompany?.role || 'member') }}
            </span>
          </div>

          <!-- Chevron -->
          <ChevronsUpDown class="ml-auto size-4 shrink-0 opacity-50 transition-transform duration-200 group-hover:scale-110" v-if="!authStore.isSwitchingCompany" />
        </Button>
      </DropdownMenuTrigger>

              <DropdownMenuContent
          class="w-[--reka-dropdown-menu-trigger-width] min-w-64 rounded-xl border-gray-600 bg-gray-800 shadow-xl"
          align="start"
          side="bottom"
          :side-offset="8"
        >
          <DropdownMenuLabel class="text-xs text-gray-400 font-semibold uppercase tracking-wider px-3 py-2">
            Switch Companies
          </DropdownMenuLabel>

          <!-- Company List -->
          <DropdownMenuItem
            v-for="(company, index) in authStore.userCompanies"
            :key="company._id"
            class="group gap-3 p-3 cursor-pointer rounded-lg mx-1 transition-all duration-200 hover:bg-gray-700 focus:bg-gray-700"
            @click="handleSwitchCompany(company._id)"
          >
            <div class="flex size-8 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 text-white shrink-0 shadow-sm group-hover:scale-105 transition-transform duration-200">
              <Building2 class="size-4 shrink-0" />
            </div>
            <div class="flex flex-col flex-1">
              <span class="font-semibold text-gray-200 text-sm">{{ company.name }}</span>
              <span class="text-xs text-gray-400 font-medium">{{ getRoleDisplay(company.role) }}</span>
            </div>
            <div class="flex items-center gap-2">
              <!-- Current Company Indicator -->
              <div
                v-if="company._id === authStore.currentUser?.currentCompanyId"
                class="w-2 h-2 rounded-full bg-green-500 animate-pulse"
              ></div>
              <DropdownMenuShortcut
                v-if="index < 9"
                class="text-gray-500 bg-gray-700 px-1.5 py-0.5 rounded text-xs font-mono"
              >
                ⌘{{ index + 1 }}
              </DropdownMenuShortcut>
            </div>
          </DropdownMenuItem>

          <DropdownMenuSeparator class="bg-gray-700 my-2" />

          <!-- Create New Company -->
          <DropdownMenuItem class="group gap-3 p-3 cursor-pointer rounded-lg mx-1 transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-blue-700 focus:bg-gradient-to-r focus:from-blue-600 focus:to-blue-700" @click="handleCreateCompany">
            <div class="flex size-8 items-center justify-center rounded-lg border-2 border-dashed border-gray-600 group-hover:border-blue-400 transition-colors duration-200">
              <Plus class="size-4 text-gray-400 group-hover:text-blue-200" />
            </div>
            <div class="flex flex-col flex-1">
              <span class="font-semibold text-gray-300 group-hover:text-white text-sm">
                Create Company
              </span>
              <span class="text-xs text-gray-500 group-hover:text-blue-200">
                Start a new organization
              </span>
            </div>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Create Company Dialog -->
    <CreateCompanyDialog
      :open="showCreateDialog"
      @update:open="showCreateDialog = $event"
      @company-created="handleCompanyCreated"
    />
  </div>
</template>
