<script setup lang="ts">
import { computed, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import {
  User,
  Settings,
  CreditCard,
  Bell,
  HelpCircle,
  LogOut,
  ChevronsUpDown
} from 'lucide-vue-next'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'

const authStore = useAuthStore()
const router = useRouter()

const currentUser = computed(() => authStore.currentUser)
const currentCompany = computed(() => authStore.currentCompany)

const userInitials = computed(() => {
  if (!currentUser.value) return 'U'
  const first = currentUser.value.firstName?.[0] || ''
  const last = currentUser.value.lastName?.[0] || ''
  return (first + last).toUpperCase() || 'U'
})

const handleNavigation = (path: string) => {
  router.push(path)
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/auth/login')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

// Menu items with enhanced styling
const menuItems = [
  {
    label: 'Profile',
    icon: User,
    action: () => handleNavigation('/dashboard/settings'),
    shortcut: '⌘P',
    description: 'personal settings'
  },
  {
    label: 'Settings',
    icon: Settings,
    action: () => handleNavigation('/dashboard/settings'),
    shortcut: '⌘,',
    description: 'preferences'
  },
  {
    label: 'Billing',
    icon: CreditCard,
    action: () => handleNavigation('/dashboard/payment-history'),
    shortcut: '⌘B',
    description: 'payments & plans'
  },
  {
    label: 'Notifications',
    icon: Bell,
    action: () => handleNavigation('/dashboard/settings'),
    shortcut: '⌘N',
    description: 'alerts & updates'
  },
  {
    label: 'Help',
    icon: HelpCircle,
    action: () => window.open('https://help.elypos.com', '_blank'),
    shortcut: '⌘?',
    description: 'support center'
  }
]
</script>

<template>
    <div class="min-w-[240px]">
    <!-- Navigation Tab Style Container -->
    <div class="bg-gray-800 rounded-2xl p-1">
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button
            variant="ghost"
            class="group h-14 w-full justify-start gap-3 px-4 py-3 rounded-xl transition-all duration-200 bg-transparent hover:bg-gray-700 text-gray-200 hover:text-white border-0 data-[state=open]:bg-white data-[state=open]:text-gray-900 data-[state=open]:shadow-sm hover:shadow-md"
            :disabled="authStore.isLoading"
          >
            <!-- User Avatar/Icon -->
            <div class="flex aspect-square size-10 items-center justify-center rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 text-white shrink-0 shadow-sm transition-transform duration-200 group-hover:scale-105">
              <Avatar class="h-10 w-10">
                <AvatarImage
                  :src="currentUser?.profilePicture || ''"
                  :alt="currentUser?.firstName + ' ' + currentUser?.lastName"
                  class="rounded-xl"
      />
                <AvatarFallback class="bg-transparent text-white text-sm font-bold rounded-xl">
                  {{ userInitials }}
                </AvatarFallback>
              </Avatar>
            </div>

            <!-- User Info -->
            <div class="grid flex-1 text-left leading-tight ml-1">
              <span class="truncate font-semibold text-sm tracking-wide">
                {{ authStore.isLoading ? 'Loading...' : `${currentUser?.firstName} ${currentUser?.lastName}` }}
              </span>
              <span class="truncate text-xs opacity-70 font-medium">
                {{ authStore.isLoading ? 'Please wait' : (currentUser?.email || 'No Email') }}
              </span>
      </div>

            <!-- Chevron -->
            <ChevronsUpDown class="ml-auto size-4 shrink-0 opacity-50 transition-transform duration-200 group-hover:scale-110" v-if="!authStore.isLoading" />
          </Button>
        </DropdownMenuTrigger>

            <DropdownMenuContent
        class="w-[--reka-dropdown-menu-trigger-width] min-w-64 rounded-xl border-gray-600 bg-gray-800 shadow-xl"
        align="start"
        side="bottom"
        :side-offset="8"
      >
        <DropdownMenuLabel class="text-xs text-gray-400 font-semibold uppercase tracking-wider px-3 py-2">
          User Profile
        </DropdownMenuLabel>

        <!-- Menu Items with Enhanced Styling -->
        <DropdownMenuItem
          v-for="item in menuItems"
          :key="item.label"
          class="group gap-3 p-3 cursor-pointer rounded-lg mx-1 transition-all duration-200 hover:bg-gray-700 focus:bg-gray-700"
          @click="item.action"
        >
          <div class="flex size-8 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 text-white shrink-0 shadow-sm group-hover:scale-105 transition-transform duration-200">
            <component :is="item.icon" class="size-4 shrink-0" />
          </div>
          <div class="flex flex-col flex-1">
            <span class="font-semibold text-gray-200 text-sm">{{ item.label }}</span>
            <span class="text-xs text-gray-400 font-medium">{{ item.description }}</span>
          </div>
          <DropdownMenuShortcut
            class="text-gray-500 bg-gray-700 px-1.5 py-0.5 rounded text-xs font-mono"
          >
            {{ item.shortcut }}
          </DropdownMenuShortcut>
        </DropdownMenuItem>

        <DropdownMenuSeparator class="bg-gray-700 my-2" />

        <!-- Logout with Enhanced Styling -->
        <DropdownMenuItem
              @click="handleLogout"
          class="group gap-3 p-3 cursor-pointer rounded-lg mx-1 transition-all duration-200 hover:bg-gradient-to-r hover:from-red-600 hover:to-red-700 focus:bg-gradient-to-r focus:from-red-600 focus:to-red-700"
            >
          <div class="flex size-8 items-center justify-center rounded-lg border-2 border-dashed border-red-500 group-hover:border-red-400 transition-colors duration-200">
            <LogOut class="size-4 text-red-400 group-hover:text-red-200" />
          </div>
          <div class="flex flex-col flex-1">
            <span class="font-semibold text-red-400 group-hover:text-white text-sm">
              Sign Out
            </span>
            <span class="text-xs text-red-500 group-hover:text-red-200">
              End your session
            </span>
        </div>
          <DropdownMenuShortcut
            class="text-gray-500 bg-gray-700 px-1.5 py-0.5 rounded text-xs font-mono group-hover:bg-red-800 group-hover:text-red-200"
          >
            ⌘Q
          </DropdownMenuShortcut>
        </DropdownMenuItem>
              </DropdownMenuContent>
      </DropdownMenu>
      </div>
  </div>
</template>


