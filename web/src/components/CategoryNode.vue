<script setup lang="ts">
import { computed } from 'vue'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import {
  ChevronRight,
  ChevronDown,
  Edit,
  Trash2,
  MoreHorizontal,
  Plus,
  Package,
} from 'lucide-vue-next'
import type { Category } from '../../../shared/types/pos'

interface CategoryNodeProps {
  category: Category
  expandedCategories: Set<string>
}

interface CategoryNodeEmits {
  (e: 'toggle-expand', categoryId: string): void
  (e: 'edit', category: Category): void
  (e: 'delete', category: Category): void
  (e: 'create-subcategory', category: Category): void
}

const props = defineProps<CategoryNodeProps>()
const emit = defineEmits<CategoryNodeEmits>()

const hasChildren = computed(() => props.category.children && props.category.children.length > 0)
const isExpanded = computed(() => props.expandedCategories.has(props.category._id))
</script>

<template>
  <div class="border rounded-lg p-3">
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-3 flex-1">
        <!-- Expand/Collapse Button -->
        <Button
          v-if="hasChildren"
          variant="ghost"
          size="sm"
          class="p-0 h-6 w-6"
          @click="emit('toggle-expand', category._id)"
        >
          <ChevronRight v-if="!isExpanded" class="w-4 h-4" />
          <ChevronDown v-else class="w-4 h-4" />
        </Button>
        <div v-else class="w-6"></div>

        <!-- Category Info -->
        <div class="flex items-center gap-2 flex-1">
          <!-- Color Indicator -->
          <div
            class="w-3 h-3 rounded-full"
            :style="{ backgroundColor: category.color }"
          ></div>

          <!-- Category Name -->
          <span class="font-medium">{{ category.name }}</span>

          <!-- Product Count Badge -->
          <Badge variant="secondary" class="text-xs">
            <Package class="w-3 h-3 mr-1" />
            {{ category.productCount || 0 }}
          </Badge>

          <!-- Level Indicator -->
          <Badge variant="outline" class="text-xs">
            Level {{ category.level }}
          </Badge>
        </div>
      </div>

      <!-- Actions Menu -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
            <MoreHorizontal class="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem @click="emit('edit', category)">
            <Edit class="w-4 h-4 mr-2" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem @click="emit('create-subcategory', category)">
            <Plus class="w-4 h-4 mr-2" />
            Add Subcategory
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            @click="emit('delete', category)"
            class="text-red-600 focus:text-red-600"
          >
            <Trash2 class="w-4 h-4 mr-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>

    <!-- Category Description -->
    <div v-if="category.description" class="mt-2 text-sm text-muted-foreground">
      {{ category.description }}
    </div>

    <!-- Children (Recursive) -->
    <div v-if="hasChildren && isExpanded" class="mt-3 ml-6 space-y-2">
      <CategoryNode
        v-for="child in category.children"
        :key="child._id"
        :category="child"
        :expanded-categories="expandedCategories"
        @toggle-expand="emit('toggle-expand', $event)"
        @edit="emit('edit', $event)"
        @delete="emit('delete', $event)"
        @create-subcategory="emit('create-subcategory', $event)"
      />
    </div>
  </div>
</template>
