<template>
  <div class="flex items-center justify-between">
    <div class="text-sm text-gray-700 dark:text-gray-300">
      Page {{ currentPage }} of {{ totalPages }}
    </div>

    <div class="flex items-center space-x-2">
      <button
        @click="$emit('page-change', currentPage - 1)"
        :disabled="!hasPrev"
        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
      >
        Previous
      </button>

      <button
        @click="$emit('page-change', currentPage + 1)"
        :disabled="!hasNext"
        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
      >
        Next
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentPage: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

interface Emits {
  (e: 'page-change', page: number): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
