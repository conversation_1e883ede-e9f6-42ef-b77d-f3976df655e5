<template>
  <div class="h-64 flex items-center justify-center">
    <div v-if="loading" class="text-gray-500 dark:text-gray-400">
      Loading chart...
    </div>
    <div v-else-if="!data || data.length === 0" class="text-gray-500 dark:text-gray-400">
      No data available
    </div>
    <div v-else class="w-full h-full flex items-end space-x-2 p-4">
      <!-- Simple bar chart -->
      <div
        v-for="(item, index) in data"
        :key="index"
        class="flex-1 bg-blue-500 rounded-t min-h-[20px]"
        :style="{ height: `${Math.max((item.revenue / maxRevenue) * 100, 10)}%` }"
        :title="`${item.period}: $${item.revenue}`"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  data: Array<{ period: string; revenue: number; transactions: number }>
  loading?: boolean
  period?: 'daily' | 'monthly'
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  period: 'monthly'
})

const maxRevenue = computed(() => {
  if (!props.data || props.data.length === 0) return 0
  return Math.max(...props.data.map(item => item.revenue))
})
</script>
