<template>
  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="badgeClass">
    <div class="w-2 h-2 rounded-full mr-1.5" :class="dotClass"></div>
    {{ statusText }}
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PaymentStatus } from '@/types/payment'

interface Props {
  status: PaymentStatus
}

const props = defineProps<Props>()

const statusConfig = {
  pending: {
    text: 'Pending',
    badge: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    dot: 'bg-yellow-400'
  },
  processing: {
    text: 'Processing',
    badge: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
    dot: 'bg-blue-400'
  },
  completed: {
    text: 'Completed',
    badge: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    dot: 'bg-green-400'
  },
  failed: {
    text: 'Failed',
    badge: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    dot: 'bg-red-400'
  },
  cancelled: {
    text: 'Cancelled',
    badge: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
    dot: 'bg-gray-400'
  }
}

const statusText = computed(() => statusConfig[props.status]?.text || props.status)
const badgeClass = computed(() => statusConfig[props.status]?.badge || statusConfig.pending.badge)
const dotClass = computed(() => statusConfig[props.status]?.dot || statusConfig.pending.dot)
</script>
