<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <!-- Loading State -->
    <div v-if="loading" class="animate-pulse">
      <div class="flex items-center justify-between mb-4">
        <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
        <div class="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
      <div class="h-8 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
      <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
    </div>

    <!-- Content -->
    <div v-else>
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-sm font-medium text-gray-600 dark:text-gray-400">
          {{ title }}
        </h4>
        <div class="flex items-center justify-center w-8 h-8 rounded-lg" :class="iconBgClass">
          <DuotoneIcon :name="icon" class="w-4 h-4" :class="iconColorClass" />
        </div>
      </div>

      <div class="flex items-baseline justify-between">
        <p class="text-2xl font-bold text-gray-900 dark:text-white">
          {{ value }}
        </p>

        <div v-if="change !== undefined" class="flex items-center space-x-1">
          <DuotoneIcon
            :name="change >= 0 ? 'arrow-up' : 'arrow-down'"
            class="w-4 h-4"
            :class="changeIconClass"
          />
          <span class="text-sm font-medium" :class="changeTextClass">
            {{ Math.abs(change).toFixed(1) }}%
          </span>
        </div>
      </div>

      <p v-if="change !== undefined" class="text-xs text-gray-500 dark:text-gray-400 mt-1">
        {{ change >= 0 ? 'Increase' : 'Decrease' }} from last period
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import DuotoneIcon from '@/components/DuotoneIcon.vue'

interface Props {
  title: string
  value: string
  change?: number
  icon: string
  loading?: boolean
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red'
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  color: 'blue'
})

const colorClasses = {
  blue: {
    bg: 'bg-blue-50 dark:bg-blue-900/20',
    icon: 'text-blue-600 dark:text-blue-400'
  },
  green: {
    bg: 'bg-green-50 dark:bg-green-900/20',
    icon: 'text-green-600 dark:text-green-400'
  },
  purple: {
    bg: 'bg-purple-50 dark:bg-purple-900/20',
    icon: 'text-purple-600 dark:text-purple-400'
  },
  orange: {
    bg: 'bg-orange-50 dark:bg-orange-900/20',
    icon: 'text-orange-600 dark:text-orange-400'
  },
  red: {
    bg: 'bg-red-50 dark:bg-red-900/20',
    icon: 'text-red-600 dark:text-red-400'
  }
}

const iconBgClass = computed(() => colorClasses[props.color].bg)
const iconColorClass = computed(() => colorClasses[props.color].icon)

const changeIconClass = computed(() => {
  if (props.change === undefined) return ''
  return props.change >= 0
    ? 'text-green-500 dark:text-green-400'
    : 'text-red-500 dark:text-red-400'
})

const changeTextClass = computed(() => {
  if (props.change === undefined) return ''
  return props.change >= 0
    ? 'text-green-600 dark:text-green-400'
    : 'text-red-600 dark:text-red-400'
})
</script>
