<template>
  <div class="overflow-x-auto">
    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
      <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
        <tr>
          <th scope="col" class="px-6 py-3">
            <button @click="handleSort('paymentId')" class="flex items-center space-x-1 hover:text-gray-900 dark:hover:text-white">
              <span>Payment ID</span>
              <DuotoneIcon
                :name="getSortIcon('paymentId')"
                class="w-3 h-3"
              />
            </button>
          </th>
          <th scope="col" class="px-6 py-3">
            <button @click="handleSort('module')" class="flex items-center space-x-1 hover:text-gray-900 dark:hover:text-white">
              <span>Module</span>
              <DuotoneIcon
                :name="getSortIcon('module')"
                class="w-3 h-3"
              />
            </button>
          </th>
          <th scope="col" class="px-6 py-3">
            <button @click="handleSort('amount')" class="flex items-center space-x-1 hover:text-gray-900 dark:hover:text-white">
              <span>Amount</span>
              <DuotoneIcon
                :name="getSortIcon('amount')"
                class="w-3 h-3"
              />
            </button>
          </th>
          <th scope="col" class="px-6 py-3">Customer</th>
          <th scope="col" class="px-6 py-3">
            <button @click="handleSort('status')" class="flex items-center space-x-1 hover:text-gray-900 dark:hover:text-white">
              <span>Status</span>
              <DuotoneIcon
                :name="getSortIcon('status')"
                class="w-3 h-3"
              />
            </button>
          </th>
          <th scope="col" class="px-6 py-3">Payment Method</th>
          <th scope="col" class="px-6 py-3">
            <button @click="handleSort('createdAt')" class="flex items-center space-x-1 hover:text-gray-900 dark:hover:text-white">
              <span>Date</span>
              <DuotoneIcon
                :name="getSortIcon('createdAt')"
                class="w-3 h-3"
              />
            </button>
          </th>
          <th scope="col" class="px-6 py-3">Actions</th>
        </tr>
      </thead>
      <tbody>
        <!-- Loading State -->
        <template v-if="loading">
          <tr v-for="i in 5" :key="i">
            <td v-for="j in 8" :key="j" class="px-6 py-4">
              <div class="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </td>
          </tr>
        </template>

        <!-- No Data State -->
        <tr v-else-if="payments.length === 0">
          <td colspan="8" class="px-6 py-12 text-center">
            <div class="flex flex-col items-center justify-center">
              <DuotoneIcon name="receipt" class="w-12 h-12 text-gray-400 mb-4" />
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No payments found
              </h3>
              <p class="text-gray-500 dark:text-gray-400">
                No payment records match your current filters.
              </p>
            </div>
          </td>
        </tr>

        <!-- Payment Rows -->
        <tr
          v-else
          v-for="payment in payments"
          :key="payment._id"
          class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          <!-- Payment ID -->
          <td class="px-6 py-4">
            <div class="flex flex-col">
              <span class="font-medium text-gray-900 dark:text-white">
                {{ payment.paymentId }}
              </span>
              <span v-if="payment.transactionId" class="text-xs text-gray-500 dark:text-gray-400">
                {{ payment.transactionId }}
              </span>
            </div>
          </td>

          <!-- Module -->
          <td class="px-6 py-4">
            <div class="flex items-center space-x-2">
              <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900">
                <DuotoneIcon :name="getModuleIcon(payment.module)" class="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <span class="font-medium text-gray-900 dark:text-white">{{ payment.module }}</span>
                <div v-if="payment.subscriptionDetails?.wasTrialConversion" class="text-xs text-orange-600 dark:text-orange-400">
                  Trial Conversion
                </div>
              </div>
            </div>
          </td>

          <!-- Amount -->
          <td class="px-6 py-4">
            <div class="flex flex-col">
              <span class="font-semibold text-gray-900 dark:text-white">
                {{ formatCurrency(payment.amount, payment.currency) }}
              </span>
              <span v-if="payment.originalAmount !== payment.amount" class="text-xs text-gray-500 dark:text-gray-400 line-through">
                {{ formatCurrency(payment.originalAmount, payment.currency) }}
              </span>
            </div>
          </td>

          <!-- Customer -->
          <td class="px-6 py-4">
            <div v-if="payment.customerInfo" class="flex flex-col">
              <span class="font-medium text-gray-900 dark:text-white">
                {{ payment.customerInfo.name }}
              </span>
              <span class="text-xs text-gray-500 dark:text-gray-400">
                {{ payment.customerInfo.email }}
              </span>
            </div>
            <span v-else class="text-gray-400 dark:text-gray-500">-</span>
          </td>

          <!-- Status -->
          <td class="px-6 py-4">
            <PaymentStatusBadge :status="payment.status" />
          </td>

          <!-- Payment Method -->
          <td class="px-6 py-4">
            <div class="flex items-center space-x-2">
              <DuotoneIcon :name="getPaymentMethodIcon(payment.paymentMethod)" class="w-4 h-4 text-gray-500" />
              <span class="text-gray-900 dark:text-white">{{ formatPaymentMethod(payment.paymentMethod) }}</span>
            </div>
          </td>

          <!-- Date -->
          <td class="px-6 py-4">
            <div class="flex flex-col">
              <span class="text-gray-900 dark:text-white">
                {{ formatDate(payment.createdAt) }}
              </span>
              <span v-if="payment.paidAt" class="text-xs text-green-600 dark:text-green-400">
                Paid: {{ formatDate(payment.paidAt) }}
              </span>
            </div>
          </td>

          <!-- Actions -->
          <td class="px-6 py-4">
            <div class="flex items-center space-x-2">
              <button
                @click="$emit('view-details', payment)"
                class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                title="View Details"
              >
                <DuotoneIcon name="eye" class="w-4 h-4" />
              </button>

              <button
                v-if="payment.status === 'pending'"
                @click="$emit('simulate-success', payment.paymentId)"
                class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300"
                title="Simulate Success"
              >
                <DuotoneIcon name="check" class="w-4 h-4" />
              </button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { PaymentHistory, PaymentMethod, PaymentStatus } from '@/types/payment'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import PaymentStatusBadge from './PaymentStatusBadge.vue'

interface Props {
  payments: PaymentHistory[]
  loading?: boolean
  sortField?: string
  sortOrder?: 'asc' | 'desc'
}

interface Emits {
  (e: 'sort', field: string, order: 'asc' | 'desc'): void
  (e: 'view-details', payment: PaymentHistory): void
  (e: 'simulate-success', paymentId: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  sortField: 'createdAt',
  sortOrder: 'desc'
})

const emit = defineEmits<Emits>()

const formatCurrency = (amount: number, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

const formatDate = (date: string | Date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

const formatPaymentMethod = (method: PaymentMethod) => {
  const methods: Record<PaymentMethod, string> = {
    'KHQR': 'KHQR',
    'ACLEDA_POS': 'ACLEDA POS',
    'ACLEDA_MOBILE': 'ACLEDA Mobile',
    'BANK_TRANSFER': 'Bank Transfer',
    'ACLEDA_ECOMMERCE': 'ACLEDA E-commerce',
    'ACLEDA_REDIRECT': 'ACLEDA Redirect'
  }
  return methods[method] || method
}

const getModuleIcon = (module: string) => {
  const icons: Record<string, string> = {
    'POS': 'shopping-cart',
    'LOAN': 'currency-dollar',
    'ERP': 'building-office',
    'INVENTORY': 'cube'
  }
  return icons[module] || 'cog'
}

const getPaymentMethodIcon = (method: PaymentMethod) => {
  const icons: Record<PaymentMethod, string> = {
    'KHQR': 'qr-code',
    'ACLEDA_POS': 'credit-card',
    'ACLEDA_MOBILE': 'device-phone-mobile',
    'BANK_TRANSFER': 'building-library',
    'ACLEDA_ECOMMERCE': 'globe-americas',
    'ACLEDA_REDIRECT': 'arrow-top-right-on-square'
  }
  return icons[method] || 'credit-card'
}

const getSortIcon = (field: string) => {
  if (props.sortField !== field) return 'arrows-up-down'
  return props.sortOrder === 'asc' ? 'arrow-up' : 'arrow-down'
}

const handleSort = (field: string) => {
  let order: 'asc' | 'desc' = 'desc'

  if (props.sortField === field) {
    order = props.sortOrder === 'asc' ? 'desc' : 'asc'
  }

  emit('sort', field, order)
}
</script>
