<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
    <div class="flex flex-col lg:flex-row gap-4">
      <!-- Search -->
      <div class="flex-1">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Search Payments
        </label>
        <div class="relative">
          <DuotoneIcon name="magnifying-glass" class="absolute left-3 top-3 w-4 h-4 text-gray-400" />
          <input
            :value="search"
            @input="handleSearchInput"
            type="text"
            placeholder="Search by payment ID, description, customer..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <!-- Status Filter -->
      <div class="w-full lg:w-48">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Status
        </label>
        <select
          :value="filters.status || ''"
          @change="handleStatusChange"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">All Statuses</option>
          <option value="pending">Pending</option>
          <option value="processing">Processing</option>
          <option value="completed">Completed</option>
          <option value="failed">Failed</option>
          <option value="cancelled">Cancelled</option>
        </select>
      </div>

      <!-- Module Filter -->
      <div class="w-full lg:w-48">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Module
        </label>
        <select
          :value="filters.module || ''"
          @change="handleModuleChange"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">All Modules</option>
          <option value="POS">POS</option>
          <option value="LOAN">Loan</option>
          <option value="ERP">ERP</option>
          <option value="INVENTORY">Inventory</option>
        </select>
      </div>

      <!-- Payment Method Filter -->
      <div class="w-full lg:w-48">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Payment Method
        </label>
        <select
          :value="filters.paymentMethod || ''"
          @change="handlePaymentMethodChange"
          class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">All Methods</option>
          <option value="KHQR">KHQR</option>
          <option value="ACLEDA_POS">ACLEDA POS</option>
          <option value="ACLEDA_MOBILE">ACLEDA Mobile</option>
          <option value="BANK_TRANSFER">Bank Transfer</option>
          <option value="ACLEDA_ECOMMERCE">ACLEDA E-commerce</option>
          <option value="ACLEDA_REDIRECT">ACLEDA Redirect</option>
        </select>
      </div>

      <!-- Actions -->
      <div class="flex items-end space-x-2">
        <button
          @click="toggleAdvancedFilters"
          class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:ring-2 focus:ring-blue-500"
        >
          <DuotoneIcon name="adjustments-horizontal" class="w-4 h-4 mr-2" />
          Filters
        </button>

        <button
          @click="handleReset"
          class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 focus:ring-2 focus:ring-blue-500"
        >
          <DuotoneIcon name="x-mark" class="w-4 h-4 mr-2" />
          Reset
        </button>
      </div>
    </div>

    <!-- Advanced Filters -->
    <div v-if="showAdvancedFilters" class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- Date Range -->
        <div class="md:col-span-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Date Range
          </label>
          <div class="flex items-center space-x-2">
                         <input
               :value="filters.dateFrom || ''"
               @change="(e) => handleFilterChange('dateFrom', (e.target as HTMLInputElement).value)"
               type="date"
               class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             />
             <span class="text-gray-500 dark:text-gray-400">to</span>
             <input
               :value="filters.dateTo || ''"
               @change="(e) => handleFilterChange('dateTo', (e.target as HTMLInputElement).value)"
               type="date"
               class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
             />
          </div>
        </div>

        <!-- Amount Range -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Min Amount
          </label>
          <input
            :value="filters.amountMin || ''"
            @input="handleFilterChange('amountMin', parseFloat($event.target.value) || undefined)"
            type="number"
            step="0.01"
            placeholder="0.00"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Max Amount
          </label>
          <input
            :value="filters.amountMax || ''"
            @input="handleFilterChange('amountMax', parseFloat($event.target.value) || undefined)"
            type="number"
            step="0.01"
            placeholder="999999.99"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      <!-- Quick Filters -->
      <div class="mt-4">
        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Quick Filters
        </label>
        <div class="flex flex-wrap gap-2">
          <button
            @click="applyQuickFilter('today')"
            class="px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30"
          >
            Today
          </button>
          <button
            @click="applyQuickFilter('week')"
            class="px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30"
          >
            This Week
          </button>
          <button
            @click="applyQuickFilter('month')"
            class="px-3 py-1 text-xs font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30"
          >
            This Month
          </button>
          <button
            @click="applyQuickFilter('completed')"
            class="px-3 py-1 text-xs font-medium text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-full hover:bg-green-100 dark:hover:bg-green-900/30"
          >
            Completed Only
          </button>
          <button
            @click="applyQuickFilter('pending')"
            class="px-3 py-1 text-xs font-medium text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-full hover:bg-yellow-100 dark:hover:bg-yellow-900/30"
          >
            Pending Only
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { PaymentHistoryFilter } from '@/types/payment'
import DuotoneIcon from '@/components/DuotoneIcon.vue'

interface Props {
  filters: PaymentHistoryFilter
  search: string
}

interface Emits {
  (e: 'update:filters', filters: PaymentHistoryFilter): void
  (e: 'update:search', search: string): void
  (e: 'filter-change'): void
  (e: 'search'): void
  (e: 'reset'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const showAdvancedFilters = ref(false)

const handleSearchInput = (event: Event) => {
  const value = (event.target as HTMLInputElement).value
  emit('update:search', value)
  emit('search')
}

const handleFilterChange = (key: keyof PaymentHistoryFilter, value: any) => {
  const newFilters = { ...props.filters }

  if (value === '' || value === undefined || value === null) {
    delete newFilters[key]
  } else {
    newFilters[key] = value
  }

  emit('update:filters', newFilters)
  emit('filter-change')
}

const handleStatusChange = (event: Event) => {
  const value = (event.target as HTMLSelectElement).value
  handleFilterChange('status', value)
}

const handleModuleChange = (event: Event) => {
  const value = (event.target as HTMLSelectElement).value
  handleFilterChange('module', value)
}

const handlePaymentMethodChange = (event: Event) => {
  const value = (event.target as HTMLSelectElement).value
  handleFilterChange('paymentMethod', value)
}

const handleReset = () => {
  emit('update:filters', {})
  emit('update:search', '')
  emit('reset')
}

const toggleAdvancedFilters = () => {
  showAdvancedFilters.value = !showAdvancedFilters.value
}

const applyQuickFilter = (filter: string) => {
  const now = new Date()
  const newFilters = { ...props.filters }

  switch (filter) {
    case 'today':
      newFilters.dateFrom = now.toISOString().split('T')[0]
      newFilters.dateTo = now.toISOString().split('T')[0]
      break
    case 'week':
      const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))
      newFilters.dateFrom = weekStart.toISOString().split('T')[0]
      newFilters.dateTo = new Date().toISOString().split('T')[0]
      break
    case 'month':
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)
      newFilters.dateFrom = monthStart.toISOString().split('T')[0]
      newFilters.dateTo = new Date().toISOString().split('T')[0]
      break
    case 'completed':
      newFilters.status = 'completed'
      break
    case 'pending':
      newFilters.status = 'pending'
      break
  }

  emit('update:filters', newFilters)
  emit('filter-change')
}
</script>
