<template>
  <div class="space-y-4">
    <div v-if="loading" class="animate-pulse space-y-2">
      <div v-for="i in 3" :key="i" class="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
    </div>

    <div v-else-if="!data || Object.keys(data).length === 0" class="text-gray-500 dark:text-gray-400 text-center py-8">
      No payment method data available
    </div>

    <div v-else class="space-y-3">
      <div v-for="[method, stats] in sortedData" :key="method" class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div class="w-3 h-3 rounded-full bg-blue-500"></div>
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            {{ formatMethodName(method) }}
          </span>
        </div>
        <div class="text-right">
          <div class="text-sm font-semibold text-gray-900 dark:text-white">
            ${{ stats.amount.toFixed(2) }}
          </div>
          <div class="text-xs text-gray-500 dark:text-gray-400">
            {{ stats.count }} transactions
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  data: Record<string, { amount: number; count: number }>
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const sortedData = computed(() => {
  return Object.entries(props.data).sort(([, a], [, b]) => b.amount - a.amount)
})

const formatMethodName = (method: string) => {
  const names: Record<string, string> = {
    'KHQR': 'KHQR',
    'ACLEDA_POS': 'ACLEDA POS',
    'ACLEDA_MOBILE': 'ACLEDA Mobile',
    'BANK_TRANSFER': 'Bank Transfer',
    'ACLEDA_ECOMMERCE': 'ACLEDA E-commerce',
    'ACLEDA_REDIRECT': 'ACLEDA Redirect'
  }
  return names[method] || method
}
</script>
