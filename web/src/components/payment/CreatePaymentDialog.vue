<template>
  <div v-if="open" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex min-h-screen items-center justify-center p-4">
      <div class="fixed inset-0 bg-black opacity-50" @click="$emit('update:open', false)"></div>

      <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Create Payment
        </h3>

        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Payment creation dialog will be implemented here.
        </p>

        <div class="flex justify-end space-x-3">
          <button
            @click="$emit('update:open', false)"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Cancel
          </button>
          <button
            @click="handleCreate"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700"
          >
            Create Payment
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  open: boolean
}

interface Emits {
  (e: 'update:open', open: boolean): void
  (e: 'payment-created'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const handleCreate = () => {
  // Mock payment creation
  emit('payment-created')
  emit('update:open', false)
}
</script>
