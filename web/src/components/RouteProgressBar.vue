<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const isLoading = ref(false)
const progress = ref(0)

let progressTimer: NodeJS.Timeout | null = null

const startProgress = () => {
  isLoading.value = true
  progress.value = 0

  // Simulate progress
  progressTimer = setInterval(() => {
    if (progress.value < 90) {
      progress.value += Math.random() * 10
    }
  }, 100)
}

const finishProgress = () => {
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }

  progress.value = 100

  setTimeout(() => {
    isLoading.value = false
    progress.value = 0
  }, 200)
}

onMounted(() => {
  router.beforeEach((to, from, next) => {
    if (to.path !== from.path) {
      startProgress()
    }
    next()
  })

  router.afterEach(() => {
    finishProgress()
  })
})
</script>

<template>
  <Transition
    enter-active-class="transition-opacity duration-200"
    leave-active-class="transition-opacity duration-200"
    enter-from-class="opacity-0"
    leave-to-class="opacity-0"
  >
    <div
      v-if="isLoading"
      class="fixed top-0 left-0 right-0 z-50 h-1 bg-gray-200"
    >
      <div
        class="h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-300 ease-out"
        :style="{ width: `${progress}%` }"
      />
    </div>
  </Transition>
</template>
