<script setup lang="ts">
import { computed } from 'vue'
import '../assets/fonts/duotone/style.css'

interface Props {
  name: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'muted' | string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  color: '',
  class: '',
})

// Size map for consistent sizing
const sizeMap = {
  sm: 'text-sm',
  md: 'text-base',
  lg: 'text-xl',
  xl: 'text-3xl',
}

// Color map for theme colors
const colorMap = {
  primary: 'text-primary',
  secondary: 'text-secondary',
  muted: 'text-muted',
}

// Generate classes based on props
const iconClasses = computed(() => {
  const classes = [`ki-${props.name}`, 'ki-duotone']

  if (props.size) {
    classes.push(sizeMap[props.size])
  }

  if (props.color) {
    if (props.color in colorMap) {
      classes.push(colorMap[props.color as keyof typeof colorMap])
    } else {
      classes.push(`text-${props.color}`)
    }
  }

  if (props.class) {
    classes.push(props.class)
  }

  return classes.join(' ')
})
</script>

<template>
  <i :class="iconClasses" aria-hidden="true"></i>
</template>

<script lang="ts">
export default {
  name: 'DuotoneIcon',
}
</script>

<style scoped>
/* Add any additional icon styling here */
</style>
