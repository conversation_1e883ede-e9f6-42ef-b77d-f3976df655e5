<script setup lang="ts">
import { useLanguage } from '@/composables/useLanguage'

const { currentLanguage, switchLanguage, getLanguageFlag, getLanguageName } = useLanguage()
</script>

<template>
  <!-- Fixed Floating Language Toggle -->
  <div class="fixed bottom-6 right-6 z-50">
    <button
      @click="switchLanguage"
      class="group flex items-center gap-3 px-4 py-3 bg-white/95 backdrop-blur-md border border-gray-200/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:bg-white"
      :title="`Switch Language`"
    >
      <!-- Current Language Flag -->
      <span class="text-2xl">{{ getLanguageFlag(currentLanguage) }}</span>

      <!-- Language Name -->
      <span class="text-sm font-semibold text-gray-800 min-w-[60px]">
        {{ getLanguageName(currentLanguage) }}
      </span>

      <!-- Switch Icon -->
      <div
        class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 group-hover:bg-blue-100 transition-colors"
      >
        <svg
          class="w-4 h-4 text-gray-600 group-hover:text-blue-600 transition-colors transform group-hover:rotate-180 duration-300"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M8 9l4-4 4 4m0 6l-4 4-4-4"
          />
        </svg>
      </div>

      <!-- Hover Tooltip -->
      <div
        class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap"
      >
        Switch Language
        <div
          class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-800"
        ></div>
      </div>
    </button>
  </div>
</template>
