<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCompanyStore } from '@/stores/company'
import { categoryService } from '@/services/category.service'
import { productService } from '@/services/product.service'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert'
import { Checkbox } from '@/components/ui/checkbox'
import { Progress } from '@/components/ui/progress'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { toast } from 'vue-sonner'
import type { Product, Category, RestaurantProductFeatures } from '../../../shared/types/pos'
import type { CreateProductRequest, UpdateProductRequest } from '@/services/product.service'
import firebaseStorageService, { type UploadProgress, type UploadResult } from '@/services/firebase-storage.service'
import {
  Package,
  DollarSign,
  Boxes,
  Utensils,
  Calendar,
  ImageIcon,
  Upload,
  X,
  Star,
  Zap,
  AlertTriangle,
  CheckCircle,
  Info,
  Clock,
  Tag,
  Globe,
  Users,
  ChefHat,
  Flame,
  Leaf,
  Coffee,
  Save,
  Camera,
  Plus,
  Minus,
  BarChart3,
  Truck,
  ArrowLeft,
  ChevronDown,
  RefreshCw,
  FileText
} from 'lucide-vue-next'

interface Props {
  product?: Product
  isEditing?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  saved: [product: Product]
  cancel: []
}>()

const authStore = useAuthStore()
const companyStore = useCompanyStore()

// Form state
const isLoading = ref(false)
const isSaving = ref(false)
const error = ref('')

// Collapsible sections state
const isRestaurantFeaturesExpanded = ref(false)
const isAvailabilityExpanded = ref(false)
const isAdditionalInfoExpanded = ref(false)

// Category creation state
const showCreateCategoryDialog = ref(false)
const isCreatingCategory = ref(false)
const newCategoryForm = ref({
  name: '',
  description: '',
  color: '#3B82F6'
})

// Category dropdown state
const showCategoryDropdown = ref(false)
const categorySearchQuery = ref('')

// Barcode scanner state
const showBarcodeScanner = ref(false)
const isScannerLoading = ref(false)
const scannerError = ref('')
const manualBarcodeInput = ref('')
const videoElement = ref<HTMLVideoElement | null>(null)
const categories = ref<Category[]>([])
const recentlyUsedCategories = ref<string[]>([])
const uploadedImages = ref<string[]>([])
const imageStoragePaths = ref<string[]>([]) // Store Firebase storage paths for cleanup
const isUploading = ref(false)
const uploadProgress = ref(0)
const uploadingFiles = ref<string[]>([]) // Track which files are currently uploading
const isDragOver = ref(false) // Track drag and drop state

// Step-based wizard state
const currentStep = ref(1)
const totalSteps = ref(5)

// Step definitions
const stepDefinitions = [
  {
    id: 1,
    title: 'Basic Information',
    description: 'Add product name, category, and images',
    icon: Package,
    fields: ['name', 'categoryId', 'images']
  },
  {
    id: 2,
    title: 'Product Details',
    description: 'Set description, SKU, and specifications',
    icon: FileText,
    fields: ['description', 'sku', 'unit']
  },
  {
    id: 3,
    title: 'Pricing & Stock',
    description: 'Configure pricing and inventory settings',
    icon: DollarSign,
    fields: ['price', 'cost', 'stock']
  },
  {
    id: 4,
    title: 'Restaurant Features',
    description: 'Set cuisine options and service settings',
    icon: ChefHat,
    fields: ['spiceLevel', 'mealTimes'],
    condition: () => isRestaurantSubscription.value
  },
  {
    id: 5,
    title: 'Availability',
    description: 'Set availability and schedule',
    icon: Calendar,
    fields: ['isAvailable', 'availableFrom']
  }
]

// Current step data
const currentStepData = computed(() => {
  return stepDefinitions.find(step => step.id === currentStep.value) || stepDefinitions[0]
})

// Form completion tracking
const completionSteps = computed(() => {
  let completed = 0
  let total = 4

  // Basic info completion
  if (formData.value.name && formData.value.categoryId && formData.value.price > 0) completed++
  
  // Pricing completion  
  if (formData.value.price > 0 && formData.value.cost > 0) completed++
  
  // Inventory completion (different for restaurant vs retail)
  if (isRestaurantSubscription.value) {
    // For restaurants: just need availability set
    if (formData.value.isAvailable !== undefined) completed++
  } else {
    // For retail: need stock levels
    if (formData.value.stock >= 0 && formData.value.minStock >= 0) completed++
  }
  
  // Availability completion
  if (formData.value.isAvailable !== undefined) completed++

  if (isRestaurantSubscription.value) {
    total = 5
    // Restaurant features completion
    if ((restaurantFeatures.value.mealTimes && restaurantFeatures.value.mealTimes.length > 0) || restaurantFeatures.value.spiceLevel) completed++
  }

  return { completed, total, percentage: Math.round((completed / total) * 100) }
})

// Basic product info
const formData = ref({
  name: '',
  nameKhmer: '',
  description: '',
  sku: '',
  barcode: '',
  price: 0,
  priceUSD: 0,
  cost: 0,
  costUSD: 0,
  stock: 0,
  minStock: 0,
  categoryId: '',
  unit: 'piece',
  supplier: '',
  expiryDate: '',
  batchNumber: '',
  isAvailable: true,
  availableFrom: '',
  availableUntil: '',
  daysAvailable: [] as string[],
})

// Restaurant-specific features
const restaurantFeatures = ref<RestaurantProductFeatures>({
  spiceLevel: undefined,
  isTraditionalKhmer: false,
  region: undefined,
  mealTimes: [],
  dietaryRestrictions: [],
  allergens: [],
  preparationTime: undefined,
  cookingMethod: undefined,
  kitchenNotes: '',
  availableForTakeout: true,
  availableForDelivery: true,
  availableForDineIn: true,
  isBBQItem: false,
  bbqCategory: undefined,
  servingSize: '',
  servingUnit: 'person',
  portionSizes: {}
})

// Form options
const units = [
  { value: 'piece', label: 'Piece', icon: Package },
  { value: 'plate', label: 'Plate', icon: Utensils },
  { value: 'bowl', label: 'Bowl', icon: Coffee },
  { value: 'cup', label: 'Cup', icon: Coffee },
  { value: 'serving', label: 'Serving', icon: Users },
  { value: 'kg', label: 'Kilogram', icon: Package },
  { value: 'gram', label: 'Gram', icon: Package },
  { value: 'liter', label: 'Liter', icon: Package },
  { value: 'bottle', label: 'Bottle', icon: Package },
  { value: 'can', label: 'Can', icon: Package }
]

const spiceLevels = [
  { value: 'mild', label: 'Mild (ស្រាល)', icon: '🌶️', color: 'bg-green-100 text-green-800' },
  { value: 'medium', label: 'Medium (មធ្យម)', icon: '🌶️🌶️', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'hot', label: 'Hot (ហឹរ)', icon: '🌶️🌶️🌶️', color: 'bg-orange-100 text-orange-800' },
  { value: 'extra_hot', label: 'Extra Hot (ហឹរបំផុត)', icon: '🌶️🌶️🌶️🌶️', color: 'bg-red-100 text-red-800' }
]

const regions = [
  { value: 'phnom_penh', label: 'Phnom Penh (ភ្នំពេញ)' },
  { value: 'siem_reap', label: 'Siem Reap (សៀមរាប)' },
  { value: 'battambang', label: 'Battambang (បាត់ដំបង)' },
  { value: 'kampot', label: 'Kampot (កំពត)' },
  { value: 'other', label: 'Other (ផ្សេងទៀត)' }
]

const mealTimes = [
  { value: 'breakfast', label: 'Breakfast', khmer: 'អាហារពេលព្រឹក', icon: '🌅' },
  { value: 'lunch', label: 'Lunch', khmer: 'អាហារពេលថ្ងៃ', icon: '☀️' },
  { value: 'dinner', label: 'Dinner', khmer: 'អាហារពេលល្ងាច', icon: '🌙' },
  { value: 'snack', label: 'Snack', khmer: 'ស្ន៊ែក', icon: '🍿' },
  { value: 'dessert', label: 'Dessert', khmer: 'បង្អែម', icon: '🍰' }
]

const dietaryRestrictions = [
  { value: 'vegetarian', label: 'Vegetarian', khmer: 'បួស', icon: '🥬', color: 'bg-green-100 text-green-800' },
  { value: 'vegan', label: 'Vegan', khmer: 'កម្មវិធីបួស', icon: '🌱', color: 'bg-green-100 text-green-800' },
  { value: 'gluten_free', label: 'Gluten Free', khmer: '', icon: '🌾', color: 'bg-amber-100 text-amber-800' },
  { value: 'dairy_free', label: 'Dairy Free', khmer: '', icon: '🥛', color: 'bg-blue-100 text-blue-800' },
  { value: 'halal', label: 'Halal', khmer: 'ហាឡាល', icon: '☪️', color: 'bg-emerald-100 text-emerald-800' },
  { value: 'kosher', label: 'Kosher', khmer: '', icon: '✡️', color: 'bg-indigo-100 text-indigo-800' }
]

const allergens = [
  { value: 'nuts', label: 'Nuts', khmer: 'គ្រាប់', icon: '🥜' },
  { value: 'seafood', label: 'Seafood', khmer: 'អាហារសមុទ្រ', icon: '🦐' },
  { value: 'dairy', label: 'Dairy', khmer: 'ទឹកដោះគោ', icon: '🥛' },
  { value: 'eggs', label: 'Eggs', khmer: 'ពង', icon: '🥚' },
  { value: 'soy', label: 'Soy', khmer: 'សៀង', icon: '🌱' },
  { value: 'wheat', label: 'Wheat', khmer: 'ស្រូវសាលី', icon: '🌾' },
  { value: 'sesame', label: 'Sesame', khmer: 'ខ្ញី', icon: '🌰' }
]

const cookingMethods = [
  { value: 'grilled', label: 'Grilled', khmer: 'អាំង', icon: '🔥' },
  { value: 'fried', label: 'Fried', khmer: 'ចៀន', icon: '🍳' },
  { value: 'steamed', label: 'Steamed', khmer: 'ចំហុយ', icon: '💨' },
  { value: 'boiled', label: 'Boiled', khmer: 'ស្ងោរ', icon: '💧' },
  { value: 'raw', label: 'Raw', khmer: 'ឆៅ', icon: '🥗' },
  { value: 'baked', label: 'Baked', khmer: 'ដុត', icon: '🔥' },
  { value: 'stir_fried', label: 'Stir Fried', khmer: 'ឆា', icon: '🥢' }
]

const bbqCategories = [
  { value: 'meat', label: 'Meat', khmer: 'សាច់', icon: '🥩' },
  { value: 'seafood', label: 'Seafood', khmer: 'អាហារសមុទ្រ', icon: '🦐' },
  { value: 'vegetable', label: 'Vegetable', khmer: 'បន្លែ', icon: '🥬' },
  { value: 'side', label: 'Side Dish', khmer: 'ម្ហូបនំ', icon: '🍚' },
  { value: 'sauce', label: 'Sauce', khmer: 'ទឹកជ្រលក់', icon: '🥄' }
]

const servingUnits = [
  { value: 'person', label: 'Person', khmer: 'នាក់', icon: Users },
  { value: 'plate', label: 'Plate', khmer: 'ចាន', icon: Utensils },
  { value: 'bowl', label: 'Bowl', khmer: 'ចំណាំង', icon: Coffee },
  { value: 'cup', label: 'Cup', khmer: 'ពែង', icon: Coffee },
  { value: 'piece', label: 'Piece', khmer: 'ដុំ', icon: Package }
]

const daysOfWeek = [
  { value: 'monday', label: 'Monday', khmer: 'ច័ន្ទ', short: 'M' },
  { value: 'tuesday', label: 'Tuesday', khmer: 'អង្គារ', short: 'T' },
  { value: 'wednesday', label: 'Wednesday', khmer: 'ពុធ', short: 'W' },
  { value: 'thursday', label: 'Thursday', khmer: 'ព្រហស្បតិ៍', short: 'T' },
  { value: 'friday', label: 'Friday', khmer: 'សុក្រ', short: 'F' },
  { value: 'saturday', label: 'Saturday', khmer: 'សៅរ៍', short: 'S' },
  { value: 'sunday', label: 'Sunday', khmer: 'អាទិត្យ', short: 'S' }
]

// Check if company has restaurant subscription
const isRestaurantSubscription = computed(() => {
  // TODO: Fix type issue - return companyStore.currentCompany?.posType === 'RESTAURANT'
  return true
})

// Form validation
const formErrors = computed(() => {
  const errors: Record<string, string> = {}
  
  if (!formData.value.name) errors.name = 'Product name is required'
  if (!formData.value.categoryId) errors.categoryId = 'Category is required'
  if (formData.value.price <= 0) errors.price = 'Price must be greater than 0'
  if (formData.value.cost < 0) errors.cost = 'Cost cannot be negative'
  
  // Only validate stock for non-restaurant businesses
  if (!isRestaurantSubscription.value) {
    if (formData.value.stock < 0) errors.stock = 'Stock cannot be negative'
    if (formData.value.minStock < 0) errors.minStock = 'Minimum stock cannot be negative'
  }
  
  return errors
})

const isFormValid = computed(() => {
  return Object.keys(formErrors.value).length === 0
})

// Recently used categories management
const RECENT_CATEGORIES_KEY = 'recentlyUsedCategories'
const MAX_RECENT_CATEGORIES = 5

const loadRecentlyUsedCategories = () => {
  try {
    const stored = localStorage.getItem(RECENT_CATEGORIES_KEY)
    if (stored) {
      recentlyUsedCategories.value = JSON.parse(stored)
    }
  } catch (err) {
    console.error('Failed to load recently used categories:', err)
    recentlyUsedCategories.value = []
  }
}

const saveRecentlyUsedCategories = () => {
  try {
    localStorage.setItem(RECENT_CATEGORIES_KEY, JSON.stringify(recentlyUsedCategories.value))
  } catch (err) {
    console.error('Failed to save recently used categories:', err)
  }
}

const addToRecentlyUsed = (categoryId: string) => {
  if (!categoryId) return
  
  // Remove if already exists
  const index = recentlyUsedCategories.value.indexOf(categoryId)
  if (index > -1) {
    recentlyUsedCategories.value.splice(index, 1)
  }
  
  // Add to front
  recentlyUsedCategories.value.unshift(categoryId)
  
  // Keep only last MAX_RECENT_CATEGORIES
  if (recentlyUsedCategories.value.length > MAX_RECENT_CATEGORIES) {
    recentlyUsedCategories.value = recentlyUsedCategories.value.slice(0, MAX_RECENT_CATEGORIES)
  }
  
  saveRecentlyUsedCategories()
}

// Organized categories with recently used at top
const organizedCategories = computed(() => {
  const recent = recentlyUsedCategories.value
    .map(id => categories.value.find(cat => cat._id === id))
    .filter(Boolean) as Category[]
  
  const remaining = categories.value.filter(cat => !recentlyUsedCategories.value.includes(cat._id))
  
  return {
    recent,
    remaining: remaining.sort((a, b) => a.name.localeCompare(b.name))
  }
})

// Category dropdown computed properties
const selectedCategoryPath = computed(() => {
  if (!formData.value.categoryId) return ''
  const category = categories.value.find(cat => cat._id === formData.value.categoryId)
  return category?.name || ''
})

const filteredCategories = computed(() => {
  if (!categorySearchQuery.value.trim()) {
    return categories.value.sort((a, b) => a.name.localeCompare(b.name))
  }
  
  const query = categorySearchQuery.value.toLowerCase()
  return categories.value
    .filter(cat => cat.name.toLowerCase().includes(query))
    .sort((a, b) => a.name.localeCompare(b.name))
})

const hasExactCategoryMatch = computed(() => {
  if (!categorySearchQuery.value.trim()) return false
  return categories.value.some(cat => 
    cat.name.toLowerCase() === categorySearchQuery.value.trim().toLowerCase()
  )
})

// Load categories
const loadCategories = async () => {
  try {
    if (!authStore.currentUser?.currentCompanyId) return
    
    const data = await categoryService.getCategories(authStore.currentUser.currentCompanyId)
    categories.value = data
  } catch (err) {
    console.error('Failed to load categories:', err)
    error.value = 'Failed to load categories'
  }
}

// Create new category
const createCategory = async () => {
  if (!newCategoryForm.value.name.trim() || !authStore.currentUser?.currentCompanyId) return
  
  try {
    isCreatingCategory.value = true
    
    const categoryData = {
      name: newCategoryForm.value.name.trim(),
      description: newCategoryForm.value.description?.trim() || undefined,
      color: newCategoryForm.value.color
    }
    
    const newCategory = await categoryService.createCategory(authStore.currentUser.currentCompanyId, categoryData)
    
    // Refresh categories list
    await loadCategories()
    
    // Auto-select the newly created category
    formData.value.categoryId = newCategory._id
    
    // Add to recently used
    addToRecentlyUsed(newCategory._id)
    
    // Reset form and close dialog
    resetCategoryForm()
    showCreateCategoryDialog.value = false
    
    toast.success('Category created successfully')
  } catch (err) {
    console.error('Failed to create category:', err)
    toast.error('Failed to create category')
  } finally {
    isCreatingCategory.value = false
  }
}

// Reset category form
const resetCategoryForm = () => {
  newCategoryForm.value = {
    name: '',
    description: '',
    color: '#3B82F6'
  }
}

// Category dropdown functions
const selectCategory = (category: Category) => {
  formData.value.categoryId = category._id
  addToRecentlyUsed(category._id)
  showCategoryDropdown.value = false
  categorySearchQuery.value = ''
}

const createCategoryFromSearch = async (categoryName: string) => {
  try {
    isCreatingCategory.value = true
    
    if (!authStore.currentUser?.currentCompanyId) return
    
    const categoryData = {
      name: categoryName,
      color: '#3B82F6'
    }
    
    const newCategory = await categoryService.createCategory(authStore.currentUser.currentCompanyId, categoryData)
    
    // Refresh categories list
    await loadCategories()
    
    // Auto-select the newly created category
    formData.value.categoryId = newCategory._id
    addToRecentlyUsed(newCategory._id)
    
    // Close dropdown and clear search
    showCategoryDropdown.value = false
    categorySearchQuery.value = ''
    
    toast.success('Category created successfully')
  } catch (err) {
    console.error('Failed to create category:', err)
    toast.error('Failed to create category')
  } finally {
    isCreatingCategory.value = false
  }
}

const handleCategorySearchEnter = () => {
  if (categorySearchQuery.value.trim() && !hasExactCategoryMatch.value) {
    createCategoryFromSearch(categorySearchQuery.value.trim())
  }
}

const openCreateCategoryDialog = (searchTerm: string = '') => {
  newCategoryForm.value.name = searchTerm
  showCreateCategoryDialog.value = true
  showCategoryDropdown.value = false
}

// Barcode scanner functions
const openBarcodeScanner = () => {
  showBarcodeScanner.value = true
  scannerError.value = ''
}

const closeBarcodeScanner = () => {
  showBarcodeScanner.value = false
  scannerError.value = ''
  stopScanner()
}

const stopScanner = () => {
  // Stop any active video streams
  if (typeof navigator !== 'undefined' && navigator.mediaDevices) {
    navigator.mediaDevices.getUserMedia({ video: true })
      .then(stream => {
        stream.getTracks().forEach(track => track.stop())
      })
      .catch(() => {
        // Handle error silently
      })
  }
}

const onBarcodeDetected = (barcode: string) => {
  formData.value.barcode = barcode
  closeBarcodeScanner()
  toast.success('Barcode scanned successfully')
}

const startCamera = async () => {
  try {
    isScannerLoading.value = true
    scannerError.value = ''
    
    if (!videoElement.value) {
      throw new Error('Video element not found')
    }
    
    const stream = await navigator.mediaDevices.getUserMedia({
      video: {
        facingMode: 'environment', // Use back camera if available
        width: { ideal: 1280 },
        height: { ideal: 720 }
      }
    })
    
    videoElement.value.srcObject = stream
    await videoElement.value.play()
    
    // Start barcode detection if BarcodeDetector is available
    if ('BarcodeDetector' in window) {
      const barcodeDetector = new (window as any).BarcodeDetector()
      const detectBarcodes = async () => {
        if (!videoElement.value || showBarcodeScanner.value === false) return
        
        try {
          const barcodes = await barcodeDetector.detect(videoElement.value)
          if (barcodes.length > 0) {
            onBarcodeDetected(barcodes[0].rawValue)
            return
          }
        } catch (err) {
          // Continue scanning
        }
        
        // Continue scanning
        if (showBarcodeScanner.value) {
          setTimeout(detectBarcodes, 100)
        }
      }
      
      detectBarcodes()
    }
    
  } catch (err) {
    console.error('Camera error:', err)
    scannerError.value = err instanceof Error ? err.message : 'Failed to access camera'
  } finally {
    isScannerLoading.value = false
  }
}

const confirmManualBarcode = () => {
  if (manualBarcodeInput.value.trim()) {
    onBarcodeDetected(manualBarcodeInput.value.trim())
    manualBarcodeInput.value = ''
  }
}

// Initialize form
const initializeForm = () => {
  if (props.product) {
    // Edit mode - populate form with existing data
    formData.value = {
      name: props.product.name,
      nameKhmer: props.product.nameKhmer || '',
      description: props.product.description || '',
      sku: props.product.sku,
      barcode: props.product.barcode || '',
      price: props.product.price,
      priceUSD: props.product.priceUSD || 0,
      cost: props.product.cost,
      costUSD: props.product.costUSD || 0,
      stock: props.product.stock,
      minStock: props.product.minStock,
      categoryId: props.product.categoryId,
      unit: props.product.unit,
      supplier: props.product.supplier || '',
      expiryDate: props.product.expiryDate ? new Date(props.product.expiryDate).toISOString().split('T')[0] : '',
      batchNumber: props.product.batchNumber || '',
      isAvailable: props.product.isAvailable ?? true,
      availableFrom: props.product.availableFrom || '',
      availableUntil: props.product.availableUntil || '',
      daysAvailable: props.product.daysAvailable || [],
    }
    
    if (props.product.restaurantFeatures) {
      restaurantFeatures.value = { ...props.product.restaurantFeatures }
    }

    if (props.product.images) {
      uploadedImages.value = [...props.product.images]
      // For existing images, we don't have storage paths, so initialize empty array
      imageStoragePaths.value = new Array(props.product.images.length).fill('')
    }
  } else {
    // Create mode - generate SKU and set defaults
    formData.value.sku = `SKU-${Date.now()}`
    
    // Set appropriate defaults for restaurant mode
    if (isRestaurantSubscription.value) {
      formData.value.stock = 0 // Not used for restaurants
      formData.value.minStock = 0 // Not used for restaurants
      formData.value.isAvailable = true // Default to available
    }
  }
}

// Image handling
const handleImageUpload = async (event: Event) => {
  const files = (event.target as HTMLInputElement).files
  if (!files || files.length === 0) return

  const fileArray = Array.from(files)
  
  // Validate files before upload
  const validation = firebaseStorageService.validateFiles(fileArray)
  if (!validation.valid) {
    toast.error(`Upload failed: ${validation.errors.join(', ')}`)
    return
  }

  isUploading.value = true
  uploadProgress.value = 0
  uploadingFiles.value = fileArray.map(f => f.name)
  
  try {
    // Upload files to Firebase Storage
    const uploadResults = await firebaseStorageService.uploadFiles(
      fileArray,
      'products', // folder name
      (progress: UploadProgress) => {
        uploadProgress.value = progress.progress
        
        if (progress.error) {
          toast.error(`Upload error: ${progress.error}`)
        }
      }
    )

    // Add successful uploads to the arrays
    uploadResults.forEach(result => {
      uploadedImages.value.push(result.url)
      imageStoragePaths.value.push(result.path)
    })

    if (uploadResults.length > 0) {
      toast.success(`Successfully uploaded ${uploadResults.length} image(s)`)
    }
    
    if (uploadResults.length < fileArray.length) {
      toast.warning(`${fileArray.length - uploadResults.length} file(s) failed to upload`)
    }
  } catch (err) {
    console.error('Upload error:', err)
    toast.error('Failed to upload images')
  } finally {
    isUploading.value = false
    uploadProgress.value = 0
    uploadingFiles.value = []
    // Clear the file input
    const input = event.target as HTMLInputElement
    input.value = ''
  }
}

const removeImage = async (index: number) => {
  try {
    // Delete from Firebase Storage if it has a storage path
    const storagePath = imageStoragePaths.value[index]
    if (storagePath) {
      await firebaseStorageService.deleteFile(storagePath)
      imageStoragePaths.value.splice(index, 1)
    }
    
    // Remove from displayed images
    uploadedImages.value.splice(index, 1)
    
    toast.success('Image removed successfully')
  } catch (err) {
    console.error('Error removing image:', err)
    toast.error('Failed to remove image')
  }
}

// Save product
const saveProduct = async () => {
  try {
    isSaving.value = true
    error.value = ''
    
    if (!authStore.currentUser?.currentCompanyId) {
      throw new Error('Company ID is required')
    }

    if (!isFormValid.value) {
      throw new Error('Please fix the form errors before saving')
    }

    const productData: CreateProductRequest | UpdateProductRequest = {
      ...formData.value,
      expiryDate: formData.value.expiryDate ? new Date(formData.value.expiryDate) : undefined,
      restaurantFeatures: isRestaurantSubscription.value ? restaurantFeatures.value : undefined,
      images: uploadedImages.value,
      thumbnail: uploadedImages.value[0] || undefined,
    }

    let savedProduct: Product
    
    if (props.isEditing && props.product) {
      savedProduct = await productService.updateProduct(props.product._id, productData)
      toast.success('Product updated successfully')
    } else {
      savedProduct = await productService.createProduct(authStore.currentUser.currentCompanyId, productData as CreateProductRequest)
      toast.success('Product created successfully')
    }

    emit('saved', savedProduct)
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to save product'
    toast.error(error.value)
  } finally {
    isSaving.value = false
  }
}

// Drag and drop handlers
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = async (event: DragEvent) => {
  event.preventDefault()
  isDragOver.value = false
  
  const files = event.dataTransfer?.files
  if (!files || files.length === 0) return
  
  // Create a mock event to reuse the existing upload logic
  const mockEvent = {
    target: {
      files: files,
      value: ''
    }
  } as unknown as Event
  
  await handleImageUpload(mockEvent)
}

// Scroll to section
const scrollToSection = (sectionId: string) => {
  // Expand section if it's collapsed
  if (sectionId === 'restaurant-features' && !isRestaurantFeaturesExpanded.value) {
    isRestaurantFeaturesExpanded.value = true
  } else if (sectionId === 'availability' && !isAvailabilityExpanded.value) {
    isAvailabilityExpanded.value = true
  } else if (sectionId === 'additional-information' && !isAdditionalInfoExpanded.value) {
    isAdditionalInfoExpanded.value = true
  }
  
  // Wait for the section to expand before scrolling
  nextTick(() => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      })
    }
  })
}

// Step navigation
const nextStep = () => {
  if (currentStep.value < totalSteps.value) {
    currentStep.value++
  }
}

const canProceedToNextStep = computed(() => {
  switch (currentStep.value) {
    case 1: // Basic Information
      return formData.value.name && formData.value.categoryId
    case 2: // Product Details
      return formData.value.sku && formData.value.description
    case 3: // Pricing & Stock
      return formData.value.price > 0
    case 4: // Restaurant Features (optional for non-restaurant)
      return !isRestaurantSubscription.value || restaurantFeatures.value.mealTimes?.length! > 0
    case 5: // Availability
      return formData.value.isAvailable !== undefined
    default:
      return true
  }
})

// Cancel editing
const cancelEditing = () => {
  emit('cancel')
}

// Format price function
const formatPrice = (price: number, currency = 'KHR') => {
  if (currency === 'USD') {
    return `$${price.toFixed(2)}`
  } else {
    return `${price.toLocaleString()} ៛`
  }
}

// Toggle array values
const toggleArrayValue = (array: string[], value: string) => {
  const index = array.indexOf(value)
  if (index > -1) {
    array.splice(index, 1)
  } else {
    array.push(value)
  }
}

// Auto-generate SKU
const generateSKU = () => {
  const prefix = formData.value.name.substring(0, 3).toUpperCase()
  const timestamp = Date.now().toString().slice(-6)
  formData.value.sku = `${prefix}-${timestamp}`
}

// Watch for BBQ item changes
watch(() => restaurantFeatures.value.isBBQItem, (isBBQ) => {
  if (!isBBQ) {
    restaurantFeatures.value.bbqCategory = undefined
  }
})

// Watch for traditional Khmer changes
watch(() => restaurantFeatures.value.isTraditionalKhmer, (isTraditional) => {
  if (!isTraditional) {
    restaurantFeatures.value.region = undefined
  }
})

// Watch for category selection changes
watch(() => formData.value.categoryId, (newCategoryId) => {
  if (newCategoryId) {
    addToRecentlyUsed(newCategoryId)
  }
})

// Reset category form when dialog opens
watch(showCreateCategoryDialog, (isOpen) => {
  if (isOpen) {
    resetCategoryForm()
  }
})

// Close category dropdown when clicking outside
const handleClickOutside = (event: Event) => {
  const dropdownElement = document.querySelector('[data-category-dropdown]')
  if (dropdownElement && !dropdownElement.contains(event.target as Node)) {
    showCategoryDropdown.value = false
    categorySearchQuery.value = ''
  }
}

watch(showCategoryDropdown, (isOpen) => {
  if (isOpen) {
    document.addEventListener('click', handleClickOutside)
  } else {
    document.removeEventListener('click', handleClickOutside)
  }
})

// Start camera when barcode scanner opens
watch(showBarcodeScanner, (isOpen) => {
  if (isOpen) {
    nextTick(() => {
      startCamera()
    })
  } else {
    stopScanner()
    manualBarcodeInput.value = ''
  }
})

onMounted(async () => {
  isLoading.value = true
  try {
    loadRecentlyUsedCategories()
    await loadCategories()
    initializeForm()
  } catch (err) {
    console.error('Failed to initialize form:', err)
    error.value = 'Failed to initialize form'
  } finally {
    isLoading.value = false
  }
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-blue-950 dark:to-indigo-950">
    <!-- Modern Header -->
    <div class="sticky top-0 z-50 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-b border-white/20 dark:border-gray-800/50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="sm" 
              @click="cancelEditing" 
              :disabled="isSaving"
              class="hover:bg-white/80 dark:hover:bg-gray-800/80"
            >
              <ArrowLeft class="w-4 h-4 mr-2" />
              Back
            </Button>
            <div class="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
            <div>
              <h1 class="text-lg font-bold text-gray-900 dark:text-white">
                {{ props.isEditing ? 'Edit Product' : 'Create New Product' }}
              </h1>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                Configure your product details and settings
              </p>
            </div>
          </div>
          
          <!-- Progress Indicator -->
          <div class="hidden md:flex items-center gap-6">
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                Form Completion
              </span>
              <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                <div 
                  class="bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 rounded-full transition-all duration-500"
                  :style="{ width: `${completionSteps.percentage}%` }"
                ></div>
              </div>
              <span class="text-xs text-gray-500 dark:text-gray-400">{{ completionSteps.percentage }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="flex gap-8 max-w-9xl mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-8">
      <!-- Main Form Content -->
      <div class="flex-1 max-w-6xl">
        <!-- Loading State -->
        <div v-if="isLoading" class="flex items-center justify-center py-20">
          <div class="flex flex-col items-center gap-4">
            <div class="animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600"></div>
            <span class="text-gray-600 dark:text-gray-400 font-medium">Loading form...</span>
          </div>
        </div>

        <!-- Error State -->
        <Alert v-if="error" variant="destructive" class="mb-8">
          <AlertTriangle class="h-4 w-4" />
          <AlertDescription>{{ error }}</AlertDescription>
        </Alert>

        <!-- Form Content -->
        <div v-if="!isLoading" class="bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-800/50">
          <div class="p-8 space-y-8">
            
            <!-- Basic Information Section -->
            <div id="basic-information" class="space-y-6 scroll-mt-24">
              <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <Package class="w-5 h-5 text-blue-600" />
                  Basic Information
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Essential product details and identification</p>
              </div>

              <!-- Product Name -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                <div class="md:pt-2">
                  <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Product Name
                    <Badge variant="outline" class="ml-2 text-xs">Required</Badge>
                  </Label>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    The main name customers will see
                  </p>
                </div>
                <div class="md:col-span-2 space-y-3">
                  <Input 
                    v-model="formData.name"
                    placeholder="Enter product name"
                    :class="formErrors.name ? 'border-red-500' : ''"
                    @blur="generateSKU"
                  />
                  <Input 
                    v-model="formData.nameKhmer"
                    placeholder="បញ្ចូលឈ្មោះជាភាសាខ្មែរ"
                    class="khmer-font"
                  />
                  <p v-if="formErrors.name" class="text-sm text-red-600">{{ formErrors.name }}</p>
                </div>
              </div>

              <!-- Description -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                <div class="md:pt-2">
                  <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Description</Label>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Detailed product description
                  </p>
                </div>
                <div class="md:col-span-2 space-y-3">
                  <Textarea 
                    v-model="formData.description"
                    placeholder="Describe your product..."
                    rows="3"
                  />
                </div>
              </div>

              <!-- SKU and Category -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                <div class="md:pt-2">
                  <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Product Details
                    <Badge variant="outline" class="ml-2 text-xs">Required</Badge>
                  </Label>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    SKU, category, and unit information
                  </p>
                </div>
                <div class="md:col-span-2 space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">SKU</Label>
                      <div class="flex gap-2">
                        <Input 
                          v-model="formData.sku"
                          placeholder="Product SKU"
                          :class="formErrors.sku ? 'border-red-500' : ''"
                        />
                        <Button variant="outline" size="icon" @click="generateSKU" type="button">
                          <RefreshCw class="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">Barcode</Label>
                      <div class="flex gap-2">
                        <Input 
                          v-model="formData.barcode"
                          placeholder="Product barcode"
                          class="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          type="button"
                          @click="openBarcodeScanner"
                          title="Scan barcode"
                        >
                          <Camera class="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">Category</Label>
                      
                      <!-- Custom Category Dropdown with Search -->
                      <div class="relative" data-category-dropdown>
                        <button
                          type="button"
                          @click="showCategoryDropdown = !showCategoryDropdown"
                          :class="[
                            'w-full flex items-center justify-between px-3 py-2 text-left bg-background border rounded-md text-sm',
                            formErrors.categoryId ? 'border-red-500' : 'border-input',
                            'hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500'
                          ]"
                        >
                          <span :class="selectedCategoryPath ? 'text-foreground' : 'text-muted-foreground'">
                            {{ selectedCategoryPath || 'Select category' }}
                          </span>
                          <ChevronDown :class="['w-4 h-4 transition-transform', showCategoryDropdown && 'rotate-180']" />
                        </button>

                        <!-- Dropdown Content -->
                        <div 
                          v-if="showCategoryDropdown" 
                          class="absolute z-50 w-full mt-1 bg-background border border-input rounded-md shadow-lg max-h-80 overflow-hidden"
                        >
                          <!-- Search Input -->
                          <div class="p-3 border-b border-border">
                            <div class="relative">
                              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                              <Input
                                v-model="categorySearchQuery"
                                placeholder="Search category"
                                class="pl-10 h-8"
                                @keydown.enter.prevent="handleCategorySearchEnter"
                              />
                            </div>
                          </div>

                          <!-- Categories List -->
                          <div class="max-h-64 overflow-y-auto">
                            <!-- Create New Category Options (when searching) -->
                            <div v-if="categorySearchQuery.trim() && !hasExactCategoryMatch" class="border-b border-border">
                              <button
                                type="button"
                                @click="createCategoryFromSearch(categorySearchQuery.trim())"
                                class="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2 text-blue-600 dark:text-blue-400"
                              >
                                <Plus class="w-4 h-4" />
                                Create "{{ categorySearchQuery.trim() }}"
                              </button>
                                                             <button
                                 type="button"
                                 @click="openCreateCategoryDialog(categorySearchQuery.trim())"
                                 class="w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2 text-blue-600 dark:text-blue-400"
                               >
                                 <Plus class="w-4 h-4" />
                                 Create and edit...
                               </button>
                            </div>

                            <!-- Filtered Categories -->
                            <div v-for="category in filteredCategories" :key="category._id">
                              <button
                                type="button"
                                @click="selectCategory(category)"
                                :class="[
                                  'w-full px-3 py-2 text-left text-sm hover:bg-muted flex items-center gap-2',
                                  formData.categoryId === category._id ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' : ''
                                ]"
                              >
                                <div :class="[
                                  'w-3 h-3 rounded-full flex-shrink-0',
                                  formData.categoryId === category._id ? 'bg-blue-500' : 'bg-gray-300 dark:bg-gray-600'
                                ]"></div>
                                <span class="flex-1">{{ category.name }}</span>
                                <CheckCircle v-if="formData.categoryId === category._id" class="w-4 h-4 text-blue-500" />
                              </button>
                            </div>

                            <!-- No Categories Found -->
                            <div v-if="filteredCategories.length === 0 && !categorySearchQuery.trim()" class="px-3 py-4 text-center text-sm text-muted-foreground">
                              No categories available
                            </div>
                            <div v-else-if="filteredCategories.length === 0 && categorySearchQuery.trim()" class="px-3 py-4 text-center text-sm text-muted-foreground">
                              No categories found matching "{{ categorySearchQuery }}"
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Recently Used Categories as Chips -->
                      <div v-if="organizedCategories.recent.length > 0" class="space-y-2">
                        <div class="flex items-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                          <Clock class="w-3 h-3" />
                          <span>Recently used:</span>
                        </div>
                        <div class="flex flex-wrap gap-2">
                          <button
                            v-for="category in organizedCategories.recent"
                            :key="`recent-chip-${category._id}`"
                            type="button"
                            @click="selectCategory(category)"
                            :class="[
                              'inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 border',
                              formData.categoryId === category._id
                                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700'
                                : 'bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                            ]"
                          >
                            <Clock class="w-3 h-3" />
                            {{ category.name }}
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">Unit</Label>
                      <Select v-model="formData.unit">
                        <SelectTrigger class="w-full">
                          <SelectValue placeholder="Select unit" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem 
                            v-for="unit in units" 
                            :key="unit.value"
                            :value="unit.value"
                          >
                            <div class="flex items-center gap-2">
                              <component :is="unit.icon" class="w-4 h-4" />
                              {{ unit.label }}
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Product Images Section -->
            <div id="product-images" class="space-y-6 scroll-mt-24">
              <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <ImageIcon class="w-5 h-5 text-green-600" />
                  Product Images
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Upload photos to showcase your product</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                <div class="md:pt-2">
                  <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Photo Upload</Label>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Add multiple images (PNG, JPG, WebP up to 10MB each)
                  </p>
                </div>
                <div class="md:col-span-2 space-y-4">
                  <!-- Upload Area -->
                  <div 
                    :class="[
                      'border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200',
                      isDragOver 
                        ? 'border-primary bg-primary/10 scale-105' 
                        : 'border-muted-foreground/25 hover:border-muted-foreground/50',
                      isUploading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'
                    ]"
                    @dragover="handleDragOver"
                    @dragleave="handleDragLeave"
                    @drop="handleDrop"
                  >
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      class="hidden"
                      id="image-upload"
                      @change="handleImageUpload"
                      :disabled="isUploading"
                    />
                    <label
                      for="image-upload"
                      class="cursor-pointer space-y-3 block"
                      :class="{ 'pointer-events-none': isUploading }"
                    >
                      <div class="flex justify-center">
                        <div :class="[
                          'p-3 rounded-full transition-all duration-200',
                          isDragOver 
                            ? 'bg-primary/20 text-primary' 
                            : 'bg-muted text-muted-foreground'
                        ]">
                          <Camera class="w-6 h-6" />
                        </div>
                      </div>
                      <div>
                        <p class="font-medium" :class="isDragOver ? 'text-primary' : ''">
                          {{ isDragOver ? 'Drop images here' : 'Click to upload or drag and drop' }}
                        </p>
                        <p class="text-sm text-muted-foreground mt-1">
                          PNG, JPG, GIF, WebP up to 10MB each
                        </p>
                      </div>
                    </label>
                  </div>

                  <!-- Upload Progress -->
                  <div v-if="isUploading" class="space-y-3">
                    <div class="flex items-center gap-2 text-sm text-muted-foreground">
                      <div class="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"></div>
                      Uploading images...
                    </div>
                    <Progress :value="uploadProgress" class="h-2" />
                  </div>

                  <!-- Uploaded Images -->
                  <div v-if="uploadedImages.length > 0" class="grid grid-cols-3 md:grid-cols-4 gap-3">
                    <div
                      v-for="(image, index) in uploadedImages"
                      :key="index"
                      class="relative group aspect-square rounded-lg overflow-hidden border"
                    >
                      <img
                        :src="image"
                        :alt="`Product image ${index + 1}`"
                        class="w-full h-full object-cover"
                      />
                      <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                        <Button
                          variant="destructive"
                          size="icon"
                          class="h-8 w-8"
                          @click="removeImage(index)"
                          :disabled="isUploading"
                        >
                          <X class="w-4 h-4" />
                        </Button>
                      </div>
                      <Badge
                        v-if="index === 0"
                        class="absolute top-2 left-2 text-xs"
                        variant="default"
                      >
                        Primary
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Pricing Section -->
            <div id="pricing-stock" class="space-y-6 scroll-mt-24">
              <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                  <DollarSign class="w-5 h-5 text-purple-600" />
                  Pricing & Stock
                </h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Set pricing strategy and inventory levels</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                <div class="md:pt-2">
                  <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Price Information
                    <Badge variant="outline" class="ml-2 text-xs">Required</Badge>
                  </Label>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Set selling price and cost for profit calculation
                  </p>
                </div>
                <div class="md:col-span-2 space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">Selling Price (KHR)</Label>
                      <Input 
                        type="number"
                        v-model.number="formData.price"
                        placeholder="0"
                        min="0"
                        step="100"
                        :class="formErrors.price ? 'border-red-500' : ''"
                      />
                      <p v-if="formErrors.price" class="text-sm text-red-600">{{ formErrors.price }}</p>
                    </div>
                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">Cost Price (KHR)</Label>
                      <Input 
                        type="number"
                        v-model.number="formData.cost"
                        placeholder="0"
                        min="0"
                        step="100"
                      />
                    </div>
                  </div>

                  <!-- Stock Management (if not restaurant) -->
                  <div v-if="!isRestaurantSubscription" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">Current Stock</Label>
                      <Input 
                        type="number"
                        v-model.number="formData.stock"
                        placeholder="0"
                        min="0"
                      />
                    </div>
                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">Minimum Stock Alert</Label>
                      <Input 
                        type="number"
                        v-model.number="formData.minStock"
                        placeholder="0"
                        min="0"
                      />
                    </div>
                  </div>

                  <!-- Profit Analysis -->
                  <div class="p-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-3">Profit Analysis</h4>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                      <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Gross Profit:</span>
                        <span class="font-bold text-green-600 dark:text-green-400">
                          {{ (formData.price - formData.cost).toLocaleString() }} ៛
                        </span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">Profit Margin:</span>
                        <span class="font-bold">
                          {{ formData.price > 0 ? Math.round(((formData.price - formData.cost) / formData.price) * 100) : 0 }}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Restaurant Features Section (if applicable) -->
            <div v-if="isRestaurantSubscription" id="restaurant-features" class="space-y-6 scroll-mt-24">
              <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                <button
                  @click="isRestaurantFeaturesExpanded = !isRestaurantFeaturesExpanded"
                  class="w-full flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-2 -m-2 transition-colors"
                >
                  <div class="flex items-center gap-2">
                    <ChefHat class="w-5 h-5 text-amber-600" />
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Restaurant Features</h3>
                    <Badge variant="outline" class="text-xs">Optional</Badge>
                  </div>
                  <ChevronDown 
                    :class="[
                      'w-5 h-5 text-gray-400 transition-transform duration-200',
                      isRestaurantFeaturesExpanded ? 'rotate-180' : ''
                    ]" 
                  />
                </button>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 ml-7">Configure cuisine-specific settings</p>
              </div>

              <!-- Collapsible Content -->
              <div 
                v-if="isRestaurantFeaturesExpanded"
                class="space-y-6 animate-in slide-in-from-top-2 duration-200"
              >
                <!-- Spice Level -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                  <div class="md:pt-2">
                    <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Spice Level</Label>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Set the heat level for this dish
                    </p>
                  </div>
                  <div class="md:col-span-2">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                      <div
                        v-for="level in spiceLevels"
                        :key="level.value"
                        class="cursor-pointer"
                        @click="restaurantFeatures.spiceLevel = restaurantFeatures.spiceLevel === level.value ? undefined : (level.value as any)"
                      >
                        <div 
                          :class="[
                            'p-3 rounded-lg border-2 transition-all duration-200 text-center',
                            restaurantFeatures.spiceLevel === level.value 
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          ]"
                        >
                          <div class="text-lg mb-1">{{ level.icon }}</div>
                          <div class="text-xs font-medium">{{ level.label.split(' (')[0] }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Meal Times -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                  <div class="md:pt-2">
                    <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Meal Times</Label>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      When is this dish typically served
                    </p>
                  </div>
                  <div class="md:col-span-2">
                    <div class="grid grid-cols-2 md:grid-cols-5 gap-3">
                      <div
                        v-for="meal in mealTimes"
                        :key="meal.value"
                        class="cursor-pointer"
                        @click="toggleArrayValue(restaurantFeatures.mealTimes!, meal.value as any)"
                      >
                        <div 
                          :class="[
                            'p-3 rounded-lg border-2 transition-all duration-200 text-center',
                            restaurantFeatures.mealTimes!.includes(meal.value as any)
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          ]"
                        >
                          <div class="text-lg mb-1">{{ meal.icon }}</div>
                          <div class="text-xs font-medium">{{ meal.label }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Special Features -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                  <div class="md:pt-2">
                    <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Special Features</Label>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Mark special characteristics
                    </p>
                  </div>
                  <div class="md:col-span-2 space-y-4">
                    <div class="flex items-center justify-between p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                      <div class="flex items-center gap-3">
                        <Star class="w-5 h-5 text-amber-500" />
                        <div>
                          <p class="font-medium text-gray-900 dark:text-white">Traditional Khmer</p>
                          <p class="text-sm text-gray-500 dark:text-gray-400">Mark as traditional Cambodian dish</p>
                        </div>
                      </div>
                      <Switch v-model:checked="restaurantFeatures.isTraditionalKhmer" />
                    </div>

                    <div class="flex items-center justify-between p-4 rounded-lg border border-gray-200 dark:border-gray-700">
                      <div class="flex items-center gap-3">
                        <Flame class="w-5 h-5 text-red-500" />
                        <div>
                          <p class="font-medium text-gray-900 dark:text-white">BBQ Item</p>
                          <p class="text-sm text-gray-500 dark:text-gray-400">Available for BBQ unlimited mode</p>
                        </div>
                      </div>
                      <Switch v-model:checked="restaurantFeatures.isBBQItem" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Availability Section -->
            <div id="availability" class="space-y-6 scroll-mt-24">
              <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                <button
                  @click="isAvailabilityExpanded = !isAvailabilityExpanded"
                  class="w-full flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-2 -m-2 transition-colors"
                >
                  <div class="flex items-center gap-2">
                    <Calendar class="w-5 h-5 text-teal-600" />
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Availability</h3>
                    <Badge variant="outline" class="text-xs">Optional</Badge>
                  </div>
                  <ChevronDown 
                    :class="[
                      'w-5 h-5 text-gray-400 transition-transform duration-200',
                      isAvailabilityExpanded ? 'rotate-180' : ''
                    ]" 
                  />
                </button>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 ml-7">Set when your product is available</p>
              </div>

              <!-- Collapsible Content -->
              <div 
                v-if="isAvailabilityExpanded"
                class="space-y-6 animate-in slide-in-from-top-2 duration-200"
              >
                <!-- Product Status -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-center">
                  <div>
                    <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Product Status</Label>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Enable or disable product availability
                    </p>
                  </div>
                  <div class="md:col-span-2">
                    <div class="flex items-center justify-between p-4 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                      <div class="space-y-1">
                        <p class="font-medium text-gray-900 dark:text-white">Available for Sale</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Customers can order this product</p>
                      </div>
                      <Switch v-model:checked="formData.isAvailable" />
                    </div>
                  </div>
                </div>

                <!-- Time Range -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                  <div class="md:pt-2">
                    <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Operating Hours</Label>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Set specific time availability
                    </p>
                  </div>
                  <div class="md:col-span-2">
                    <div class="grid grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <Label class="text-xs text-gray-500 dark:text-gray-400">Available From</Label>
                        <Input 
                          type="time"
                          v-model="formData.availableFrom"
                        />
                      </div>
                      <div class="space-y-2">
                        <Label class="text-xs text-gray-500 dark:text-gray-400">Available Until</Label>
                        <Input 
                          type="time"
                          v-model="formData.availableUntil"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Days of Week -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                  <div class="md:pt-2">
                    <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Available Days</Label>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Select which days this product is available
                    </p>
                  </div>
                  <div class="md:col-span-2">
                    <div class="grid grid-cols-7 gap-2">
                      <div
                        v-for="day in daysOfWeek"
                        :key="day.value"
                        class="cursor-pointer"
                        @click="toggleArrayValue(formData.daysAvailable, day.value)"
                      >
                        <div 
                          :class="[
                            'p-3 rounded-lg border-2 transition-all text-center',
                            formData.daysAvailable.includes(day.value)
                              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300' 
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          ]"
                        >
                          <div class="text-xs font-bold">{{ day.short }}</div>
                          <div class="text-[10px] text-gray-500 mt-1">{{ day.label.slice(0, 3) }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Information -->
            <div id="additional-information" class="space-y-6 scroll-mt-24">
              <div class="border-b border-gray-200 dark:border-gray-700 pb-4">
                <button
                  @click="isAdditionalInfoExpanded = !isAdditionalInfoExpanded"
                  class="w-full flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-2 -m-2 transition-colors"
                >
                  <div class="flex items-center gap-2">
                    <Truck class="w-5 h-5 text-indigo-600" />
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Additional Information</h3>
                    <Badge variant="outline" class="text-xs">Optional</Badge>
                  </div>
                  <ChevronDown 
                    :class="[
                      'w-5 h-5 text-gray-400 transition-transform duration-200',
                      isAdditionalInfoExpanded ? 'rotate-180' : ''
                    ]" 
                  />
                </button>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1 ml-7">Supplier and inventory details</p>
              </div>

              <!-- Collapsible Content -->
              <div 
                v-if="isAdditionalInfoExpanded"
                class="space-y-6 animate-in slide-in-from-top-2 duration-200"
              >
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
                  <div class="md:pt-2">
                    <Label class="text-sm font-medium text-gray-700 dark:text-gray-300">Supplier Details</Label>
                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Track supplier and batch information
                    </p>
                  </div>
                  <div class="md:col-span-2 space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <Label class="text-xs text-gray-500 dark:text-gray-400">Supplier Name</Label>
                        <Input 
                          v-model="formData.supplier"
                          placeholder="Supplier name"
                        />
                      </div>
                      <div class="space-y-2">
                        <Label class="text-xs text-gray-500 dark:text-gray-400">Batch Number</Label>
                        <Input 
                          v-model="formData.batchNumber"
                          placeholder="Batch number"
                        />
                      </div>
                    </div>

                    <div class="space-y-2">
                      <Label class="text-xs text-gray-500 dark:text-gray-400">Expiry Date</Label>
                      <Input 
                        type="date"
                        v-model="formData.expiryDate"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!-- Right Sidebar Navigation -->
      <div class="hidden lg:block w-80 shrink-0">
        <div class="sticky top-24 space-y-4">
          <!-- Section Navigation -->
          <div class="bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-800/50 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <BarChart3 class="w-5 h-5 text-blue-600" />
              Form Progress
            </h3>
            
            <!-- Overall Progress -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Overall Completion</span>
                <span class="text-sm font-bold text-blue-600 dark:text-blue-400">{{ completionSteps.percentage }}%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  class="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-500"
                  :style="{ width: `${completionSteps.percentage}%` }"
                ></div>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {{ completionSteps.completed }} of {{ completionSteps.total }} sections completed
              </p>
            </div>

            <!-- Section Navigation -->
            <div class="space-y-2">
              <h4 class="text-sm font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">Sections</h4>
              
              <!-- Basic Information -->
              <button
                @click="scrollToSection('basic-information')"
                class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors text-left group"
              >
                <div class="flex items-center gap-3">
                  <div :class="[
                    'w-8 h-8 rounded-lg flex items-center justify-center transition-colors',
                    (formData.name && formData.categoryId) 
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500'
                  ]">
                    <Package class="w-4 h-4" />
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                      Basic Information
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Name, category, details</p>
                  </div>
                </div>
                <CheckCircle 
                  v-if="formData.name && formData.categoryId"
                  class="w-4 h-4 text-green-600 dark:text-green-400" 
                />
              </button>

              <!-- Product Images -->
              <button
                @click="scrollToSection('product-images')"
                class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors text-left group"
              >
                <div class="flex items-center gap-3">
                  <div :class="[
                    'w-8 h-8 rounded-lg flex items-center justify-center transition-colors',
                    uploadedImages.length > 0
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500'
                  ]">
                    <ImageIcon class="w-4 h-4" />
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                      Product Images
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ uploadedImages.length }} uploaded</p>
                  </div>
                </div>
                <CheckCircle 
                  v-if="uploadedImages.length > 0"
                  class="w-4 h-4 text-green-600 dark:text-green-400" 
                />
              </button>

              <!-- Pricing & Stock -->
              <button
                @click="scrollToSection('pricing-stock')"
                class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors text-left group"
              >
                <div class="flex items-center gap-3">
                  <div :class="[
                    'w-8 h-8 rounded-lg flex items-center justify-center transition-colors',
                    formData.price > 0
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500'
                  ]">
                    <DollarSign class="w-4 h-4" />
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                      Pricing & Stock
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ formData.price > 0 ? formatPrice(formData.price) : 'Not set' }}</p>
                  </div>
                </div>
                <CheckCircle 
                  v-if="formData.price > 0"
                  class="w-4 h-4 text-green-600 dark:text-green-400" 
                />
              </button>

              <!-- Restaurant Features (if applicable) -->
              <button
                v-if="isRestaurantSubscription"
                @click="scrollToSection('restaurant-features')"
                class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors text-left group"
              >
                <div class="flex items-center gap-3">
                  <div :class="[
                    'w-8 h-8 rounded-lg flex items-center justify-center transition-colors',
                    (restaurantFeatures.mealTimes && restaurantFeatures.mealTimes.length > 0) || restaurantFeatures.spiceLevel
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500'
                  ]">
                    <ChefHat class="w-4 h-4" />
                  </div>
                  <div>
                    <div class="flex items-center gap-2">
                      <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                        Restaurant Features
                      </p>
                      <ChevronDown 
                        :class="[
                          'w-3 h-3 text-gray-400 transition-transform duration-200',
                          isRestaurantFeaturesExpanded ? 'rotate-180' : ''
                        ]" 
                      />
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      {{ isRestaurantFeaturesExpanded ? 'Expanded' : 'Collapsed' }} • Optional
                    </p>
                  </div>
                </div>
                <CheckCircle 
                  v-if="(restaurantFeatures.mealTimes && restaurantFeatures.mealTimes.length > 0) || restaurantFeatures.spiceLevel"
                  class="w-4 h-4 text-green-600 dark:text-green-400" 
                />
              </button>

              <!-- Availability -->
              <button
                @click="scrollToSection('availability')"
                class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors text-left group"
              >
                <div class="flex items-center gap-3">
                  <div :class="[
                    'w-8 h-8 rounded-lg flex items-center justify-center transition-colors',
                    formData.isAvailable !== undefined
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500'
                  ]">
                    <Calendar class="w-4 h-4" />
                  </div>
                  <div>
                    <div class="flex items-center gap-2">
                      <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                        Availability
                      </p>
                      <ChevronDown 
                        :class="[
                          'w-3 h-3 text-gray-400 transition-transform duration-200',
                          isAvailabilityExpanded ? 'rotate-180' : ''
                        ]" 
                      />
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      {{ isAvailabilityExpanded ? 'Expanded' : 'Collapsed' }} • Optional
                    </p>
                  </div>
                </div>
                <CheckCircle 
                  v-if="formData.isAvailable !== undefined"
                  class="w-4 h-4 text-green-600 dark:text-green-400" 
                />
              </button>

              <!-- Additional Information -->
              <button
                @click="scrollToSection('additional-information')"
                class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors text-left group"
              >
                <div class="flex items-center gap-3">
                  <div :class="[
                    'w-8 h-8 rounded-lg flex items-center justify-center transition-colors',
                    (formData.supplier || formData.batchNumber || formData.expiryDate)
                      ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' 
                      : 'bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500'
                  ]">
                    <Truck class="w-4 h-4" />
                  </div>
                  <div>
                    <div class="flex items-center gap-2">
                      <p class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                        Additional Info
                      </p>
                      <ChevronDown 
                        :class="[
                          'w-3 h-3 text-gray-400 transition-transform duration-200',
                          isAdditionalInfoExpanded ? 'rotate-180' : ''
                        ]" 
                      />
                    </div>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      {{ isAdditionalInfoExpanded ? 'Expanded' : 'Collapsed' }} • Optional
                    </p>
                  </div>
                </div>
                <CheckCircle 
                  v-if="formData.supplier || formData.batchNumber || formData.expiryDate"
                  class="w-4 h-4 text-green-600 dark:text-green-400" 
                />
              </button>
            </div>
          </div>

          <!-- Quick Stats -->
          <div class="bg-white/70 dark:bg-gray-900/70 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-800/50 p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center gap-2">
              <Info class="w-5 h-5 text-green-600" />
              Quick Stats
            </h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Form Validity</span>
                <Badge :variant="isFormValid ? 'default' : 'secondary'">
                  {{ isFormValid ? 'Valid' : 'Incomplete' }}
                </Badge>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Images</span>
                <Badge variant="outline">{{ uploadedImages.length }}</Badge>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Storage</span>
                <Badge variant="outline" class="text-xs">Firebase</Badge>
              </div>
              <div v-if="formData.price > 0 && formData.cost > 0" class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">Profit Margin</span>
                <Badge variant="outline">
                  {{ Math.round(((formData.price - formData.cost) / formData.price) * 100) }}%
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sticky Bottom Submit -->
    <div class="sticky bottom-0 left-0 right-0 z-40 bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-t border-white/20 dark:border-gray-800/50">
      <div class="max-w-9xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="flex items-center gap-2">
              <div class="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center">
                <span class="text-xs font-bold text-white">{{ completionSteps.completed }}/{{ completionSteps.total }}</span>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-900 dark:text-white">Form Progress</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ completionSteps.percentage }}% complete</p>
              </div>
            </div>
            <div class="hidden md:block w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                class="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-500"
                :style="{ width: `${completionSteps.percentage}%` }"
              ></div>
            </div>
          </div>

          <div class="flex items-center gap-3">
            <Button
              variant="outline"
              @click="cancelEditing"
              :disabled="isSaving"
              class="px-6"
            >
              Cancel
            </Button>
            
            <Button
              @click="saveProduct"
              :disabled="isSaving || !isFormValid"
              class="px-8 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 shadow-lg"
            >
              <div v-if="isSaving" class="flex items-center gap-2">
                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {{ props.isEditing ? 'Updating...' : 'Creating...' }}
              </div>
              <div v-else class="flex items-center gap-2">
                <Save class="w-4 h-4" />
                {{ props.isEditing ? 'Update Product' : 'Create Product' }}
              </div>
            </Button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create Category Dialog -->
    <Dialog v-model:open="showCreateCategoryDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Category</DialogTitle>
          <DialogDescription>
            Add a new category for organizing your products.
          </DialogDescription>
        </DialogHeader>
        
        <div class="space-y-4">
          <div class="space-y-2">
            <Label for="category-name">Category Name *</Label>
            <Input
              id="category-name"
              v-model="newCategoryForm.name"
              placeholder="Enter category name"
              :disabled="isCreatingCategory"
            />
          </div>

          <div class="space-y-2">
            <Label for="category-description">Description</Label>
            <Textarea
              id="category-description"
              v-model="newCategoryForm.description"
              placeholder="Optional description..."
              rows="2"
              :disabled="isCreatingCategory"
            />
          </div>

          <div class="space-y-2">
            <Label for="category-color">Color</Label>
            <div class="flex items-center gap-3">
              <input
                id="category-color"
                type="color"
                v-model="newCategoryForm.color"
                class="w-12 h-8 rounded border border-input cursor-pointer"
                :disabled="isCreatingCategory"
              />
              <span class="text-sm text-gray-500 dark:text-gray-400">{{ newCategoryForm.color }}</span>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            @click="showCreateCategoryDialog = false; resetCategoryForm()"
            :disabled="isCreatingCategory"
          >
            Cancel
          </Button>
          <Button
            @click="createCategory"
            :disabled="!newCategoryForm.name.trim() || isCreatingCategory"
          >
            <div v-if="isCreatingCategory" class="flex items-center gap-2">
              <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Creating...
            </div>
            <div v-else class="flex items-center gap-2">
              <Plus class="w-4 h-4" />
              Create Category
            </div>
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Barcode Scanner Dialog -->
    <Dialog v-model:open="showBarcodeScanner">
      <DialogContent class="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle class="flex items-center gap-2">
            <Camera class="w-5 h-5" />
            Barcode Scanner
          </DialogTitle>
          <DialogDescription>
            Position the barcode within the scanning area or enter manually
          </DialogDescription>
        </DialogHeader>
        
        <div class="space-y-4">
          <!-- Camera Scanner Area -->
          <div class="relative bg-black rounded-lg overflow-hidden aspect-video">
            <!-- Camera View -->
            <video
              ref="videoElement"
              class="w-full h-full object-cover"
              autoplay
              muted
              playsinline
            ></video>
            
            <!-- Scanning Overlay -->
            <div class="absolute inset-0 flex items-center justify-center">
              <!-- Scanning Rectangle -->
              <div class="relative w-80 h-32 border-2 border-white rounded-lg">
                <!-- Corner markers -->
                <div class="absolute -top-1 -left-1 w-6 h-6 border-t-4 border-l-4 border-blue-500 rounded-tl"></div>
                <div class="absolute -top-1 -right-1 w-6 h-6 border-t-4 border-r-4 border-blue-500 rounded-tr"></div>
                <div class="absolute -bottom-1 -left-1 w-6 h-6 border-b-4 border-l-4 border-blue-500 rounded-bl"></div>
                <div class="absolute -bottom-1 -right-1 w-6 h-6 border-b-4 border-r-4 border-blue-500 rounded-br"></div>
                
                <!-- Scanning animation line -->
                <div class="absolute inset-x-0 top-1/2 h-0.5 bg-red-500 opacity-80 animate-pulse"></div>
              </div>
            </div>
            
            <!-- Loading overlay -->
            <div v-if="isScannerLoading" class="absolute inset-0 bg-black/50 flex items-center justify-center">
              <div class="flex flex-col items-center gap-3 text-white">
                <div class="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent"></div>
                <span class="text-sm">Starting camera...</span>
              </div>
            </div>
            
            <!-- Error overlay -->
            <div v-if="scannerError" class="absolute inset-0 bg-black/80 flex items-center justify-center">
              <div class="text-center text-white p-6">
                <AlertTriangle class="w-12 h-12 mx-auto mb-3 text-red-400" />
                <h3 class="text-lg font-semibold mb-2">Camera Error</h3>
                <p class="text-sm text-gray-300 mb-4">{{ scannerError }}</p>
                <Button variant="outline" size="sm" @click="startCamera" class="bg-white text-black">
                  Try Again
                </Button>
              </div>
            </div>
          </div>
          
          <!-- Manual Input Fallback -->
          <div class="space-y-2">
            <Label for="manual-barcode">Or enter barcode manually:</Label>
            <div class="flex gap-2">
              <Input
                id="manual-barcode"
                v-model="manualBarcodeInput"
                placeholder="Enter barcode number"
                class="flex-1"
                @keydown.enter="confirmManualBarcode"
              />
              <Button 
                @click="confirmManualBarcode"
                :disabled="!manualBarcodeInput.trim()"
              >
                Confirm
              </Button>
            </div>
          </div>
          
          <!-- Instructions -->
          <div class="text-center text-sm text-muted-foreground">
            <p>Position the barcode within the white rectangle for automatic scanning</p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" @click="closeBarcodeScanner">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

  <style scoped>
  .khmer-font {
    font-family: 'Noto Sans Khmer', sans-serif;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar for sidebar */
  .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
  }

  .overflow-y-auto::-webkit-scrollbar-track {
    background: transparent;
  }

  .overflow-y-auto::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.3);
    border-radius: 3px;
  }

  .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.5);
  }

  /* Focus states for navigation buttons */
  button:focus-visible {
    outline: 2px solid rgb(59, 130, 246);
    outline-offset: 2px;
  }

  /* Section headers spacing */
  section {
    scroll-margin-top: 100px;
  }

  /* Enhanced card shadows */
  .card-enhanced {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: box-shadow 0.2s ease-in-out;
  }

  .card-enhanced:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  /* Enhanced navigation sections */
  .group:hover .w-6.h-6 {
    transform: scale(1.05);
  }

  /* Progress animation */
  .h-1\.5 {
    background: linear-gradient(90deg, #f3f4f6, #e5e7eb);
  }

  .h-1\.5 > div {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease-in-out;
  }

  /* Micro-interactions */
  .transition-all {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Hover states for sections */
  .group:hover .text-gray-400 {
    color: rgb(107, 114, 128);
  }

  /* Active section indicator */
  .bg-blue-50 {
    position: relative;
  }

  .bg-blue-50::before {
    content: '';
    position: absolute;
    left: -4px;
    top: 0;
    bottom: 0;
    width: 3px;
    background: rgb(59, 130, 246);
    border-radius: 0 2px 2px 0;
  }

  /* Sticky bottom shadow */
  .sticky.bottom-0 {
    box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  /* Enhanced button states */
  .hover\\:bg-gray-50:hover {
    background-color: rgb(249, 250, 251);
    transform: translateY(-1px);
  }

  /* Loading states */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  </style>