<script setup lang="ts">
import { computed } from 'vue'
import DuotoneIcon from './DuotoneIcon.vue'
import { useTheme } from '@/composables/useTheme'
import { useLanguage } from '@/composables/useLanguage'

const { theme, currentTheme, cycleTheme } = useTheme()
const { t } = useLanguage()

// Get icon based on current theme - show what theme is active
const themeIcon = computed(() => {
  if (theme.value === 'system') {
    return 'screen'
  }
  return currentTheme.value === 'dark' ? 'moon' : 'sun'
})

// Get tooltip text
const tooltipText = computed(() => {
  const nextThemes = { light: 'dark', dark: 'system', system: 'light' }
  const nextTheme = nextThemes[theme.value as keyof typeof nextThemes]
  return t('theme.switchTo', { theme: t(`theme.${nextTheme}`) })
})
</script>

<template>
  <button
    @click="cycleTheme"
    :title="tooltipText"
    class="fixed bottom-20 right-6 z-50 group flex items-center justify-center w-12 h-12 bg-white/95 hover:bg-white dark:bg-gray-900/95 dark:hover:bg-gray-900 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
  >
    <!-- Theme icon -->
    <DuotoneIcon
      :name="themeIcon"
      size="lg"
      class="text-gray-700 dark:text-gray-300 group-hover:text-black dark:group-hover:text-white transition-colors duration-200"
    />

    <!-- Theme indicator -->
    <div
      class="absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white dark:border-gray-900 transition-colors duration-200"
      :class="{
        'bg-yellow-400': theme === 'light',
        'bg-blue-600': theme === 'dark',
        'bg-green-500': theme === 'system',
      }"
    ></div>

    <!-- Hover tooltip -->
    <div
      class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-black/80 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none"
    >
      {{ tooltipText }}
      <div
        class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80"
      ></div>
    </div>
  </button>
</template>
