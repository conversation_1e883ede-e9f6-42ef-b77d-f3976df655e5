<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCompanyStore } from '@/stores/company'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import DuotoneIcon from './DuotoneIcon.vue'
import type { CreateCompanyRequest } from '@/types/company'

interface Props {
  open: boolean
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'company-created', company: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const authStore = useAuthStore()
const companyStore = useCompanyStore()

// Form state
const formData = ref<CreateCompanyRequest>({
  name: '',
  email: '',
  ownerId: '', // Will be set when dialog opens
  phone: '',
  businessType: 'private_limited',
  businessLicense: '',
  taxId: '',
  vatNumber: '',
  address: {
    street: '',
    commune: '',
    district: '',
    province: 'Phnom Penh',
    postalCode: '',
    country: 'Cambodia'
  },
  bankInfo: {
    bankName: '',
    accountNumber: '',
    accountName: '',
    swift: ''
  },
  settings: {
    timezone: 'Asia/Phnom_Penh',
    currency: 'USD',
    language: 'both',
    taxRate: 10,
    witholdingTaxRate: 14,
    dateFormat: 'DD/MM/YYYY',
    numberFormat: 'US'
  }
})

const isLoading = ref(false)
const error = ref('')
const currentStep = ref(1)

// Business type options
const businessTypes = [
  { value: 'sole_proprietorship', label: 'Sole Proprietorship' },
  { value: 'partnership', label: 'Partnership' },
  { value: 'private_limited', label: 'Private Limited Company' },
  { value: 'public_limited', label: 'Public Limited Company' },
  { value: 'branch_office', label: 'Branch Office' },
  { value: 'representative_office', label: 'Representative Office' },
  { value: 'ngo', label: 'Non-Governmental Organization' },
  { value: 'cooperative', label: 'Cooperative' }
]

// Province options for Cambodia
const provinces = [
  'Phnom Penh', 'Kandal', 'Takeo', 'Kampong Speu', 'Kampong Chhnang',
  'Kampong Cham', 'Kampong Thom', 'Siem Reap', 'Battambang', 'Pursat',
  'Koh Kong', 'Kampot', 'Kep', 'Preah Sihanouk', 'Preah Vihear',
  'Stung Treng', 'Kratie', 'Mondulkiri', 'Ratanakiri', 'Banteay Meanchey',
  'Pailin', 'Oddar Meanchey', 'Svay Rieng', 'Prey Veng', 'Tboung Khmum'
]

// Currency options
const currencies = [
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'KHR', label: 'Khmer Riel (KHR)' }
]

// Language options
const languages = [
  { value: 'en', label: 'English' },
  { value: 'km', label: 'Khmer' },
  { value: 'both', label: 'Both Languages' }
]

// Date format options
const dateFormats = [
  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' }
]

// Number format options
const numberFormats = [
  { value: 'US', label: 'US Format (1,234.56)' },
  { value: 'EU', label: 'EU Format (1.234,56)' },
  { value: 'KH', label: 'Khmer Format' }
]

// Validation
const isStep1Valid = computed(() => {
  return formData.value.name.trim() !== '' &&
         formData.value.email.trim() !== '' &&
         formData.value.ownerId.trim() !== ''
})

const isStep2Valid = computed(() => {
  // Address is optional but if provided, should have basic info
  return true // Basic info is optional
})

const isStep3Valid = computed(() => {
  // Settings have defaults, so always valid
  return true
})

// Methods
const handleClose = () => {
  emit('update:open', false)
  resetForm()
}

const resetForm = () => {
  currentStep.value = 1
  error.value = ''
  isLoading.value = false
  formData.value = {
    name: '',
    email: '',
    ownerId: authStore.currentUser?._id || '',
    phone: '',
    businessType: 'private_limited',
    businessLicense: '',
    taxId: '',
    vatNumber: '',
    address: {
      street: '',
      commune: '',
      district: '',
      province: 'Phnom Penh',
      postalCode: '',
      country: 'Cambodia'
    },
    bankInfo: {
      bankName: '',
      accountNumber: '',
      accountName: '',
      swift: ''
    },
    settings: {
      timezone: 'Asia/Phnom_Penh',
      currency: 'USD',
      language: 'both',
      taxRate: 10,
      witholdingTaxRate: 14,
      dateFormat: 'DD/MM/YYYY',
      numberFormat: 'US'
    }
  }
}

const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}

const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const handleSubmit = async () => {
  if (!authStore.currentUser) {
    error.value = 'User not authenticated'
    return
  }

  try {
    isLoading.value = true
    error.value = ''

    // Ensure ownerId is set
    formData.value.ownerId = authStore.currentUser._id

    const company = await companyStore.createCompany(formData.value)

    // Switch to the new company
    await authStore.switchCompany(company._id)

    emit('company-created', company)
    handleClose()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to create company'
  } finally {
    isLoading.value = false
  }
}

// Auto-fill account name based on company name
const autoFillAccountName = () => {
  if (formData.value.name && !formData.value.bankInfo?.accountName) {
    if (formData.value.bankInfo) {
      formData.value.bankInfo.accountName = formData.value.name
    }
  }
}

// Watch for dialog open to set ownerId
watch(() => props.open, (newOpen) => {
  if (newOpen && authStore.currentUser && !formData.value.ownerId) {
    formData.value.ownerId = authStore.currentUser._id
  }
}, { immediate: true })
</script>

<template>
  <Dialog :open="open" @update:open="handleClose">
    <DialogContent class="max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <DuotoneIcon name="business" class="w-6 h-6 text-blue-600" />
          Create New Company
        </DialogTitle>
        <DialogDescription>
          Set up a new company to manage your business operations and team.
        </DialogDescription>
      </DialogHeader>

      <!-- Progress Steps -->
      <div class="flex items-center justify-center mb-6">
        <div class="flex items-center space-x-2">
          <div v-for="step in 3" :key="step" class="flex items-center">
            <div :class="[
              'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
              currentStep >= step
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 text-gray-600'
            ]">
              {{ step }}
            </div>
            <div v-if="step < 3" :class="[
              'w-12 h-0.5 mx-2',
              currentStep > step ? 'bg-blue-600' : 'bg-gray-200'
            ]"></div>
          </div>
        </div>
      </div>

      <!-- Step 1: Basic Information -->
      <div v-if="currentStep === 1" class="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Basic Company Information</CardTitle>
            <CardDescription>
              Enter the essential details about your company
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label for="company-name">Company Name *</Label>
                <Input
                  id="company-name"
                  v-model="formData.name"
                  placeholder="Enter company name"
                  @blur="autoFillAccountName"
                />
              </div>
              <div class="space-y-2">
                <Label for="company-email">Company Email *</Label>
                <Input
                  id="company-email"
                  v-model="formData.email"
                  type="email"
                  placeholder="<EMAIL>"
                />
              </div>
              <div class="space-y-2">
                <Label for="company-phone">Phone Number</Label>
                <Input
                  id="company-phone"
                  v-model="formData.phone"
                  placeholder="+855 12 345 678"
                />
              </div>
              <div class="space-y-2">
                <Label for="business-type">Business Type *</Label>
                <Select v-model="formData.businessType">
                  <SelectTrigger>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="type in businessTypes"
                      :key="type.value"
                      :value="type.value"
                    >
                      {{ type.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="space-y-2">
                <Label for="business-license">Business License</Label>
                <Input
                  id="business-license"
                  v-model="formData.businessLicense"
                  placeholder="Business license number"
                />
              </div>
              <div class="space-y-2">
                <Label for="tax-id">Tax ID</Label>
                <Input
                  id="tax-id"
                  v-model="formData.taxId"
                  placeholder="Tax identification number"
                />
              </div>
              <div class="space-y-2">
                <Label for="vat-number">VAT Number</Label>
                <Input
                  id="vat-number"
                  v-model="formData.vatNumber"
                  placeholder="VAT registration number"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Step 2: Address & Banking -->
      <div v-if="currentStep === 2" class="space-y-6">
        <!-- Address Information -->
        <Card>
          <CardHeader>
            <CardTitle>Business Address</CardTitle>
            <CardDescription>
              Enter your company's business address
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <Label for="street">Street Address</Label>
              <Textarea
                id="street"
                v-model="formData.address!.street"
                placeholder="Enter full street address"
                rows="2"
              />
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label for="commune">Commune/Sangkat</Label>
                <Input
                  id="commune"
                  v-model="formData.address!.commune"
                  placeholder="Commune or Sangkat"
                />
              </div>
              <div class="space-y-2">
                <Label for="district">District/Khan</Label>
                <Input
                  id="district"
                  v-model="formData.address!.district"
                  placeholder="District or Khan"
                />
              </div>
              <div class="space-y-2">
                <Label for="province">Province</Label>
                <Select v-model="formData.address!.province">
                  <SelectTrigger>
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="province in provinces"
                      :key="province"
                      :value="province"
                    >
                      {{ province }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div class="space-y-2">
                <Label for="postal-code">Postal Code</Label>
                <Input
                  id="postal-code"
                  v-model="formData.address!.postalCode"
                  placeholder="Postal code"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Banking Information -->
        <Card>
          <CardHeader>
            <CardTitle>Banking Information</CardTitle>
            <CardDescription>
              Optional: Add your company's banking details for invoicing
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label for="bank-name">Bank Name</Label>
                <Input
                  id="bank-name"
                  v-model="formData.bankInfo!.bankName"
                  placeholder="e.g., ACLEDA Bank Plc."
                />
              </div>
              <div class="space-y-2">
                <Label for="account-number">Account Number</Label>
                <Input
                  id="account-number"
                  v-model="formData.bankInfo!.accountNumber"
                  placeholder="Bank account number"
                />
              </div>
              <div class="space-y-2">
                <Label for="account-name">Account Name</Label>
                <Input
                  id="account-name"
                  v-model="formData.bankInfo!.accountName"
                  placeholder="Account holder name"
                />
              </div>
              <div class="space-y-2">
                <Label for="swift-code">SWIFT Code</Label>
                <Input
                  id="swift-code"
                  v-model="formData.bankInfo!.swift"
                  placeholder="SWIFT/BIC code"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Step 3: Settings -->
      <div v-if="currentStep === 3" class="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Company Settings</CardTitle>
            <CardDescription>
              Configure your company's operational preferences
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label for="currency">Default Currency</Label>
                <Select v-model="formData.settings!.currency">
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="currency in currencies"
                      :key="currency.value"
                      :value="currency.value"
                    >
                      {{ currency.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div class="space-y-2">
                <Label for="language">Default Language</Label>
                <Select v-model="formData.settings!.language">
                  <SelectTrigger>
                    <SelectValue placeholder="Select language" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="lang in languages"
                      :key="lang.value"
                      :value="lang.value"
                    >
                      {{ lang.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div class="space-y-2">
                <Label for="date-format">Date Format</Label>
                <Select v-model="formData.settings!.dateFormat">
                  <SelectTrigger>
                    <SelectValue placeholder="Select date format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="format in dateFormats"
                      :key="format.value"
                      :value="format.value"
                    >
                      {{ format.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div class="space-y-2">
                <Label for="number-format">Number Format</Label>
                <Select v-model="formData.settings!.numberFormat">
                  <SelectTrigger>
                    <SelectValue placeholder="Select number format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem
                      v-for="format in numberFormats"
                      :key="format.value"
                      :value="format.value"
                    >
                      {{ format.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Separator />

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <Label for="tax-rate">VAT Rate (%)</Label>
                <Input
                  id="tax-rate"
                  v-model.number="formData.settings!.taxRate"
                  type="number"
                  placeholder="10"
                  min="0"
                  max="100"
                />
                <p class="text-xs text-gray-500">Standard VAT rate in Cambodia is 10%</p>
              </div>
              <div class="space-y-2">
                <Label for="withholding-tax-rate">Withholding Tax Rate (%)</Label>
                <Input
                  id="withholding-tax-rate"
                  v-model.number="formData.settings!.witholdingTaxRate"
                  type="number"
                  placeholder="14"
                  min="0"
                  max="100"
                />
                <p class="text-xs text-gray-500">Standard withholding tax in Cambodia is 14%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Error Alert -->
      <Alert v-if="error" variant="destructive">
        <DuotoneIcon name="error" class="w-4 h-4" />
        <AlertDescription>{{ error }}</AlertDescription>
      </Alert>

      <!-- Navigation Buttons -->
      <div class="flex justify-between pt-4">
        <div>
          <Button
            v-if="currentStep > 1"
            variant="outline"
            @click="prevStep"
            :disabled="isLoading"
          >
            <DuotoneIcon name="arrow_back" class="w-4 h-4 mr-2" />
            Previous
          </Button>
        </div>

        <div class="flex gap-2">
          <Button variant="outline" @click="handleClose" :disabled="isLoading">
            Cancel
          </Button>

          <Button
            v-if="currentStep < 3"
            @click="nextStep"
            :disabled="(currentStep === 1 && !isStep1Valid) ||
                      (currentStep === 2 && !isStep2Valid) ||
                      isLoading"
          >
            Next
            <DuotoneIcon name="arrow_forward" class="w-4 h-4 ml-2" />
          </Button>

          <Button
            v-else
            @click="handleSubmit"
            :disabled="!isStep3Valid || isLoading"
          >
            <DuotoneIcon name="sync" v-if="isLoading" class="w-4 h-4 mr-2 animate-spin" />
            <DuotoneIcon name="check" v-else class="w-4 h-4 mr-2" />
            {{ isLoading ? 'Creating...' : 'Create Company' }}
          </Button>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
