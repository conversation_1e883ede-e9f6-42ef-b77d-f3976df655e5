<script setup lang="ts">
import { ref, computed } from 'vue'
import DuotoneIcon from './DuotoneIcon.vue'
import { useTheme } from '@/composables/useTheme'
import { useLanguage } from '@/composables/useLanguage'

const { theme, currentTheme, cycleTheme, getThemeInfo } = useTheme()
const { currentLanguage, switchLanguage, getLanguageFlag, getLanguageName } = useLanguage()

// Control panel state
const isExpanded = ref(false)

// Get current theme info
const currentThemeInfo = computed(() => getThemeInfo(theme.value))

// Get theme icon
const themeIcon = computed(() => {
  if (theme.value === 'system') {
    return 'screen'
  }
  return currentTheme.value === 'dark' ? 'moon' : 'sun'
})

// Toggle expanded state
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// Handle outside click to close
const handleOutsideClick = () => {
  if (isExpanded.value) {
    isExpanded.value = false
  }
}
</script>

<template>
  <!-- Backdrop for outside click -->
  <div v-if="isExpanded" @click="handleOutsideClick" class="fixed inset-0 z-40"></div>

  <!-- Floating Controls Panel -->
  <div class="fixed bottom-6 right-6 z-50">
    <!-- Main Control Button (always visible) -->
    <div class="relative">
      <button
        @click="toggleExpanded"
        class="group relative flex items-center justify-center w-14 h-14 bg-white/95 hover:bg-white dark:bg-gray-900/95 dark:hover:bg-gray-900 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
        :class="{ 'rotate-45': isExpanded }"
      >
        <!-- Control Icon -->
        <div class="relative">
          <DuotoneIcon
            name="setting"
            size="lg"
            class="text-gray-600 dark:text-gray-400 group-hover:text-gray-800 dark:group-hover:text-gray-200 transition-colors duration-200"
          />

          <!-- Status indicators -->
          <div class="absolute -top-1 -right-1 flex gap-0.5">
            <!-- Language indicator -->
            <div
              class="w-2 h-2 rounded-full bg-blue-500 border border-white dark:border-gray-900"
            ></div>
            <!-- Theme indicator -->
            <div
              class="w-2 h-2 rounded-full border border-white dark:border-gray-900 transition-colors duration-200"
              :class="{
                'bg-yellow-400': theme === 'light',
                'bg-blue-600': theme === 'dark',
                'bg-green-500': theme === 'system',
              }"
            ></div>
          </div>
        </div>
      </button>

      <!-- Expanded Controls -->
      <div
        v-show="isExpanded"
        class="absolute bottom-full right-0 mb-4 space-y-3"
        :class="isExpanded ? 'animate-slide-up' : ''"
      >
        <!-- Language Switcher -->
        <div class="group/lang relative">
          <button
            @click="switchLanguage"
            class="flex items-center gap-3 px-4 py-3 bg-white/95 hover:bg-white dark:bg-gray-900/95 dark:hover:bg-gray-900 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105 min-w-[120px]"
          >
            <!-- Language Flag -->
            <span class="text-xl">{{ getLanguageFlag(currentLanguage) }}</span>

            <!-- Language Name -->
            <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
              {{ getLanguageName(currentLanguage) }}
            </span>

            <!-- Switch Arrow -->
            <DuotoneIcon
              name="arrow-up-down"
              size="sm"
              class="text-gray-500 dark:text-gray-400 group-hover/lang:text-blue-600 dark:group-hover/lang:text-blue-400 transition-colors duration-200"
            />
          </button>

          <!-- Language Tooltip -->
          <div
            class="absolute bottom-full right-0 mb-2 px-3 py-1.5 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover/lang:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none"
          >
            Switch Language
            <div
              class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"
            ></div>
          </div>
        </div>

        <!-- Theme Switcher -->
        <div class="group/theme relative">
          <button
            @click="cycleTheme"
            class="flex items-center gap-3 px-4 py-3 bg-white/95 hover:bg-white dark:bg-gray-900/95 dark:hover:bg-gray-900 backdrop-blur-md border border-gray-200/50 dark:border-gray-700/50 rounded-xl shadow-md hover:shadow-lg transition-all duration-200 hover:scale-105 min-w-[120px]"
          >
            <!-- Theme Icon -->
            <DuotoneIcon
              :name="themeIcon"
              size="md"
              :class="`transition-colors duration-200 ${
                theme === 'light'
                  ? 'text-yellow-500'
                  : theme === 'dark'
                    ? 'text-blue-400'
                    : 'text-green-500'
              }`"
            />

            <!-- Theme Name -->
            <span class="text-sm font-medium text-gray-800 dark:text-gray-200">
              {{ currentThemeInfo.label }}
            </span>

            <!-- Cycle Arrow -->
            <DuotoneIcon
              name="refresh"
              size="sm"
              class="text-gray-500 dark:text-gray-400 group-hover/theme:text-purple-600 dark:group-hover/theme:text-purple-400 transition-colors duration-200"
            />
          </button>

          <!-- Theme Tooltip -->
          <div
            class="absolute bottom-full right-0 mb-2 px-3 py-1.5 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover/theme:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none"
          >
            {{ currentThemeInfo.description }}
            <div
              class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
</style>
