import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Font utility function
export function getFontClass(language: string = 'en'): string {
  return language === 'km' ? 'font-khmer' : 'font-english'
}

// Icon data for the duotone icon system

// Comprehensive list of all available icons
export const iconNames = [
  'abstract-1',
  'abstract-2',
  'abstract-3',
  'abstract-4',
  'abstract-5',
  'abstract-6',
  'abstract-7',
  'abstract-8',
  'abstract-9',
  'abstract-10',
  'add-files',
  'add-folder',
  'add-notepad',
  'additem',
  'address-book',
  'airplane',
  'airplane-square',
  'airpod',
  'android',
  'angular',
  'apple',
  'archive',
  'archive-tick',
  'arrow-circle-left',
  'arrow-circle-right',
  'arrow-down',
  'arrow-down-left',
  'arrow-down-right',
  'arrow-left',
  'arrow-right',
  'arrow-up',
  'arrow-up-left',
  'arrow-up-right',
  'arrow-mix',
  'arrow-right-left',
  'arrow-two-diagonals',
  'arrow-up-down',
  'arrow-zigzag',
  'arrows-circle',
  'arrows-loop',
  'artificial-intelligence',
  'autobrightness',
  'avalanche-avax',
  'award',
  'badge',
  'bandage',
  'bank',
  'bar-chart',
  'barcode',
  'basket',
  'basket-ok',
  'behance',
  'bill',
  'binance',
  'binance-usd-busd',
  'bitcoin',
  'black-down',
  'black-left',
  'black-right',
  'black-up',
  'bluetooth',
  'book',
  'book-open',
  'book-square',
  'bookmark',
  'bookmark-2',
  'bootstrap',
  'briefcase',
  'brifecase-cros',
  'brifecase-tick',
  'brifecase-timer',
  'brush',
  'bucket',
  'bucket-square',
  'burger-menu',
  'burger-menu-1',
  'burger-menu-2',
  'burger-menu-3',
  'burger-menu-4',
  'burger-menu-5',
  'burger-menu-6',
  'bus',
  'calculator',
  'calculatoror',
  'calendar',
  'calendar-2',
  'calendar-8',
  'calendar-add',
  'calendar-edit',
  'calendar-remove',
  'calendar-search',
  'calendar-tick',
  'call',
  'capsule',
  'car',
  'category',
  'cd',
  'celsius-cel',
  'chart',
  'chart-line',
  'chart-line-down',
  'chart-line-down-2',
  'chart-line-star',
  'chart-line-up',
  'chart-line-up-2',
  'chart-pie-3',
  'chart-pie-4',
  'chart-pie-simple',
  'chart-pie-too',
  'chart-simple',
  'chart-simple-2',
  'chart-simple-3',
  'check',
  'check-circle',
  'check-squared',
  'cheque',
  'chrome',
  'classmates',
  'click',
  'clipboard',
  'cloud',
  'cloud-add',
  'cloud-change',
  'cloud-download',
  'code',
  'coffee',
  'color-swatch',
  'colors-square',
  'compass',
  'copy',
  'copy-success',
  'courier',
  'courier-express',
  'credit-cart',
  'cross',
  'cross-circle',
  'cross-square',
  'crown',
  'crown-2',
  'css',
  'cube-2',
  'cube-3',
  'cup',
  'cursor',
  'dash',
  'data',
  'delete-files',
  'delete-folder',
  'delivery',
  'delivery-2',
  'delivery-3',
  'delivery-24',
  'delivery-door',
  'delivery-geolocation',
  'delivery-time',
  'design-1',
  'design-2',
  'desktop-mobile',
  'devices',
  'devices-2',
  'diamonds',
  'directbox-default',
  'disconnect',
  'discount',
  'disguise',
  'disk',
  'dislike',
  'dj',
  'document',
  'double-check',
  'double-check-circle',
  'double-down',
  'double-left',
  'double-left-arrow',
  'double-right',
  'double-right-arrow',
  'double-up',
  'dollar',
  'dots-circle',
  'dots-circle-vertical',
  'dots-horizontal',
  'dots-square',
  'dots-square-vertical',
  'dots-vertical',
  'down',
  'down-square',
  'dribbble',
  'drop',
  'dropbox',
  'educare-ekt',
  'electricity',
  'electronic-clock',
  'element-1',
  'element-2',
  'element-3',
  'element-4',
  'element-5',
  'element-6',
  'element-7',
  'element-8',
  'element-9',
  'element-10',
  'element-11',
  'element-12',
  'element-equal',
  'element-plus',
  'emoji-happy',
  'enjin-coin-enj',
  'ensure',
  'entrance-left',
  'entrance-right',
  'eraser',
  'euro',
  'exit-down',
  'exit-left',
  'exit-right',
  'exit-right-corner',
  'exit-up',
  'external-drive',
  'eye',
  'eye-slash',
  'face-id',
  'facebook',
  'fasten',
  'fatrows',
  'feather',
  'figma',
  'file',
  'file-added',
  'file-deleted',
  'file-down',
  'file-left',
  'file-right',
  'file-sheet',
  'file-up',
  'files',
  'filter',
  'filter-edit',
  'filter-search',
  'filter-square',
  'filter-tablet',
  'filter-tick',
  'financial-schedule',
  'fingerprint-scanning',
  'flag',
  'flash-circle',
  'flask',
  'focus',
  'folder',
  'folder-added',
  'folder-down',
  'folder-up',
  'frame',
  'geolocation',
  'geolocation-home',
  'ghost',
  'gift',
  'github',
  'glass',
  'google',
  'google-play',
  'graph',
  'graph-2',
  'graph-3',
  'graph-4',
  'graph-up',
  'grid',
  'grid-2',
  'handcart',
  'happyemoji',
  'heart',
  'heart-circle',
  'home',
  'home-1',
  'home-2',
  'home-3',
  'html',
  'icon',
  'illustrator',
  'information',
  'information-1',
  'information-2',
  'information-3',
  'information-4',
  'instagram',
  'joystick',
  'js',
  'js-2',
  'kanban',
  'key',
  'key-square',
  'keyboard',
  'laptop',
  'laravel',
  'left',
  'left-square',
  'like',
  'like-2',
  'like-folder',
  'like-shapes',
  'like-tag',
  'loading',
  'lock',
  'lock-2',
  'lock-3',
  'logistic',
  'lots-shopping',
  'lovely',
  'lts',
  'magnifier',
  'map',
  'mask',
  'maximize',
  'medal-star',
  'menu',
  'message',
  'message-add',
  'message-edit',
  'message-minus',
  'message-notify',
  'message-programming',
  'message-question',
  'message-text',
  'message-text-2',
  'messages',
  'microsoft',
  'milk',
  'minus',
  'minus-circle',
  'minus-folder',
  'minus-squared',
  'moon',
  'more-2',
  'mouse',
  'mouse-circle',
  'mouse-square',
  'nexo',
  'night-day',
  'note',
  'note-2',
  'notepad',
  'notepad-bookmark',
  'notepad-edit',
  'notification',
  'notification-1',
  'notification-bing',
  'notification-circle',
  'notification-favorite',
  'notification-on',
  'notification-status',
  'ocean',
  'office-bag',
  'package',
  'pad',
  'pails',
  'paintbucket',
  'paper-clip',
  'paper-plane',
  'parcel',
  'parcel-tracking',
  'password-check',
  'paypal',
  'pencil',
  'people',
  'percentage',
  'phone',
  'photoshop',
  'picture',
  'pill',
  'pin',
  'plus',
  'plus-circle',
  'plus-squared',
  'pointers',
  'price-tag',
  'printer',
  'profile-circle',
  'pulse',
  'purchase',
  'python',
  'question',
  'question-2',
  'questionnaire-tablet',
  'ranking',
  'react',
  'receipt-square',
  'rescue',
  'right',
  'right-left',
  'right-square',
  'rocket',
  'route',
  'router',
  'row-horizontal',
  'row-vertical',
  'safe-home',
  'satellite',
  'save',
  'save-2',
  'save-deposit',
  'scan-barcode',
  'screen',
  'scroll',
  'search',
  'search-list',
  'security-user',
  'setting',
  'setting-2',
  'setting-3',
  'setting-4',
  'share',
  'shield',
  'shield-cross',
  'shield-search',
  'shield-slash',
  'shield-tick',
  'ship',
  'shop',
  'simcard',
  'simcard-2',
  'size',
  'slack',
  'slider',
  'slider-horizontal',
  'slider-horizontal-2',
  'slider-vertica',
  'slider-vertical',
  'sms',
  'snapchat',
  'social-media',
  'soft',
  'soft-2',
  'soft-3',
  'some-files',
  'sort',
  'speaker',
  'spotify',
  'spring-framework',
  'square-brackets',
  'star',
  'status',
  'subtitle',
  'sun',
  'support',
  'switch',
  'syringe',
  'tab-tablet',
  'tablet',
  'tablet-delete',
  'tablet-down',
  'tablet-ok',
  'tablet-text-down',
  'tablet-text-up',
  'tablet-up',
  'tag',
  'tag-cross',
  'teacher',
  'technology-1',
  'technology-2',
  'technology-3',
  'technology-4',
  'telephone-geolocation',
  'test-tubes',
  'text',
  'text-bold',
  'text-circle',
  'text-italic',
  'text-number',
  'text-strikethrough',
  'text-underline',
  'textalign-center',
  'textalign-justifycenter',
  'textalign-left',
  'textalign-right',
  'thermometer',
  'theta-theta',
  'tiktok',
  'time',
  'timer',
  'to-left',
  'to-right',
  'toggle-off',
  'toggle-off-circle',
  'toggle-on',
  'toggle-on-circle',
  'trash',
  'trash-square',
  'tree',
  'trello',
  'ts',
  'twitch',
  'twitter',
  'two-credit-cart',
  'underlining',
  'up',
  'up-diagonal',
  'up-down',
  'up-square',
  'update-file',
  'update-folder',
  'user',
  'user-edit',
  'user-square',
  'user-tick',
  'users',
  'verify',
  'vibe-vibe',
  'virus',
  'vue',
  'vuesax',
  'wallet',
  'wanchain-wan',
  'watch',
  'whatsapp',
  'wifi',
  'wifi-home',
  'wifi-square',
  'wireframe',
  'wlan',
  'wrench',
  'xaomi',
  'xd',
  'xmr',
  'yii',
  'youtube',
]

// Icon categories for better organization
export const iconCategories = {
  all: iconNames,
  arrows: iconNames.filter(
    (name) =>
      name.includes('arrow') ||
      name.includes('up') ||
      name.includes('down') ||
      name.includes('left') ||
      name.includes('right'),
  ),
  ui: iconNames.filter((name) =>
    ['menu', 'burger', 'dots', 'setting', 'gear', 'toggle', 'switch', 'slider'].some((keyword) =>
      name.includes(keyword),
    ),
  ),
  files: iconNames.filter((name) =>
    ['file', 'folder', 'document', 'note', 'clipboard', 'archive'].some((keyword) =>
      name.includes(keyword),
    ),
  ),
  communication: iconNames.filter((name) =>
    ['message', 'call', 'phone', 'mail', 'notification', 'sms'].some((keyword) =>
      name.includes(keyword),
    ),
  ),
  social: iconNames.filter((name) =>
    [
      'facebook',
      'twitter',
      'instagram',
      'youtube',
      'github',
      'linkedin',
      'whatsapp',
      'tiktok',
    ].some((keyword) => name.includes(keyword)),
  ),
  business: iconNames.filter((name) =>
    [
      'briefcase',
      'office',
      'bank',
      'dollar',
      'euro',
      'credit',
      'purchase',
      'delivery',
      'shop',
    ].some((keyword) => name.includes(keyword)),
  ),
  technology: iconNames.filter((name) =>
    ['code', 'html', 'css', 'js', 'react', 'vue', 'angular', 'python', 'laptop', 'device'].some(
      (keyword) => name.includes(keyword),
    ),
  ),
  media: iconNames.filter((name) =>
    ['picture', 'camera', 'video', 'music', 'speaker', 'cd', 'play'].some((keyword) =>
      name.includes(keyword),
    ),
  ),
}

// Copy to clipboard helper function
export async function copyToClipboard(text: string): Promise<void> {
  try {
    await navigator.clipboard.writeText(text)
    return Promise.resolve()
  } catch (err) {
    console.error('Failed to copy: ', err)
    return Promise.reject(err)
  }
}
