export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}

export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

export class ApiClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor(config: ApiClientConfig) {
    this.baseURL = config.baseURL;
    this.timeout = config.timeout || 10000;
    this.defaultHeaders = config.headers || {};
  }

  private async request<T>(
    method: string,
    url: string,
    data?: any,
    headers?: Record<string, string>
  ): Promise<ApiResponse<T>> {
    const fullUrl = `${this.baseURL}${url}`;
    const requestHeaders = { ...this.defaultHeaders, ...headers };

    // Log requests with appropriate level
    if (url.includes('/auth/')) {
      console.debug(`Making ${method} request to ${fullUrl}`);
    } else {
      console.log(`Making ${method} request to ${fullUrl}`, { data, headers: requestHeaders });
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(fullUrl, {
        method,
        headers: {
          'Content-Type': 'application/json',
          ...requestHeaders,
        },
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      let responseData;
      try {
        responseData = await response.json();
      } catch {
        responseData = null;
      }

      const headersObj: Record<string, string> = {};
      response.headers.forEach((value, key) => {
        headersObj[key] = value;
      });

      const result: ApiResponse<T> = {
        data: responseData,
        status: response.status,
        statusText: response.statusText,
        headers: headersObj,
      };

      if (!response.ok) {
        console.error(`HTTP request failed: ${method} ${fullUrl}`, {
          status: response.status,
          statusText: response.statusText,
          data: responseData,
        });
        throw new Error(responseData?.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      console.log(`HTTP request successful: ${method} ${fullUrl}`, {
        status: response.status,
      });

      return result;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error) {
        console.error(`HTTP request error: ${method} ${fullUrl}`, {
          error: error.message,
        });
      }

      throw error;
    }
  }

  async get<T>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>('GET', url, undefined, headers);
  }

  async post<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>('POST', url, data, headers);
  }

  async put<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', url, data, headers);
  }

  async patch<T>(url: string, data?: any, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>('PATCH', url, data, headers);
  }

  async delete<T>(url: string, headers?: Record<string, string>): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', url, undefined, headers);
  }

  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  clearAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }
}

// API client instances for different services
export const apiClient = new ApiClient({
  baseURL: import.meta.env.VITE_API_GATEWAY_URL || 'http://localhost:3000',
});

export const coreApiClient = new ApiClient({
  baseURL: import.meta.env.VITE_CORE_API_URL || 'http://localhost:3001',
});

export const posApiClient = new ApiClient({
  baseURL: import.meta.env.VITE_POS_API_URL || 'http://localhost:3002',
});

export const loanApiClient = new ApiClient({
  baseURL: import.meta.env.VITE_LOAN_API_URL || 'http://localhost:3003',
});

// Use API Gateway as default
export default apiClient;
