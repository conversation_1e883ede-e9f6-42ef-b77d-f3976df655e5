import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { companyService } from '@/services/company.service'
import type {
  Company,
  CompanySubscription,
  BillingInfo,
  CreateCompanyRequest,
  SubscribeRequest,
  StartTrialRequest
} from '@/types/company'

export const useCompanyStore = defineStore('company', () => {
  // State
  const currentCompany = ref<Company | null>(null)
  const companies = ref<Company[]>([])
  const subscriptions = ref<CompanySubscription[]>([])
  const billingInfo = ref<BillingInfo | null>(null)
  const availableModules = ref<{ modules: any[]; posTypes: any[] } | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const hasActiveSubscriptions = computed(() => subscriptions.value.length > 0)
  const activeModules = computed(() =>
    subscriptions.value.filter(sub => sub.active).map(sub => sub.module)
  )
  const trialSubscriptions = computed(() =>
    subscriptions.value.filter(sub => sub.isTrialMode && sub.active)
  )
  const paidSubscriptions = computed(() =>
    subscriptions.value.filter(sub => !sub.isTrialMode && sub.active)
  )

  // Actions
  async function fetchCompanies(userId: string) {
    try {
      isLoading.value = true
      error.value = null

      const data = await companyService.getUserCompanies(userId)
      companies.value = data

      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch companies'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function fetchCompany(companyId: string) {
    try {
      isLoading.value = true
      error.value = null

      const company = await companyService.getCompany(companyId)
      currentCompany.value = company

      return company
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch company'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function createCompany(companyData: CreateCompanyRequest) {
    try {
      isLoading.value = true
      error.value = null

      const company = await companyService.createCompany(companyData)
      companies.value.push(company)
      currentCompany.value = company

      return company
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to create company'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function updateCompany(companyId: string, companyData: any) {
    try {
      isLoading.value = true
      error.value = null

      const company = await companyService.updateCompany(companyId, companyData)

      // Update in companies array
      const index = companies.value.findIndex(c => c._id === companyId)
      if (index !== -1) {
        companies.value[index] = company
      }

      // Update current company if it's the same
      if (currentCompany.value?._id === companyId) {
        currentCompany.value = company
      }

      return company
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to update company'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function fetchSubscriptions(companyId: string) {
    try {
      isLoading.value = true
      error.value = null

      const data = await companyService.getCompanySubscriptions(companyId)
      subscriptions.value = data

      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch subscriptions'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function subscribeToModule(companyId: string, subscriptionData: SubscribeRequest) {
    try {
      isLoading.value = true
      error.value = null

      const subscription = await companyService.subscribeToModule(companyId, subscriptionData)
      subscriptions.value.push(subscription)

      return subscription
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to subscribe to module'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function startTrial(companyId: string, trialData: StartTrialRequest) {
    try {
      isLoading.value = true
      error.value = null

      const subscription = await companyService.startTrial(companyId, trialData)
      subscriptions.value.push(subscription)

      return subscription
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to start trial'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function convertTrialToPaid(companyId: string, module: string) {
    try {
      isLoading.value = true
      error.value = null

      const subscription = await companyService.convertTrialToPaid(companyId, module)

      // Update subscription in array
      const index = subscriptions.value.findIndex(s => s.companyId === companyId && s.module === module)
      if (index !== -1) {
        subscriptions.value[index] = subscription
      }

      return subscription
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to convert trial'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function unsubscribeFromModule(companyId: string, module: string) {
    try {
      isLoading.value = true
      error.value = null

      await companyService.unsubscribeFromModule(companyId, module)

      // Remove from subscriptions array
      subscriptions.value = subscriptions.value.filter(
        s => !(s.companyId === companyId && s.module === module)
      )

    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to unsubscribe'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function fetchBillingInfo(companyId: string) {
    try {
      isLoading.value = true
      error.value = null

      const data = await companyService.getBillingInfo(companyId)
      billingInfo.value = data

      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch billing info'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function fetchAvailableModules() {
    try {
      const data = await companyService.getAvailableModules()
      availableModules.value = data
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to fetch available modules'
      throw err
    }
  }

  function setCurrentCompany(company: Company) {
    currentCompany.value = company
  }

  function clearError() {
    error.value = null
  }

  function resetStore() {
    currentCompany.value = null
    companies.value = []
    subscriptions.value = []
    billingInfo.value = null
    availableModules.value = null
    error.value = null
    isLoading.value = false
  }

  return {
    // State
    currentCompany,
    companies,
    subscriptions,
    billingInfo,
    availableModules,
    isLoading,
    error,

    // Getters
    hasActiveSubscriptions,
    activeModules,
    trialSubscriptions,
    paidSubscriptions,

    // Actions
    fetchCompanies,
    fetchCompany,
    createCompany,
    updateCompany,
    fetchSubscriptions,
    subscribeToModule,
    startTrial,
    convertTrialToPaid,
    unsubscribeFromModule,
    fetchBillingInfo,
    fetchAvailableModules,
    setCurrentCompany,
    clearError,
    resetStore
  }
})
