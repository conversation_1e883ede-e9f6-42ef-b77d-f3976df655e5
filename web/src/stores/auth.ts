import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authService } from '@/services/auth.service'
import apiClient from '@/lib/api-client'
import type { User, AuthTokens, LoginRequest, RegisterRequest } from '@/types/auth'

interface UserCompany {
  _id: string;
  name: string;
  role: string;
  joinedAt: string;
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const tokens = ref<AuthTokens | null>(null)
  const userCompanies = ref<UserCompany[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const isInitializing = ref(true)
  const loadingMessage = ref('')
  const isSwitchingCompany = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!tokens.value?.accessToken)
  const currentUser = computed(() => user.value)
  const hasCurrentCompany = computed(() => !!user.value?.currentCompanyId)
  const currentCompany = computed(() => {
    if (!user.value?.currentCompanyId || !userCompanies.value.length) return null
    return userCompanies.value.find(company => company._id === user.value?.currentCompanyId) || null
  })
  const hasMultipleCompanies = computed(() => userCompanies.value.length > 1)

  // Actions
  async function login(credentials: LoginRequest) {
    try {
      isLoading.value = true
      error.value = null

      const response = await authService.login(credentials)

      user.value = response.user
      tokens.value = response.tokens

      // Set token in API client
      apiClient.setAuthToken(response.tokens.accessToken)

      // Store in localStorage
      localStorage.setItem('auth_tokens', JSON.stringify(response.tokens))
      localStorage.setItem('auth_user', JSON.stringify(response.user))

      // Load user companies if authenticated
      if (response.user._id) {
        await loadUserCompanies()
      }

      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Login failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function register(userData: RegisterRequest) {
    try {
      isLoading.value = true
      error.value = null

      const response = await authService.register(userData)

      user.value = response.user
      tokens.value = response.tokens

      // Set token in API client
      apiClient.setAuthToken(response.tokens.accessToken)

      // Store in localStorage
      localStorage.setItem('auth_tokens', JSON.stringify(response.tokens))
      localStorage.setItem('auth_user', JSON.stringify(response.user))

      // Load user companies if authenticated
      if (response.user._id) {
        await loadUserCompanies()
      }

      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Registration failed'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function logout() {
    try {
      if (tokens.value?.accessToken) {
        await authService.logout(tokens.value.accessToken)
      }
    } catch (err) {
      console.warn('Logout request failed:', err)
    } finally {
      // Clear state regardless of API call success
      user.value = null
      tokens.value = null
      userCompanies.value = []
      error.value = null

      // Clear API client token
      apiClient.clearAuthToken()

      // Clear localStorage
      localStorage.removeItem('auth_tokens')
      localStorage.removeItem('auth_user')
    }
  }

  async function loadUserCompanies() {
    if (!user.value?._id) return

    try {
      const response = await authService.getUserCompanies(user.value._id)
      // The API returns the user with companies array
      if (response && (response as any).companies) {
        userCompanies.value = (response as any).companies
      }
    } catch (err) {
      console.warn('Failed to load user companies:', err)
    }
  }

  async function switchCompany(companyId: string) {
    if (!user.value?._id) {
      throw new Error('User not authenticated')
    }

    try {
      isSwitchingCompany.value = true
      error.value = null

      const response = await authService.switchCompany(user.value._id, companyId)

      // Update user with new currentCompanyId
      if (user.value) {
        user.value.currentCompanyId = companyId
        user.value.updatedAt = new Date().toISOString()

        // Update localStorage
        localStorage.setItem('auth_user', JSON.stringify(user.value))
      }

      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Failed to switch company'
      throw err
    } finally {
      isSwitchingCompany.value = false
    }
  }

  async function initializeAuth() {
    try {
      isInitializing.value = true
      loadingMessage.value = 'Initializing application...'

      const storedTokens = localStorage.getItem('auth_tokens')
      const storedUser = localStorage.getItem('auth_user')

      if (storedTokens && storedUser) {
        loadingMessage.value = 'Restoring session...'
        tokens.value = JSON.parse(storedTokens)
        user.value = JSON.parse(storedUser)

        // Set token in API client
        if (tokens.value?.accessToken) {
          apiClient.setAuthToken(tokens.value.accessToken)
        }

        // Load user companies
        if (user.value?._id) {
          await loadUserCompanies()
        }

        // Add a small delay for better UX
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    } catch (err) {
      console.warn('Failed to initialize auth from localStorage:', err)
      localStorage.removeItem('auth_tokens')
      localStorage.removeItem('auth_user')
    } finally {
      isInitializing.value = false
      loadingMessage.value = ''
    }
  }

  return {
    // State
    user,
    tokens,
    userCompanies,
    isLoading,
    error,
    isInitializing,
    loadingMessage,
    isSwitchingCompany,

    // Getters
    isAuthenticated,
    currentUser,
    hasCurrentCompany,
    currentCompany,
    hasMultipleCompanies,

    // Actions
    login,
    register,
    logout,
    loadUserCompanies,
    switchCompany,
    initializeAuth
  }
})
