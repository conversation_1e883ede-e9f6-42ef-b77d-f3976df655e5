<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCompanyStore } from '@/stores/company'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import CreateCompanyDialog from '@/components/CreateCompanyDialog.vue'

const authStore = useAuthStore()
const companyStore = useCompanyStore()
const router = useRouter()

// State
const isLoading = ref(true)
const showCreateDialog = ref(false)
const error = ref('')

// Load companies
onMounted(async () => {
  try {
    if (authStore.currentUser?._id) {
      await authStore.loadUserCompanies()
    }
  } catch (err) {
    error.value = 'Failed to load companies'
    console.error('Failed to load companies:', err)
  } finally {
    isLoading.value = false
  }
})

// Computed
const companies = computed(() => authStore.userCompanies || [])
const currentCompanyId = computed(() => authStore.currentUser?.currentCompanyId)

// Methods
const handleCreateCompany = () => {
  showCreateDialog.value = true
}

const handleCompanyCreated = (company: any) => {
  // Refresh the companies list
  if (authStore.currentUser?._id) {
    authStore.loadUserCompanies()
  }
}

const handleSwitchCompany = async (companyId: string) => {
  if (companyId === currentCompanyId.value) {
    return // Already selected
  }

  try {
    await authStore.switchCompany(companyId)
    // Navigate to dashboard to see the new company context
    router.push('/dashboard')
  } catch (err) {
    error.value = 'Failed to switch company'
    console.error('Failed to switch company:', err)
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getRoleColor = (role: string) => {
  switch (role) {
    case 'company_admin':
      return 'bg-blue-100 text-blue-800'
    case 'manager':
      return 'bg-green-100 text-green-800'
    case 'employee':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getRoleLabel = (role: string) => {
  return role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">Companies</h1>
        <p class="text-muted-foreground">
          Manage your companies and switch between different business entities.
        </p>
      </div>
      <Button @click="handleCreateCompany">
        <DuotoneIcon name="add" class="w-4 h-4 mr-2" />
        Create New Company
      </Button>
    </div>

    <!-- Error Alert -->
    <Alert v-if="error" variant="destructive">
      <DuotoneIcon name="error" class="w-4 h-4" />
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Loading State -->
    <div v-if="isLoading" class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <div v-for="i in 3" :key="i" class="animate-pulse">
        <Card>
          <CardHeader>
            <div class="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-4 bg-gray-200 rounded w-1/2"></div>
          </CardHeader>
          <CardContent>
            <div class="space-y-2">
              <div class="h-4 bg-gray-200 rounded w-full"></div>
              <div class="h-4 bg-gray-200 rounded w-2/3"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Companies Grid -->
    <div v-else-if="companies.length > 0" class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card
        v-for="company in companies"
        :key="company._id"
        :class="[
          'cursor-pointer transition-all hover:shadow-md',
          company._id === currentCompanyId
            ? 'ring-2 ring-blue-500 ring-offset-2'
            : 'hover:border-gray-300'
        ]"
        @click="handleSwitchCompany(company._id)"
      >
        <CardHeader>
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <CardTitle class="text-lg mb-1 flex items-center gap-2">
                <DuotoneIcon name="business" class="w-5 h-5 text-blue-600" />
                {{ company.name }}
                <Badge
                  v-if="company._id === currentCompanyId"
                  variant="secondary"
                  class="text-xs"
                >
                  Current
                </Badge>
              </CardTitle>
              <CardDescription class="text-sm">
                {{ company.email }}
              </CardDescription>
            </div>
          </div>
          <div class="flex items-center gap-2 mt-2">
            <Badge :class="getRoleColor(company.role)" class="text-xs px-2 py-1">
              {{ getRoleLabel(company.role) }}
            </Badge>
            <Badge variant="outline" class="text-xs">
              {{ company.businessType.replace('_', ' ') }}
            </Badge>
          </div>
        </CardHeader>

        <CardContent>
          <div class="space-y-2 text-sm text-muted-foreground">
            <div class="flex items-center gap-2">
              <DuotoneIcon name="location_on" class="w-4 h-4" />
              <span>{{ company.address?.province || 'No location' }}, {{ company.address?.country || 'Cambodia' }}</span>
            </div>
            <div class="flex items-center gap-2">
              <DuotoneIcon name="calendar_today" class="w-4 h-4" />
              <span>Founded {{ formatDate(company.foundedDate || company.createdAt) }}</span>
            </div>
            <div class="flex items-center gap-2">
              <DuotoneIcon name="people" class="w-4 h-4" />
              <span>Joined {{ formatDate(company.joinedAt || company.createdAt) }}</span>
            </div>
          </div>

          <div class="mt-4 pt-4 border-t">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div :class="[
                  'w-2 h-2 rounded-full',
                  company.isActive ? 'bg-green-500' : 'bg-red-500'
                ]"></div>
                <span class="text-sm text-muted-foreground">
                  {{ company.isActive ? 'Active' : 'Inactive' }}
                </span>
              </div>

              <Button
                v-if="company._id !== currentCompanyId"
                variant="outline"
                size="sm"
                @click.stop="handleSwitchCompany(company._id)"
              >
                Switch
              </Button>
              <Badge v-else variant="secondary" class="text-xs">
                Current
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-12">
      <DuotoneIcon name="business" class="w-16 h-16 mx-auto text-gray-400 mb-4" />
      <h3 class="text-lg font-semibold text-gray-900 mb-2">No Companies Found</h3>
      <p class="text-gray-500 mb-6">
        Get started by creating your first company to manage your business.
      </p>
      <Button @click="handleCreateCompany">
        <DuotoneIcon name="add" class="w-4 h-4 mr-2" />
        Create Your First Company
      </Button>
    </div>

    <!-- Quick Stats -->
    <div v-if="companies.length > 0" class="grid gap-4 md:grid-cols-3">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Total Companies</CardTitle>
          <DuotoneIcon name="business" class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ companies.length }}</div>
          <p class="text-xs text-muted-foreground">
            Companies you manage
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Admin Roles</CardTitle>
          <DuotoneIcon name="admin_panel_settings" class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">
            {{ companies.filter(c => c.role === 'company_admin').length }}
          </div>
          <p class="text-xs text-muted-foreground">
            Companies you own
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Active Status</CardTitle>
          <DuotoneIcon name="check_circle" class="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-green-600">
            {{ companies.filter(c => c.isActive).length }}
          </div>
          <p class="text-xs text-muted-foreground">
            Active companies
          </p>
        </CardContent>
      </Card>
    </div>

    <!-- Create Company Dialog -->
    <CreateCompanyDialog
      :open="showCreateDialog"
      @update:open="showCreateDialog = $event"
      @company-created="handleCompanyCreated"
    />
  </div>
</template>
