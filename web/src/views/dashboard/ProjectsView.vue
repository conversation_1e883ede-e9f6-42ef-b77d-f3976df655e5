<script setup lang="ts">
import { ref } from 'vue'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const projects = ref([
  {
    id: 1,
    name: 'E-commerce Dashboard',
    description: 'A modern dashboard for managing online store operations',
    status: 'In Progress',
    progress: 65,
    members: 4,
    tasks: 12,
    dueDate: '2024-03-15',
  },
  {
    id: 2,
    name: 'Mobile App Redesign',
    description: 'Redesigning the mobile app interface for better user experience',
    status: 'Planning',
    progress: 25,
    members: 3,
    tasks: 8,
    dueDate: '2024-03-30',
  },
  {
    id: 3,
    name: 'API Integration',
    description: 'Integrating third-party APIs for enhanced functionality',
    status: 'Completed',
    progress: 100,
    members: 2,
    tasks: 6,
    dueDate: '2024-02-28',
  },
])

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'in progress':
      return 'bg-blue-100 text-blue-800'
    case 'planning':
      return 'bg-yellow-100 text-yellow-800'
    case 'completed':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
</script>

<template>
  <div class="py-6">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-semibold text-gray-900">Projects</h1>
        <Button>
          <DuotoneIcon name="plus" size="sm" class="mr-2" />
          New Project
        </Button>
      </div>
    </div>

    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
      <div class="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
        <Card v-for="project in projects" :key="project.id">
          <CardHeader>
            <div class="flex items-center justify-between">
              <CardTitle class="text-lg">{{ project.name }}</CardTitle>
              <Badge :class="getStatusColor(project.status)">
                {{ project.status }}
              </Badge>
            </div>
            <CardDescription>{{ project.description }}</CardDescription>
          </CardHeader>
          <CardContent>
            <!-- Progress Bar -->
            <div class="mb-4">
              <div class="flex items-center justify-between text-sm mb-1">
                <span class="text-muted-foreground">Progress</span>
                <span class="font-medium">{{ project.progress }}%</span>
              </div>
              <div class="h-2 bg-gray-200 rounded-full">
                <div
                  class="h-2 bg-[#1C1C1C] rounded-full"
                  :style="{ width: `${project.progress}%` }"
                ></div>
              </div>
            </div>

            <!-- Project Stats -->
            <div class="grid grid-cols-3 gap-4 text-sm">
              <div>
                <div class="flex items-center text-muted-foreground mb-1">
                  <DuotoneIcon name="users" size="sm" class="mr-1" />
                  Members
                </div>
                <span class="font-medium">{{ project.members }}</span>
              </div>
              <div>
                <div class="flex items-center text-muted-foreground mb-1">
                  <DuotoneIcon name="check-square" size="sm" class="mr-1" />
                  Tasks
                </div>
                <span class="font-medium">{{ project.tasks }}</span>
              </div>
              <div>
                <div class="flex items-center text-muted-foreground mb-1">
                  <DuotoneIcon name="calendar" size="sm" class="mr-1" />
                  Due Date
                </div>
                <span class="font-medium">{{
                  new Date(project.dueDate).toLocaleDateString()
                }}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>
