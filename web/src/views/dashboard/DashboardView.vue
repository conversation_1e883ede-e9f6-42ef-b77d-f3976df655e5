<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useCompanyStore } from '@/stores/company'
import { useRouter } from 'vue-router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import CreateCompanyDialog from '@/components/CreateCompanyDialog.vue'

const authStore = useAuthStore()
const companyStore = useCompanyStore()
const router = useRouter()

// Dialog state
const showCreateDialog = ref(false)

// Load company data
const isLoading = ref(true)
const subscriptions = ref<any[]>([])
const billingInfo = ref<any>(null)

onMounted(async () => {
  if (authStore.currentUser?.currentCompanyId) {
    try {
      const [subs, billing] = await Promise.all([
        companyStore.fetchSubscriptions(authStore.currentUser.currentCompanyId),
        companyStore.fetchBillingInfo(authStore.currentUser.currentCompanyId)
      ])
      subscriptions.value = subs
      billingInfo.value = billing
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      isLoading.value = false
    }
  } else {
    isLoading.value = false
  }
})

// Computed values
const activeSubscriptions = computed(() =>
  subscriptions.value.filter(sub => sub.active)
)

const trialSubscriptions = computed(() =>
  subscriptions.value.filter(sub => sub.active && sub.isTrialMode)
)

const totalMonthlyAmount = computed(() =>
  billingInfo.value?.totalMonthlyAmount || 0
)

const quickActions = [
  {
    title: 'View Modules',
    description: 'Manage your subscriptions and start trials',
    icon: 'apps',
    color: 'blue',
    path: '/dashboard/modules'
  },
  {
    title: 'Company Settings',
    description: 'Update company information and preferences',
    icon: 'settings',
    color: 'gray',
    path: '/dashboard/settings'
  },
  {
    title: 'Invite Team Members',
    description: 'Add users to your company',
    icon: 'people',
    color: 'green',
    path: '/dashboard/settings?tab=users'
  }
]

const handleCreateCompany = () => {
  showCreateDialog.value = true
}

const handleCompanyCreated = (company) => {
  // Refresh the current page to show new company data
  router.go(0)
}
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p class="text-muted-foreground">
          Welcome back! Here's an overview of your business.
        </p>
      </div>
      <Button @click="handleCreateCompany" variant="outline">
        <DuotoneIcon name="add" class="w-4 h-4 mr-2" />
        Create Company
      </Button>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <div v-for="i in 4" :key="i" class="animate-pulse">
        <Card>
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <div class="h-4 bg-gray-200 rounded w-24"></div>
            <div class="h-4 w-4 bg-gray-200 rounded"></div>
          </CardHeader>
          <CardContent>
            <div class="h-8 bg-gray-200 rounded w-16 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-full"></div>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Stats Cards -->
    <div v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Active Modules</CardTitle>
          <DuotoneIcon name="apps" class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ activeSubscriptions.length }}</div>
          <p class="text-xs text-muted-foreground">
            {{ trialSubscriptions.length }} in trial mode
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Monthly Cost</CardTitle>
          <DuotoneIcon name="trending_up" class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">${{ totalMonthlyAmount }}</div>
          <p class="text-xs text-muted-foreground">
            Per month billing
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Companies</CardTitle>
          <DuotoneIcon name="business" class="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ authStore.userCompanies.length }}</div>
          <p class="text-xs text-muted-foreground">
            Companies you manage
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle class="text-sm font-medium">Status</CardTitle>
          <DuotoneIcon name="check_circle" class="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold text-green-600">Active</div>
          <p class="text-xs text-muted-foreground">
            All systems operational
          </p>
        </CardContent>
      </Card>
    </div>

    <!-- Quick Actions -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <Card v-for="action in quickActions" :key="action.title" class="cursor-pointer hover:shadow-md transition-shadow">
        <CardHeader>
          <div class="flex items-center space-x-2">
            <div :class="[
              'p-2 rounded-lg',
              action.color === 'blue' ? 'bg-blue-100 text-blue-600' :
              action.color === 'green' ? 'bg-green-100 text-green-600' :
              'bg-gray-100 text-gray-600'
            ]">
              <DuotoneIcon :name="action.icon" class="w-5 h-5" />
            </div>
            <CardTitle class="text-lg">{{ action.title }}</CardTitle>
          </div>
          <CardDescription>{{ action.description }}</CardDescription>
        </CardHeader>
        <CardContent>
          <Button variant="outline" class="w-full" @click="router.push(action.path)">
            <DuotoneIcon name="arrow_forward" class="w-4 h-4 mr-2" />
            Get Started
          </Button>
        </CardContent>
      </Card>

      <!-- Create Company Card -->
      <Card class="cursor-pointer hover:shadow-md transition-shadow border-dashed border-2">
        <CardHeader>
          <div class="flex items-center space-x-2">
            <div class="p-2 rounded-lg bg-purple-100 text-purple-600">
              <DuotoneIcon name="add" class="w-5 h-5" />
            </div>
            <CardTitle class="text-lg">Create New Company</CardTitle>
          </div>
          <CardDescription>Set up a new business entity to manage</CardDescription>
        </CardHeader>
        <CardContent>
          <Button variant="outline" class="w-full" @click="handleCreateCompany">
            <DuotoneIcon name="business" class="w-4 h-4 mr-2" />
            Create Company
          </Button>
        </CardContent>
      </Card>
    </div>

    <!-- Active Subscriptions -->
    <div v-if="activeSubscriptions.length > 0">
      <h2 class="text-xl font-semibold mb-4">Active Modules</h2>
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card v-for="subscription in activeSubscriptions" :key="subscription._id">
          <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-base">{{ subscription.module }}</CardTitle>
            <Badge v-if="subscription.isTrialMode" variant="secondary">Trial</Badge>
            <Badge v-else variant="default">Active</Badge>
          </CardHeader>
          <CardContent>
            <div class="text-lg font-semibold">${{ subscription.price }}/month</div>
            <p class="text-sm text-muted-foreground">
              {{ subscription.posType ? `POS: ${subscription.posType}` : 'Standard pricing' }}
            </p>
            <p class="text-xs text-muted-foreground mt-1">
              Next billing: {{ new Date(subscription.nextBillingDate).toLocaleDateString() }}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>

    <!-- Create Company Dialog -->
    <CreateCompanyDialog
      :open="showCreateDialog"
      @update:open="showCreateDialog = $event"
      @company-created="handleCompanyCreated"
    />
  </div>
</template>
