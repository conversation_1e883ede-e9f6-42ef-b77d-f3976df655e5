<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { categoryService } from '@/services/category.service'
import CategoryTree from '@/components/CategoryTree.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem
} from '@/components/ui/dropdown-menu'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import {
  ArrowLeft,
  FolderTree,
  Package,
  Settings,
  Download,
  Upload,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Layers3,
  TrendingUp,
  Eye,
  Grid3x3,
  List,
  SortAsc,
  SortDesc,
  RefreshCw,
  Archive,
  FileText,
  PieChart,
  BarChart3,
  Users,
  Activity,
  Trash2,
  Edit
} from 'lucide-vue-next'

const router = useRouter()
const authStore = useAuthStore()

// Load saved view mode preference or default to 'tree'
const getSavedViewMode = (): 'tree' | 'grid' | 'list' => {
  try {
    const saved = localStorage.getItem('categoryViewMode')
    if (saved && ['tree', 'grid', 'list'].includes(saved)) {
      return saved as 'tree' | 'grid' | 'list'
    }
  } catch (err) {
    console.error('Failed to load view mode preference:', err)
  }
  return 'tree'
}

// Load saved sidebar state or default to true
const getSavedSidebarState = (): boolean => {
  try {
    const saved = localStorage.getItem('categorySidebarOpen')
    if (saved !== null) {
      return JSON.parse(saved)
    }
  } catch (err) {
    console.error('Failed to load sidebar state preference:', err)
  }
  return true
}

// View preferences
const viewMode = ref<'tree' | 'grid' | 'list'>(getSavedViewMode())
const sidebarOpen = ref(getSavedSidebarState())
const showFilters = ref(false)

// Save preferences when they change
watch(viewMode, (newMode) => {
  try {
    localStorage.setItem('categoryViewMode', newMode)
  } catch (err) {
    console.error('Failed to save view mode preference:', err)
  }
})

watch(sidebarOpen, (newState) => {
  try {
    localStorage.setItem('categorySidebarOpen', JSON.stringify(newState))
  } catch (err) {
    console.error('Failed to save sidebar state preference:', err)
  }
})

// Search and filters
const searchQuery = ref('')
const sortBy = ref<'name' | 'created' | 'products' | 'level'>('name')
const sortOrder = ref<'asc' | 'desc'>('asc')
const selectedLevels = ref<number[]>([])
const statusFilter = ref<'all' | 'active' | 'empty'>('all')

// Stats for the category overview
const categoryStats = ref({
  totalCategories: 0,
  totalProducts: 0,
  maxDepth: 0,
  categoriesWithProducts: 0,
  averageProductsPerCategory: 0,
  emptyCategories: 0,
  recentlyCreated: 0
})

const isLoading = ref(true)
const isRefreshing = ref(false)
const error = ref('')

// Dialog states
const showCreateDialog = ref(false)
const showImportDialog = ref(false)
const showExportDialog = ref(false)
const showBulkActionsDialog = ref(false)

// Form states
const createForm = ref({
  name: '',
  description: '',
  parentId: '',
  icon: '',
  color: '#3B82F6'
})

const companyId = computed(() => authStore.currentUser?.currentCompanyId || '')

// Computed properties
const filteredStats = computed(() => {
  const stats = categoryStats.value
  const utilizationRate = stats.totalCategories > 0
    ? Math.round((stats.categoriesWithProducts / stats.totalCategories) * 100)
    : 0

  const averageProducts = stats.totalCategories > 0
    ? Math.round(stats.totalProducts / stats.totalCategories * 10) / 10
    : 0

  return {
    ...stats,
    utilizationRate,
    averageProducts
  }
})

const availableLevels = computed(() => {
  return Array.from({ length: categoryStats.value.maxDepth }, (_, i) => i + 1)
})

// Additional state for different views
const categoryData = ref<any>(null)
const isRefreshingData = ref(false)

// Load category data for all views
const loadCategoryData = async () => {
  if (!companyId.value) return

  try {
    isRefreshingData.value = true
    error.value = ''

    // Load real category tree data
    const categoryTree = await categoryService.getCategoryTree(companyId.value)
    categoryData.value = categoryTree

    // Calculate enhanced stats from the tree
    const flatCategories = flattenCategories(categoryTree.categories)
    const totalProducts = flatCategories.reduce((sum, cat) => sum + (cat.productCount || 0), 0)
    const categoriesWithProducts = flatCategories.filter(cat => (cat.productCount || 0) > 0).length
    const emptyCategories = flatCategories.filter(cat => (cat.productCount || 0) === 0).length

    // Mock recent creation data (in real app, this would come from API)
    const recentlyCreated = Math.floor(Math.random() * 5) + 1

    categoryStats.value = {
      totalCategories: categoryTree.totalCount,
      totalProducts,
      maxDepth: categoryTree.maxDepth,
      categoriesWithProducts,
      averageProductsPerCategory: categoryTree.totalCount > 0 ? totalProducts / categoryTree.totalCount : 0,
      emptyCategories,
      recentlyCreated
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load category statistics'
    console.error('Failed to load category stats:', err)
  } finally {
    isLoading.value = false
    isRefreshingData.value = false
  }
}

// Category management functions
const editCategory = async (category: any) => {
  // Implementation for editing category
  console.log('Edit category:', category)
}

const deleteCategory = async (category: any) => {
  if (!companyId.value) return

  try {
    await categoryService.deleteCategory(companyId.value, category._id)
    await loadCategoryData() // Reload data
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to delete category'
  }
}

// Get flattened categories for grid and list views
const flatCategories = computed(() => {
  if (!categoryData.value) return []
  return flattenCategories(categoryData.value.categories)
})

// Filter categories based on search and filters
const filteredCategories = computed(() => {
  let categories = flatCategories.value

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    categories = categories.filter(cat =>
      cat.name.toLowerCase().includes(query) ||
      cat.description?.toLowerCase().includes(query)
    )
  }

  // Apply status filter
  if (statusFilter.value !== 'all') {
    if (statusFilter.value === 'active') {
      categories = categories.filter(cat => (cat.productCount || 0) > 0)
    } else if (statusFilter.value === 'empty') {
      categories = categories.filter(cat => (cat.productCount || 0) === 0)
    }
  }

  // Apply level filter
  if (selectedLevels.value.length > 0) {
    categories = categories.filter(cat => selectedLevels.value.includes(cat.level + 1))
  }

  // Apply sorting
  categories.sort((a, b) => {
    let aValue, bValue

    switch (sortBy.value) {
      case 'name':
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case 'products':
        aValue = a.productCount || 0
        bValue = b.productCount || 0
        break
      case 'level':
        aValue = a.level
        bValue = b.level
        break
      case 'created':
        aValue = new Date(a.createdAt).getTime()
        bValue = new Date(b.createdAt).getTime()
        break
      default:
        return 0
    }

    if (aValue < bValue) return sortOrder.value === 'asc' ? -1 : 1
    if (aValue > bValue) return sortOrder.value === 'asc' ? 1 : -1
    return 0
  })

  return categories
})

// Update the loadCategoryStats function to use loadCategoryData
const loadCategoryStats = async () => {
  await loadCategoryData()
}

// Navigation functions
const goBack = () => {
  router.back()
}

const goToProducts = () => {
  router.push('/dashboard/products')
}

const goToSettings = () => {
  router.push('/dashboard/settings')
}

// Action functions
const refreshData = async () => {
  isRefreshing.value = true
  await loadCategoryData()
  isRefreshing.value = false
}

const exportCategories = () => {
  showExportDialog.value = true
}

const importCategories = () => {
  showImportDialog.value = true
}

const handleBulkActions = () => {
  showBulkActionsDialog.value = true
}

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const clearFilters = () => {
  searchQuery.value = ''
  selectedLevels.value = []
  statusFilter.value = 'all'
  sortBy.value = 'name'
  sortOrder.value = 'asc'
}

// Category management
const handleCreateCategory = async () => {
  if (!companyId.value || !createForm.value.name.trim()) return

  try {
    await categoryService.createCategory(companyId.value, {
      name: createForm.value.name.trim(),
      description: createForm.value.description.trim() || undefined,
      parentId: createForm.value.parentId || undefined,
      icon: createForm.value.icon || undefined,
      color: createForm.value.color
    })

    // Reset form and reload
    createForm.value = {
      name: '',
      description: '',
      parentId: '',
      icon: '',
      color: '#3B82F6'
    }
    showCreateDialog.value = false
    await loadCategoryData()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to create category'
  }
}

// Helper function to flatten the category tree
const flattenCategories = (categories: any[]): any[] => {
  const result: any[] = []

  for (const category of categories) {
    result.push(category)
    if (category.children && category.children.length > 0) {
      result.push(...flattenCategories(category.children))
    }
  }

  return result
}

// Watch for filter changes
watch([searchQuery, sortBy, sortOrder, selectedLevels, statusFilter], () => {
  // In a real implementation, this would filter the category tree
  console.log('Filters changed:', {
    searchQuery: searchQuery.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value,
    selectedLevels: selectedLevels.value,
    statusFilter: statusFilter.value
  })
})

onMounted(() => {
  loadCategoryData()
})
</script>

<template>
  <div class="min-h-[calc(100vh-100px)] bg-background">
    <!-- Main Layout -->
    <div class="h-[calc(100vh-100px)] flex">
      <!-- Sidebar -->
      <div
        :class="[
          'transition-all duration-300 border-r bg-muted/10',
          sidebarOpen ? 'w-80' : 'w-0'
        ]"
        class="overflow-hidden"
      >
        <div class="p-6 space-y-6 overflow-y-auto">
          <!-- Sidebar Header -->
          <div class="space-y-2">
            <h3 class="font-semibold text-lg">Category Management</h3>
            <p class="text-sm text-muted-foreground">
              Organize and manage your product categories
            </p>
          </div>

          <Separator />

          <!-- Quick Stats -->
          <div class="space-y-4">
            <h4 class="font-medium text-sm text-muted-foreground uppercase tracking-wide">Overview</h4>
            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 rounded-lg bg-background border">
                <div class="flex items-center gap-2">
                  <FolderTree class="w-4 h-4 text-blue-600" />
                  <span class="text-sm">Categories</span>
                </div>
                <Badge variant="secondary">{{ filteredStats.totalCategories }}</Badge>
              </div>

              <div class="flex items-center justify-between p-3 rounded-lg bg-background border">
                <div class="flex items-center gap-2">
                  <Package class="w-4 h-4 text-green-600" />
                  <span class="text-sm">Products</span>
                </div>
                <Badge variant="secondary">{{ filteredStats.totalProducts }}</Badge>
              </div>

              <div class="p-3 rounded-lg bg-background border space-y-2">
                <div class="flex items-center justify-between">
                  <span class="text-sm">Utilization</span>
                  <span class="text-sm font-medium">{{ filteredStats.utilizationRate }}%</span>
                </div>
                <Progress :value="filteredStats.utilizationRate" class="h-2" />
              </div>
            </div>
          </div>

                     <Separator />

           <!-- Search and Filters (Hidden in Tree Mode) -->
           <div v-if="viewMode !== 'tree'" class="space-y-4">
             <h4 class="font-medium text-sm text-muted-foreground uppercase tracking-wide">Filters</h4>

             <!-- Search -->
             <div class="relative">
               <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
               <Input
                 v-model="searchQuery"
                 placeholder="Search categories..."
                 class="pl-10"
               />
             </div>

             <!-- Status Filter -->
             <div class="space-y-2">
               <Label class="text-xs">Status</Label>
               <Select v-model="statusFilter">
                 <SelectTrigger>
                   <SelectValue />
                 </SelectTrigger>
                 <SelectContent>
                   <SelectItem value="all">All Categories</SelectItem>
                   <SelectItem value="active">With Products</SelectItem>
                   <SelectItem value="empty">Empty Categories</SelectItem>
                 </SelectContent>
               </Select>
             </div>

             <!-- Level Filter -->
             <div class="space-y-2" v-if="availableLevels.length > 0">
               <Label class="text-xs">Levels</Label>
               <div class="space-y-1">
                 <div v-for="level in availableLevels" :key="level" class="flex items-center space-x-2">
                   <input
                     type="checkbox"
                     :id="`level-${level}`"
                     :value="level"
                     v-model="selectedLevels"
                     class="rounded border-gray-300"
                   />
                   <Label :for="`level-${level}`" class="text-sm">Level {{ level }}</Label>
                 </div>
               </div>
             </div>

             <!-- Sort Options -->
             <div class="space-y-2">
               <Label class="text-xs">Sort by</Label>
               <div class="flex gap-2">
                 <Select v-model="sortBy">
                   <SelectTrigger class="flex-1">
                     <SelectValue />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value="name">Name</SelectItem>
                     <SelectItem value="created">Created</SelectItem>
                     <SelectItem value="products">Products</SelectItem>
                     <SelectItem value="level">Level</SelectItem>
                   </SelectContent>
                 </Select>

                 <Button
                   variant="outline"
                   size="icon"
                   @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
                 >
                   <SortAsc v-if="sortOrder === 'asc'" class="w-4 h-4" />
                   <SortDesc v-else class="w-4 h-4" />
                 </Button>
               </div>
             </div>

             <Button variant="outline" size="sm" @click="clearFilters" class="w-full">
               Clear Filters
             </Button>
           </div>

           <!-- Tree Mode Info -->
           <div v-else class="space-y-4">
             <h4 class="font-medium text-sm text-muted-foreground uppercase tracking-wide">Tree View</h4>
             <div class="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
               <div class="flex items-start gap-2">
                 <FolderTree class="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                 <div class="text-xs text-blue-700 dark:text-blue-300">
                   <p class="font-medium mb-1">Hierarchical View</p>
                   <p>Browse categories in their natural tree structure. Use the CategoryTree component's built-in search and management features.</p>
                 </div>
               </div>
             </div>
           </div>

           <!-- <Separator /> -->


        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Header -->
        <div class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <Button variant="ghost" size="icon" @click="toggleSidebar">
                  <Grid3x3 class="w-4 h-4" />
                </Button>

                <div class="flex items-center gap-2">
                  <Button variant="ghost" size="sm" @click="goBack">
                    <ArrowLeft class="w-4 h-4 mr-2" />
                    Back
                  </Button>
                  <Separator orientation="vertical" class="h-4" />
                  <nav class="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>Dashboard</span>
                    <span>/</span>
                    <span>POS</span>
                    <span>/</span>
                    <span class="text-foreground font-medium">Categories</span>
                  </nav>
                </div>
              </div>

              <div class="flex items-center gap-3">
                <!-- View Mode Toggle -->
                <div class="flex items-center border rounded-lg p-1 space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    :class="viewMode === 'tree' ? 'bg-muted' : ''"
                    @click="viewMode = 'tree'"
                  >
                    <FolderTree class="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    :class="viewMode === 'grid' ? 'bg-muted' : ''"
                    @click="viewMode = 'grid'"
                  >
                    <Grid3x3 class="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    :class="viewMode === 'list' ? 'bg-muted' : ''"
                    @click="viewMode = 'list'"
                  >
                    <List class="w-4 h-4" />
                  </Button>
                </div>

                <Button variant="outline" @click="refreshData" :disabled="isRefreshing">
                  <RefreshCw :class="['w-4 h-4 mr-2', isRefreshing && 'animate-spin']" />
                  Refresh
                </Button>

                <Button @click="goToProducts">
                  <Package class="w-4 h-4 mr-2" />
                  View Products
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="outline" size="icon">
                      <MoreHorizontal class="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem @click="goToSettings">
                      <Settings class="w-4 h-4 mr-2" />
                      Settings
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem @click="exportCategories">
                      <Download class="w-4 h-4 mr-2" />
                      Export Data
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="importCategories">
                      <Upload class="w-4 h-4 mr-2" />
                      Import Data
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="handleBulkActions">
                      <Archive class="w-4 h-4 mr-2" />
                      Bulk Actions
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Stats Cards -->
        <div class="p-6 border-b bg-muted/20">
          <div class="xcard grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            <Card class="relative overflow-hidden">
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Total Categories</p>
                    <p class="text-2xl font-bold">{{ filteredStats.totalCategories }}</p>
                  </div>
                  <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FolderTree class="w-4 h-4 text-blue-600" />
                  </div>
                </div>
                <div class="flex items-center gap-1 mt-2">
                  <TrendingUp class="w-3 h-3 text-green-600" />
                  <span class="text-xs text-green-600">+{{ filteredStats.recentlyCreated }} this week</span>
                </div>
              </CardContent>
            </Card>

            <Card class="relative overflow-hidden">
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Total Products</p>
                    <p class="text-2xl font-bold">{{ filteredStats.totalProducts }}</p>
                  </div>
                  <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <Package class="w-4 h-4 text-green-600" />
                  </div>
                </div>
                <div class="flex items-center gap-1 mt-2">
                  <Activity class="w-3 h-3 text-blue-600" />
                  <span class="text-xs text-muted-foreground">{{ filteredStats.averageProducts }} avg per category</span>
                </div>
              </CardContent>
            </Card>

            <Card class="relative overflow-hidden">
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Category Depth</p>
                    <p class="text-2xl font-bold">{{ filteredStats.maxDepth }}</p>
                  </div>
                  <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Layers3 class="w-4 h-4 text-purple-600" />
                  </div>
                </div>
                <div class="flex items-center gap-1 mt-2">
                  <span class="text-xs text-muted-foreground">Maximum levels deep</span>
                </div>
              </CardContent>
            </Card>

            <Card class="relative overflow-hidden">
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Active Categories</p>
                    <p class="text-2xl font-bold">{{ filteredStats.categoriesWithProducts }}</p>
                  </div>
                  <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                    <Eye class="w-4 h-4 text-orange-600" />
                  </div>
                </div>
                <div class="flex items-center gap-1 mt-2">
                  <span class="text-xs text-muted-foreground">With products assigned</span>
                </div>
              </CardContent>
            </Card>

            <Card class="relative overflow-hidden">
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Utilization Rate</p>
                    <p class="text-2xl font-bold">{{ filteredStats.utilizationRate }}%</p>
                  </div>
                  <div class="w-8 h-8 bg-teal-100 rounded-lg flex items-center justify-center">
                    <PieChart class="w-4 h-4 text-teal-600" />
                  </div>
                </div>
                <Progress :value="filteredStats.utilizationRate" class="h-1 mt-2" />
              </CardContent>
            </Card>

            <Card class="relative overflow-hidden">
              <CardContent class="p-4">
                <div class="flex items-center justify-between">
                  <div>
                    <p class="text-sm text-muted-foreground">Empty Categories</p>
                    <p class="text-2xl font-bold">{{ filteredStats.emptyCategories }}</p>
                  </div>
                  <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                    <Archive class="w-4 h-4 text-gray-600" />
                  </div>
                </div>
                <div class="flex items-center gap-1 mt-2">
                  <span class="text-xs text-muted-foreground">Need attention</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 overflow-auto p-6">
          <!-- Loading State -->
          <div v-if="isLoading" class="flex justify-center items-center h-64">
            <div class="flex items-center gap-3">
              <div class="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
              <span class="text-muted-foreground">Loading categories...</span>
            </div>
          </div>

          <!-- Error State -->
          <Card v-else-if="error" class="border-red-200 bg-red-50">
            <CardContent class="pt-6">
              <div class="flex items-center gap-2 text-red-600 justify-between">
                <span>{{ error }}</span>
                <Button variant="outline" size="sm" @click="loadCategoryStats">
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>

                                           <!-- Dynamic View Content Based on View Mode -->
             <div v-else class="view-container">
               <!-- Tree View -->
               <div v-if="viewMode === 'tree'">
                 <CategoryTree />
               </div>

               <!-- Grid View -->
               <div v-else-if="viewMode === 'grid'" class="space-y-6">
               <div class="flex items-center justify-between">
                 <div>
                   <h2 class="text-xl font-bold tracking-tight">Categories Grid</h2>
                   <p class="text-muted-foreground">
                     Browse categories in a visual grid layout
                   </p>
                 </div>
                 <Button @click="showCreateDialog = true">
                   <Plus class="w-4 h-4 mr-2" />
                   Add Category
                 </Button>
               </div>

               <!-- Grid Layout -->
               <div class="xcard grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                 <Card
                   v-for="category in filteredCategories"
                   :key="category._id"
                   class="hover:shadow-md transition-shadow cursor-pointer group"
                 >
                   <CardContent class="p-4">
                     <div class="flex items-start justify-between mb-3">
                       <div class="flex items-center gap-2">
                         <div
                           class="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
                           :style="{ backgroundColor: category.color || '#3B82F6' }"
                         >
                           {{ category.icon || category.name.charAt(0).toUpperCase() }}
                         </div>
                         <div class="flex-1 min-w-0">
                           <h3 class="font-medium text-sm truncate">{{ category.name }}</h3>
                           <p class="text-xs text-muted-foreground">Level {{ category.level + 1 }}</p>
                         </div>
                       </div>
                       <DropdownMenu>
                         <DropdownMenuTrigger as-child>
                           <Button variant="ghost" size="icon" class="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity">
                             <MoreHorizontal class="w-4 h-4" />
                           </Button>
                         </DropdownMenuTrigger>
                         <DropdownMenuContent align="end">
                           <DropdownMenuItem @click="editCategory(category)">
                             <Edit class="w-4 h-4 mr-2" />
                             Edit
                           </DropdownMenuItem>
                           <DropdownMenuSeparator />
                           <DropdownMenuItem @click="deleteCategory(category)" class="text-red-600">
                             <Trash2 class="w-4 h-4 mr-2" />
                             Delete
                           </DropdownMenuItem>
                         </DropdownMenuContent>
                       </DropdownMenu>
                     </div>

                     <div class="space-y-2">
                       <p v-if="category.description" class="text-xs text-muted-foreground line-clamp-2">
                         {{ category.description }}
                       </p>

                       <div class="flex items-center justify-between">
                         <div class="flex items-center gap-2">
                           <Package class="w-3 h-3 text-muted-foreground" />
                           <span class="text-xs text-muted-foreground">
                             {{ category.productCount || 0 }} products
                           </span>
                         </div>
                         <Badge
                           :variant="(category.productCount || 0) > 0 ? 'default' : 'secondary'"
                           class="text-xs"
                         >
                           {{ (category.productCount || 0) > 0 ? 'Active' : 'Empty' }}
                         </Badge>
                       </div>

                       <div v-if="category.path && category.path.length > 1" class="flex items-center gap-1">
                         <span class="text-xs text-muted-foreground">Path:</span>
                         <span class="text-xs text-muted-foreground truncate">
                           {{ category.path.slice(0, -1).join(' > ') }}
                         </span>
                       </div>
                     </div>
                   </CardContent>
                 </Card>

                 <!-- Add Category Card -->
                 <Card
                   class="border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors cursor-pointer"
                   @click="showCreateDialog = true"
                 >
                   <CardContent class="p-4 flex flex-col items-center justify-center h-full min-h-[140px]">
                     <Plus class="w-8 h-8 text-muted-foreground mb-2" />
                     <span class="text-sm text-muted-foreground text-center">Add New Category</span>
                   </CardContent>
                 </Card>
               </div>

               <!-- Empty State for Grid -->
               <div v-if="filteredCategories.length === 0" class="text-center py-12">
                 <FolderTree class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                 <h3 class="text-lg font-medium mb-2">No categories found</h3>
                 <p class="text-muted-foreground mb-4">
                   {{ searchQuery || selectedLevels.length || statusFilter !== 'all' ? 'Try adjusting your filters' : 'Create your first category to get started' }}
                 </p>
                 <Button @click="searchQuery || selectedLevels.length || statusFilter !== 'all' ? clearFilters() : (showCreateDialog = true)">
                   {{ searchQuery || selectedLevels.length || statusFilter !== 'all' ? 'Clear Filters' : 'Create Category' }}
                 </Button>
               </div>
             </div>

             <!-- List View -->
             <div v-else-if="viewMode === 'list'" class="space-y-6 xcard">
               <div class="flex items-center justify-between">
                 <div>
                   <h2 class="text-xl font-bold tracking-tight">Categories List</h2>
                   <p class="text-muted-foreground">
                     Detailed view of all categories with comprehensive information
                   </p>
                 </div>
                 <Button @click="showCreateDialog = true">
                   <Plus class="w-4 h-4 mr-2" />
                   Add Category
                 </Button>
               </div>

               <!-- Table Layout -->
               <Card>
                 <CardContent class="p-0">
                   <div class="overflow-x-auto">
                     <table class="w-full">
                       <thead class="border-b bg-muted/50">
                         <tr>
                           <th class="text-left p-4 font-medium">Category</th>
                           <th class="text-left p-4 font-medium">Level</th>
                           <th class="text-left p-4 font-medium">Products</th>
                           <th class="text-left p-4 font-medium">Status</th>
                           <th class="text-left p-4 font-medium">Path</th>
                           <th class="text-left p-4 font-medium">Created</th>
                           <th class="text-right p-4 font-medium">Actions</th>
                         </tr>
                       </thead>
                       <tbody>
                         <tr
                           v-for="category in filteredCategories"
                           :key="category._id"
                           class="border-b hover:bg-muted/50 transition-colors"
                         >
                           <td class="p-4">
                             <div class="flex items-center gap-3">
                               <div
                                 class="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium flex-shrink-0"
                                 :style="{ backgroundColor: category.color || '#3B82F6' }"
                               >
                                 {{ category.icon || category.name.charAt(0).toUpperCase() }}
                               </div>
                               <div class="min-w-0">
                                 <p class="font-medium text-sm">{{ category.name }}</p>
                                 <p v-if="category.description" class="text-xs text-muted-foreground truncate">
                                   {{ category.description }}
                                 </p>
                               </div>
                             </div>
                           </td>
                           <td class="p-4">
                             <Badge variant="outline" class="text-xs">
                               Level {{ category.level + 1 }}
                             </Badge>
                           </td>
                           <td class="p-4">
                             <div class="flex items-center gap-2">
                               <Package class="w-4 h-4 text-muted-foreground" />
                               <span class="text-sm">{{ category.productCount || 0 }}</span>
                             </div>
                           </td>
                           <td class="p-4">
                             <Badge
                               :variant="(category.productCount || 0) > 0 ? 'default' : 'secondary'"
                               class="text-xs"
                             >
                               {{ (category.productCount || 0) > 0 ? 'Active' : 'Empty' }}
                             </Badge>
                           </td>
                           <td class="p-4">
                             <span class="text-sm text-muted-foreground">
                               {{ category.path?.join(' > ') || category.name }}
                             </span>
                           </td>
                           <td class="p-4">
                             <span class="text-sm text-muted-foreground">
                               {{ new Date(category.createdAt).toLocaleDateString() }}
                             </span>
                           </td>
                           <td class="p-4 text-right">
                             <DropdownMenu>
                               <DropdownMenuTrigger as-child>
                                 <Button variant="ghost" size="icon" class="h-8 w-8">
                                   <MoreHorizontal class="w-4 h-4" />
                                 </Button>
                               </DropdownMenuTrigger>
                               <DropdownMenuContent align="end">
                                 <DropdownMenuItem @click="editCategory(category)">
                                   <Edit class="w-4 h-4 mr-2" />
                                   Edit
                                 </DropdownMenuItem>
                                 <DropdownMenuSeparator />
                                 <DropdownMenuItem @click="deleteCategory(category)" class="text-red-600">
                                   <Trash2 class="w-4 h-4 mr-2" />
                                   Delete
                                 </DropdownMenuItem>
                               </DropdownMenuContent>
                             </DropdownMenu>
                           </td>
                         </tr>
                       </tbody>
                     </table>
                   </div>
                 </CardContent>
               </Card>

               <!-- Empty State for List -->
               <div v-if="filteredCategories.length === 0" class="text-center py-12">
                 <List class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                 <h3 class="text-lg font-medium mb-2">No categories found</h3>
                 <p class="text-muted-foreground mb-4">
                   {{ searchQuery || selectedLevels.length || statusFilter !== 'all' ? 'Try adjusting your filters' : 'Create your first category to get started' }}
                 </p>
                 <Button @click="searchQuery || selectedLevels.length || statusFilter !== 'all' ? clearFilters() : (showCreateDialog = true)">
                   {{ searchQuery || selectedLevels.length || statusFilter !== 'all' ? 'Clear Filters' : 'Create Category' }}
                 </Button>
                                </div>
               </div>
             </div>
         </div>
       </div>
     </div>

    <!-- Additional Dialogs -->

    <!-- Export Dialog -->
    <Dialog v-model:open="showExportDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Export Categories</DialogTitle>
          <DialogDescription>
            Export your category data in various formats.
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <Button variant="outline" class="h-auto p-4 flex flex-col items-center gap-2">
              <FileText class="w-6 h-6 text-blue-600" />
              <span class="text-sm">CSV</span>
            </Button>
            <Button variant="outline" class="h-auto p-4 flex flex-col items-center gap-2">
              <FileText class="w-6 h-6 text-green-600" />
              <span class="text-sm">JSON</span>
            </Button>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showExportDialog = false">
            Cancel
          </Button>
          <Button @click="showExportDialog = false">
            Export
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Import Dialog -->
    <Dialog v-model:open="showImportDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Import Categories</DialogTitle>
          <DialogDescription>
            Import category data from a file.
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div class="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
            <Upload class="w-8 h-8 text-muted-foreground mx-auto mb-2" />
            <p class="text-sm text-muted-foreground mb-2">Drag & drop your file here</p>
            <Button variant="outline" size="sm">Choose File</Button>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showImportDialog = false">
            Cancel
          </Button>
          <Button @click="showImportDialog = false">
            Import
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Bulk Actions Dialog -->
    <Dialog v-model:open="showBulkActionsDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Bulk Actions</DialogTitle>
          <DialogDescription>
            Perform actions on multiple categories at once.
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div class="space-y-2">
            <Button variant="outline" class="w-full justify-start">
              <Archive class="w-4 h-4 mr-2" />
              Archive Empty Categories
            </Button>
            <Button variant="outline" class="w-full justify-start">
              <Download class="w-4 h-4 mr-2" />
              Export Selected
            </Button>
            <Button variant="outline" class="w-full justify-start text-red-600 hover:text-red-600">
              <Trash2 class="w-4 h-4 mr-2" />
              Delete Selected
            </Button>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showBulkActionsDialog = false">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Create Category Dialog -->
    <Dialog v-model:open="showCreateDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Create New Category</DialogTitle>
          <DialogDescription>
            Add a new category to organize your products.
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div>
            <Label for="create-name">Category Name</Label>
            <Input
              id="create-name"
              v-model="createForm.name"
              placeholder="Enter category name"
              class="mt-1"
            />
          </div>
          <div>
            <Label for="create-description">Description (Optional)</Label>
            <Textarea
              id="create-description"
              v-model="createForm.description"
              placeholder="Enter category description"
              class="mt-1"
              rows="2"
            />
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label for="create-icon">Icon (Optional)</Label>
              <Input
                id="create-icon"
                v-model="createForm.icon"
                placeholder="e.g., laptop"
                class="mt-1"
              />
            </div>
            <div>
              <Label for="create-color">Color</Label>
              <input
                id="create-color"
                v-model="createForm.color"
                type="color"
                class="w-full h-10 rounded border border-input mt-1"
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showCreateDialog = false">
            Cancel
          </Button>
          <Button @click="handleCreateCategory" :disabled="!createForm.name.trim()">
            Create Category
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>
/* Custom scrollbar for sidebar */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* View container transitions */
.view-container {
  animation: fadeIn 0.3s ease-in-out;
}

.view-container > div {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced view mode toggle buttons */
.flex.items-center.border.rounded-lg.p-1.space-x-1 button {
  transition: all 0.2s ease-in-out;
}

.flex.items-center.border.rounded-lg.p-1.space-x-1 button:hover {
  transform: translateY(-1px);
}
</style>
