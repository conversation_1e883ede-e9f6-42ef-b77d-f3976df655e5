<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Alert, AlertDescription } from '../../components/ui/alert'
import DuotoneIcon from '../../components/DuotoneIcon.vue'
import PaymentDialog from '../../components/PaymentDialog.vue'
import { ArrowLeft, CreditCard, CheckCircle, Crown, Zap, Shield, Star } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import { companyService } from '../../services/company.service'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Data
const isLoading = ref(false)
const selectedPlan = ref('monthly')
const availableModules = ref<any>({ modules: [], posTypes: [] })
const loadingModules = ref(true)
const showPaymentDialog = ref(false)

// Get query parameters
const moduleType = computed(() => route.query.module as string || 'POS')
const businessType = computed(() => route.query.type as string || 'RESTAURANT')
const reason = computed(() => route.query.reason as string || 'subscription_required')
const returnTo = computed(() => route.query.returnTo as string || '/dashboard/modules')

// Get pricing for the specific business type
const businessTypePricing = computed(() => {
  if (!availableModules.value.posTypes) return { price: 35 } // Fallback

  const posType = availableModules.value.posTypes.find(
    (pt: any) => pt.type === businessType.value
  )
  return posType || { price: 35 } // Fallback to RESTAURANT pricing
})

// Enhanced plan configuration with better features
const subscriptionPlans = computed(() => {
  const monthlyPrice = businessTypePricing.value.price
  const annualPrice = Math.round(monthlyPrice * 12 * 0.85) // 15% annual discount
  const annualSavings = (monthlyPrice * 12) - annualPrice

  return [
    {
      id: 'monthly',
      name: 'Monthly',
      price: monthlyPrice,
      currency: 'USD',
      period: 'month',
      billing: 'Billed monthly',
      popular: false,
      badge: null,
      savings: null,
      features: [
        'Complete POS System',
        'Unlimited Tables & Orders',
        'QR Menu Generation',
        'Kitchen Display System',
        'Real-time Analytics',
        'Customer Management',
        'Payment Processing',
        'Mobile App Access',
        'Email Support'
      ]
    },
    {
      id: 'annual',
      name: 'Annual',
      price: annualPrice,
      currency: 'USD',
      period: 'year',
      billing: 'Billed annually',
      popular: true,
      badge: 'BEST VALUE',
      savings: `Save $${annualSavings}`,
      features: [
        'Everything in Monthly',
        '15% Annual Discount',
        'Priority Support',
        'Advanced Analytics',
        'Custom Integrations',
        'Data Export & Backup',
        'Multi-location Support',
        'Staff Management',
        'Phone Support'
      ]
    }
  ]
})

// Business type display configuration
const businessConfig = computed(() => {
  const configs = {
    RESTAURANT: {
      name: 'Restaurant',
      icon: 'utensils',
      description: 'Complete restaurant management system with table service, kitchen display, and QR menus',
      color: 'orange'
    },
    BAR: {
      name: 'Bar & Pub',
      icon: 'wine',
      description: 'Bar management with drink recipes, tips tracking, and table ordering',
      color: 'purple'
    },
    HOTEL: {
      name: 'Hotel',
      icon: 'building',
      description: 'Hotel management with room service, guest management, and housekeeping',
      color: 'blue'
    },
    PHARMACY: {
      name: 'Pharmacy',
      icon: 'pills',
      description: 'Pharmacy management with prescription tracking and medicine inventory',
      color: 'green'
    },
    RETAIL_SHOP: {
      name: 'Retail Shop',
      icon: 'store',
      description: 'Retail management with inventory tracking and barcode scanning',
      color: 'indigo'
    }
  }

  return configs[businessType.value as keyof typeof configs] || configs.RESTAURANT
})

// Computed
const isTrialExpired = computed(() => reason.value === 'trial_expired')
const needsSubscription = computed(() => reason.value === 'subscription_required')

const pageTitle = computed(() => {
  if (isTrialExpired.value) {
    return 'Upgrade Your Plan'
  } else if (needsSubscription.value) {
    return 'Choose Your Plan'
  } else {
    return 'Subscription Plans'
  }
})

const pageSubtitle = computed(() => {
  if (isTrialExpired.value) {
    return `Your ${businessConfig.value.name} trial has ended. Choose a plan to continue using all features.`
  } else if (needsSubscription.value) {
    return `Unlock the full power of ${businessConfig.value.name} POS with our professional plans.`
  } else {
    return 'Manage your subscription and upgrade your plan anytime.'
  }
})

const selectedPlanDetails = computed(() => {
  return subscriptionPlans.value.find(plan => plan.id === selectedPlan.value)
})

// Methods
const loadAvailableModules = async () => {
  try {
    loadingModules.value = true
    const modules = await companyService.getAvailableModules()
    availableModules.value = modules
    console.log('Loaded pricing from backend:', {
      businessType: businessType.value,
      pricing: businessTypePricing.value
    })
  } catch (error) {
    console.error('Failed to load modules:', error)
    toast.error('Failed to load pricing information')
  } finally {
    loadingModules.value = false
  }
}

const goBack = () => {
  router.push('/dashboard/modules')
}

const selectPlan = (planId: string) => {
  selectedPlan.value = planId
}

const proceedToPayment = async () => {
  if (!selectedPlanDetails.value) {
    toast.error('Please select a plan')
    return
  }

  if (!authStore.currentCompany?._id) {
    toast.error('No company selected')
    return
  }

  // Open the ACLEDA payment dialog
  showPaymentDialog.value = true
}

const handlePaymentSuccess = (paymentResult: any) => {
  console.log('Payment successful:', paymentResult)

  toast.success('Payment processed successfully!')
  toast.success(`${businessConfig.value.name} subscription activated!`)

  // Close payment dialog
  showPaymentDialog.value = false

  // Redirect to return URL or modules page after a brief delay
  setTimeout(() => {
    router.push(returnTo.value)
  }, 2000)
}

const handlePaymentError = (error: any) => {
  console.error('Payment failed:', error)
  toast.error('Payment processing failed. Please try again.')
  showPaymentDialog.value = false
}

const formatPrice = (price: number, currency: string = 'USD') => {
  if (currency === 'USD') {
    return `$${price}`
  }
  return `${price} ${currency}`
}

const formatMonthlyPrice = (annualPrice: number) => {
  return Math.round(annualPrice / 12)
}

// Load pricing data on component mount
onMounted(async () => {
  await loadAvailableModules()
  // Auto-select annual plan as it's the best value
  selectedPlan.value = 'annual'
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-40" style="background-image: url('data:image/svg+xml,%3Csvg width=60 height=60 viewBox=0 0 60 60 xmlns=http://www.w3.org/2000/svg%3E%3Cg fill=none fill-rule=evenodd%3E%3Cg fill=%239C92AC fill-opacity=0.05%3E%3Ccircle cx=30 cy=30 r=2/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')"></div>

    <div class="relative">
      <!-- Header -->
      <div class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50">
        <div class="max-w-6xl mx-auto px-6 py-4">
          <div class="flex items-center gap-4">
            <Button variant="ghost" size="sm" @click="goBack" class="flex items-center gap-2 text-gray-600 hover:text-gray-900">
              <ArrowLeft class="w-4 h-4" />
              Back to Modules
            </Button>
          </div>
        </div>
      </div>

      <div class="max-w-6xl mx-auto px-6 py-12">
        <!-- Hero Section -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center gap-3 bg-white/60 backdrop-blur-sm rounded-2xl px-6 py-3 mb-6 shadow-sm border border-gray-200/50">
            <div :class="[
              'w-10 h-10 rounded-xl flex items-center justify-center',
              `bg-${businessConfig.color}-500`
            ]">
              <DuotoneIcon :name="businessConfig.icon" color="white" size="md" />
            </div>
            <div class="text-left">
              <div class="font-semibold text-gray-900">{{ businessConfig.name }} POS</div>
              <div class="text-sm text-gray-600">Professional Point of Sale</div>
            </div>
          </div>

          <h1 class="text-5xl font-bold text-gray-900 mb-4">{{ pageTitle }}</h1>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">{{ pageSubtitle }}</p>
        </div>

        <!-- Trial Expiry Alert -->
        <Alert v-if="isTrialExpired" class="max-w-2xl mx-auto mb-12 border-amber-200 bg-amber-50">
          <Zap class="w-5 h-5 text-amber-600" />
          <AlertDescription class="text-amber-800 font-medium">
            Your trial period has ended. Please choose a subscription plan to continue accessing all {{ businessConfig.name }} features.
          </AlertDescription>
        </Alert>

        <!-- Loading State -->
        <div v-if="loadingModules" class="text-center py-16">
          <div class="inline-flex items-center gap-3">
            <div class="w-8 h-8 border-3 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <span class="text-lg text-gray-600">Loading subscription plans...</span>
          </div>
        </div>

        <!-- Pricing Plans -->
        <div v-else-if="subscriptionPlans.length > 0" class="space-y-12">
          <!-- Plans Grid -->
          <div class="grid lg:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <Card
              v-for="plan in subscriptionPlans"
              :key="plan.id"
              @click="selectPlan(plan.id)"
              :class="[
                'cursor-pointer transition-all duration-300 hover:shadow-2xl transform hover:-translate-y-1 relative overflow-hidden group',
                selectedPlan === plan.id ? 'ring-3 ring-blue-500 shadow-2xl scale-105' : 'hover:shadow-xl',
                plan.popular ? 'border-2 border-blue-200' : 'border border-gray-200'
              ]"
            >
              <!-- Popular Badge -->
              <div v-if="plan.badge" class="absolute top-0 left-0 right-0 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-center py-2 text-sm font-semibold">
                <Crown class="w-4 h-4 inline mr-2" />
                {{ plan.badge }}
              </div>

              <!-- Selected Indicator -->
              <div v-if="selectedPlan === plan.id" class="absolute top-4 right-4 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
                <CheckCircle class="w-5 h-5 text-white" />
              </div>

              <div :class="plan.badge ? 'pt-12' : 'pt-6'">
                <CardHeader class="text-center pb-6">
                  <div class="space-y-4">
                    <div>
                      <CardTitle class="text-2xl font-bold text-gray-900 mb-2">{{ plan.name }}</CardTitle>
                      <p class="text-gray-600">{{ plan.billing }}</p>
                    </div>

                    <div class="space-y-2">
                      <div class="flex items-baseline justify-center gap-2">
                        <span class="text-5xl font-bold text-gray-900">
                          {{ formatPrice(plan.price) }}
                        </span>
                        <span class="text-lg text-gray-600">/{{ plan.period }}</span>
                      </div>

                      <div v-if="plan.id === 'annual'" class="text-sm text-gray-600">
                        Only {{ formatPrice(formatMonthlyPrice(plan.price)) }}/month
                      </div>

                      <div v-if="plan.savings" class="inline-block bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">
                        {{ plan.savings }}
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent class="space-y-6">
                  <!-- Features List -->
                  <ul class="space-y-3">
                    <li v-for="feature in plan.features" :key="feature" class="flex items-start gap-3">
                      <CheckCircle class="w-5 h-5 text-green-500 flex-shrink-0 mt-0.5" />
                      <span class="text-gray-700">{{ feature }}</span>
                    </li>
                  </ul>

                  <!-- CTA Button -->
                  <Button
                    :variant="selectedPlan === plan.id ? 'default' : 'outline'"
                    size="lg"
                    class="w-full h-12 font-semibold"
                    @click.stop="selectPlan(plan.id)"
                  >
                    <span v-if="selectedPlan === plan.id">Selected Plan</span>
                    <span v-else>Choose {{ plan.name }}</span>
                  </Button>
                </CardContent>
              </div>
            </Card>
          </div>

          <!-- Action Section -->
          <div class="text-center space-y-8">
            <!-- Selected Plan Summary -->
            <Card v-if="selectedPlanDetails" class="max-w-2xl mx-auto bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardContent class="p-8">
                <div class="flex items-center justify-between">
                  <div class="text-left">
                    <h3 class="text-xl font-bold text-gray-900 mb-2">{{ selectedPlanDetails.name }} Plan Selected</h3>
                    <p class="text-gray-600">
                      Full access to {{ businessConfig.name }} POS system with all professional features
                    </p>
                  </div>
                  <div class="text-right">
                    <div class="text-3xl font-bold text-blue-600">
                      {{ formatPrice(selectedPlanDetails.price) }}
                    </div>
                    <div class="text-gray-600">
                      per {{ selectedPlanDetails.period }}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Subscribe Button -->
            <div class="space-y-4">
              <Button
                size="lg"
                @click="proceedToPayment"
                :disabled="!selectedPlanDetails"
                class="px-12 h-14 text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg hover:shadow-xl transition-all"
              >
                <span class="flex items-center gap-3">
                  <CreditCard class="w-6 h-6" />
                  {{ isTrialExpired ? 'Upgrade Now' : 'Start Subscription' }}
                </span>
              </Button>

              <p class="text-sm text-gray-500">
                Secure payment • 30-day money-back guarantee • Cancel anytime
              </p>
            </div>
          </div>

          <!-- Trust Indicators -->
          <div class="bg-white/60 backdrop-blur-sm rounded-2xl p-8 border border-gray-200/50">
            <div class="grid md:grid-cols-3 gap-8 text-center">
              <div class="space-y-3">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <Shield class="w-6 h-6 text-green-600" />
                </div>
                <h4 class="font-semibold text-gray-900">Secure & Reliable</h4>
                <p class="text-gray-600 text-sm">Bank-level security with 99.9% uptime guarantee</p>
              </div>

              <div class="space-y-3">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <Star class="w-6 h-6 text-blue-600" />
                </div>
                <h4 class="font-semibold text-gray-900">Trusted by Thousands</h4>
                <p class="text-gray-600 text-sm">Join 5,000+ businesses across Cambodia</p>
              </div>

              <div class="space-y-3">
                <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <Zap class="w-6 h-6 text-purple-600" />
                </div>
                <h4 class="font-semibold text-gray-900">Instant Activation</h4>
                <p class="text-gray-600 text-sm">Get started immediately after payment</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Error State -->
        <div v-else class="text-center py-16">
          <Alert class="max-w-md mx-auto border-red-200 bg-red-50">
            <AlertDescription class="text-red-800">
              Failed to load pricing information. Please try again later.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    </div>

    <!-- Payment Dialog -->
    <PaymentDialog
      v-if="selectedPlanDetails && authStore.currentCompany && authStore.currentUser"
      v-model:open="showPaymentDialog"
      :subscription-id="''"
      :module="moduleType"
      :amount="selectedPlanDetails.price"
      :currency="selectedPlanDetails.currency as 'USD' | 'KHR'"
      :description="`${businessConfig.name} ${selectedPlanDetails.name} Subscription`"
      :trial-conversion="isTrialExpired"
      @payment-success="handlePaymentSuccess"
      @payment-error="handlePaymentError"
    />
  </div>
</template>

<style scoped>
.border-3 {
  border-width: 3px;
}
</style>
