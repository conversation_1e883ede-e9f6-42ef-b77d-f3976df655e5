<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useCompanyStore } from '@/stores/company'
import { productService } from '@/services/product.service'
import { categoryService } from '@/services/category.service'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import ProductForm from '@/components/ProductForm.vue'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import { toast } from 'vue-sonner'
import type { Product, Category } from '../../../../shared/types/pos'
import type { ProductListOptions } from '@/services/product.service'
import {
  ArrowLeft,
  Package,
  Settings,
  Download,
  Upload,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Grid3x3,
  List,
  SortAsc,
  SortDesc,
  RefreshCw,
  Archive,
  FileText,
  PieChart,
  BarChart3,
  TrendingUp,
  Activity,
  Eye,
  AlertTriangle,
  Star,
  ShoppingCart,
  Utensils,
  Clock,
  DollarSign,
  Layers3,
  Users,
  Calendar,
  Edit,
  Trash2,
  Copy,
  ToggleLeft,
  ToggleRight,
  CheckCircle,
  XCircle,
  ImageIcon,
  Tag,
  Zap,
  Boxes,
  ChefHat
} from 'lucide-vue-next'

const router = useRouter()
const authStore = useAuthStore()
const companyStore = useCompanyStore()

// Load saved preferences
const getSavedViewMode = (): 'grid' | 'list' | 'table' => {
  try {
    const saved = localStorage.getItem('productViewMode')
    if (saved && ['grid', 'list', 'table'].includes(saved)) {
      return saved as 'grid' | 'list' | 'table'
    }
  } catch (err) {
    console.error('Failed to load view mode preference:', err)
  }
  return 'grid'
}

const getSavedSidebarState = (): boolean => {
  try {
    const saved = localStorage.getItem('productSidebarOpen')
    if (saved !== null) {
      return JSON.parse(saved)
    }
  } catch (err) {
    console.error('Failed to load sidebar state preference:', err)
  }
  return true
}

// View preferences
const viewMode = ref<'grid' | 'list' | 'table'>(getSavedViewMode())
const sidebarOpen = ref(getSavedSidebarState())
const showFilters = ref(false)

// Save preferences when they change
watch(viewMode, (newMode) => {
  try {
    localStorage.setItem('productViewMode', newMode)
  } catch (err) {
    console.error('Failed to save view mode preference:', err)
  }
})

watch(sidebarOpen, (newState) => {
  try {
    localStorage.setItem('productSidebarOpen', JSON.stringify(newState))
  } catch (err) {
    console.error('Failed to save sidebar state preference:', err)
  }
})

// State
const products = ref<Product[]>([])
const categories = ref<Category[]>([])
const isLoading = ref(true)
const isLoadingMore = ref(false)
const isRefreshing = ref(false)
const error = ref('')

// Search and filters
const searchQuery = ref('')
const selectedCategory = ref('all')
const selectedSpiceLevel = ref('all')
const selectedMealTime = ref('all')
const selectedDietaryRestriction = ref('all')
const showBBQOnly = ref(false)
const showAvailableOnly = ref(false)
const showKhmerTraditional = ref(false)
const showLowStockOnly = ref(false)
const priceRange = ref<[number, number]>([0, 1000])
const sortBy = ref<'name' | 'price' | 'stock' | 'createdAt'>('name')
const sortOrder = ref<'asc' | 'desc'>('asc')

// Pagination
const currentPage = ref(1)
const itemsPerPage = ref(20)
const totalItems = ref(0)
const totalPages = ref(0)

// Product management
const showProductDialog = ref(false)
const selectedProduct = ref<Product | null>(null)
const isEditing = ref(false)
const showDeleteDialog = ref(false)
const productToDelete = ref<Product | null>(null)

// Bulk actions
const selectedProducts = ref<string[]>([])
const showBulkActions = ref(false)

// Check if company has restaurant subscription
const isRestaurantSubscription = computed(() => {
  // TODO: Fix type issue - return companyStore.currentCompany?.posType === 'RESTAURANT'
  return true
})

// Enhanced stats
const productStats = computed(() => {
  const allProducts = products.value || []
  const available = allProducts.filter((p: Product) => p.isAvailable)
  const unavailable = allProducts.filter((p: Product) => !p.isAvailable)
  const khmerTraditional = allProducts.filter((p: Product) => p.restaurantFeatures?.isTraditionalKhmer)
  const bbqItems = allProducts.filter((p: Product) => p.restaurantFeatures?.isBBQItem)
  const withImages = allProducts.filter((p: Product) => p.images && p.images.length > 0)
  
  const averagePrice = allProducts.length > 0 ? allProducts.reduce((sum: number, p: Product) => sum + p.price, 0) / allProducts.length : 0
  const utilizationRate = allProducts.length > 0 ? Math.round((available.length / allProducts.length) * 100) : 0
  
  // For restaurants, calculate different stats
  if (isRestaurantSubscription.value) {
    return {
      total: allProducts.length,
      available: available.length,
      unavailable: unavailable.length,
      outOfStock: 0, // Not relevant for restaurants
      lowStock: 0, // Not relevant for restaurants
      khmerTraditional: khmerTraditional.length,
      bbqItems: bbqItems.length,
      withImages: withImages.length,
      totalValue: 0, // Not calculated for restaurants
      averagePrice,
      utilizationRate,
      recentlyAdded: Math.floor(Math.random() * 5) + 1 // Mock data
    }
  }
  
  // For retail, calculate stock-based stats
  const outOfStock = allProducts.filter((p: Product) => p.stock <= 0)
  const lowStock = allProducts.filter((p: Product) => p.stock <= p.minStock && p.stock > 0)
  const totalValue = allProducts.reduce((sum: number, p: Product) => sum + (p.price * p.stock), 0)
  
  return {
    total: allProducts.length,
    available: available.length,
    unavailable: unavailable.length,
    outOfStock: outOfStock.length,
    lowStock: lowStock.length,
    khmerTraditional: khmerTraditional.length,
    bbqItems: bbqItems.length,
    withImages: withImages.length,
    totalValue,
    averagePrice,
    utilizationRate,
    recentlyAdded: Math.floor(Math.random() * 5) + 1 // Mock data
  }
})

// Filter options for restaurant
const spiceLevels = [
  { value: 'mild', label: 'Mild (ស្រាល)', icon: '🌶️' },
  { value: 'medium', label: 'Medium (មធ្យម)', icon: '🌶️🌶️' },
  { value: 'hot', label: 'Hot (ហឹរ)', icon: '🌶️🌶️🌶️' },
  { value: 'extra_hot', label: 'Extra Hot (ហឹរបំផុត)', icon: '🌶️🌶️🌶️🌶️' }
]

const mealTimes = [
  { value: 'breakfast', label: 'Breakfast (អាហារពេលព្រឹក)' },
  { value: 'lunch', label: 'Lunch (អាហារពេលថ្ងៃ)' },
  { value: 'dinner', label: 'Dinner (អាហារពេលល្ងាច)' },
  { value: 'snack', label: 'Snack (ស្ន៊ែក)' },
  { value: 'dessert', label: 'Dessert (បង្អែម)' }
]

const dietaryRestrictions = [
  { value: 'vegetarian', label: 'Vegetarian (បួស)' },
  { value: 'vegan', label: 'Vegan (កម្មវិធីបួស)' },
  { value: 'gluten_free', label: 'Gluten Free' },
  { value: 'dairy_free', label: 'Dairy Free' },
  { value: 'halal', label: 'Halal (ហាឡាល)' },
  { value: 'kosher', label: 'Kosher' }
]

// Computed properties
const filteredProducts = computed(() => {
  let filtered = products.value || []

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter((product: Product) => 
      product.name.toLowerCase().includes(query) ||
      product.nameKhmer?.toLowerCase().includes(query) ||
      product.description?.toLowerCase().includes(query) ||
      product.sku.toLowerCase().includes(query)
    )
  }

  if (selectedCategory.value && selectedCategory.value !== 'all') {
    filtered = filtered.filter((product: Product) => product.categoryId === selectedCategory.value)
  }

  if (showAvailableOnly.value) {
    filtered = filtered.filter((product: Product) => product.isAvailable)
  }

  if (showLowStockOnly.value) {
    filtered = filtered.filter((product: Product) => product.stock <= product.minStock)
  }

  if (isRestaurantSubscription.value) {
    if (selectedSpiceLevel.value && selectedSpiceLevel.value !== 'all') {
      filtered = filtered.filter((product: Product) => 
        product.restaurantFeatures?.spiceLevel === selectedSpiceLevel.value
      )
    }

    if (selectedMealTime.value && selectedMealTime.value !== 'all') {
      filtered = filtered.filter((product: Product) => 
        product.restaurantFeatures?.mealTimes?.includes(selectedMealTime.value as any)
      )
    }

    if (selectedDietaryRestriction.value && selectedDietaryRestriction.value !== 'all') {
      filtered = filtered.filter((product: Product) => 
        product.restaurantFeatures?.dietaryRestrictions?.includes(selectedDietaryRestriction.value as any)
      )
    }

    if (showBBQOnly.value) {
      filtered = filtered.filter((product: Product) => product.restaurantFeatures?.isBBQItem)
    }

    if (showKhmerTraditional.value) {
      filtered = filtered.filter((product: Product) => product.restaurantFeatures?.isTraditionalKhmer)
    }
  }

  // Sort
  filtered.sort((a: Product, b: Product) => {
    let aValue: any, bValue: any
    
    switch (sortBy.value) {
      case 'name':
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
      case 'price':
        aValue = a.price
        bValue = b.price
        break
      case 'stock':
        aValue = a.stock
        bValue = b.stock
        break
      case 'createdAt':
        aValue = new Date(a.createdAt).getTime()
        bValue = new Date(b.createdAt).getTime()
        break
      default:
        return 0
    }

    if (sortOrder.value === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
    }
  })

  return filtered
})

const hasActiveFilters = computed(() => {
  return (selectedCategory.value && selectedCategory.value !== 'all') || 
         (selectedSpiceLevel.value && selectedSpiceLevel.value !== 'all') || 
         (selectedMealTime.value && selectedMealTime.value !== 'all') || 
         (selectedDietaryRestriction.value && selectedDietaryRestriction.value !== 'all') || 
         showBBQOnly.value || showAvailableOnly.value || showKhmerTraditional.value || showLowStockOnly.value
})

// Methods
const loadProducts = async (page = 1) => {
  try {
    if (page === 1) {
      isLoading.value = true
    } else {
      isLoadingMore.value = true
    }
    
    if (!authStore.currentUser?.currentCompanyId) return

    const options: ProductListOptions = {
      page,
      limit: itemsPerPage.value,
      search: searchQuery.value || undefined,
      categoryId: (selectedCategory.value && selectedCategory.value !== 'all') ? selectedCategory.value : undefined,
      isAvailable: showAvailableOnly.value || undefined,
      spiceLevel: (selectedSpiceLevel.value && selectedSpiceLevel.value !== 'all') ? selectedSpiceLevel.value : undefined,
      mealTime: (selectedMealTime.value && selectedMealTime.value !== 'all') ? selectedMealTime.value : undefined,
      dietaryRestriction: (selectedDietaryRestriction.value && selectedDietaryRestriction.value !== 'all') ? selectedDietaryRestriction.value : undefined,
      isBBQItem: showBBQOnly.value || undefined,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
    }

    const response = await productService.getProducts(authStore.currentUser.currentCompanyId, options)
    
    if (page === 1) {
      products.value = response.products
    } else {
      products.value = [...products.value, ...response.products]
    }
    
    totalItems.value = response.total
    totalPages.value = response.totalPages
    currentPage.value = page
    
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load products'
    toast.error(error.value)
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

const loadCategories = async () => {
  try {
    if (!authStore.currentUser?.currentCompanyId) return
    
    const data = await categoryService.getCategories(authStore.currentUser.currentCompanyId)
    categories.value = data
  } catch (err) {
    console.error('Failed to load categories:', err)
  }
}

const refreshProducts = async () => {
  isRefreshing.value = true
  currentPage.value = 1
  await loadProducts()
  isRefreshing.value = false
}

const clearFilters = () => {
  selectedCategory.value = 'all'
  selectedSpiceLevel.value = 'all'
  selectedMealTime.value = 'all'
  selectedDietaryRestriction.value = 'all'
  showBBQOnly.value = false
  showAvailableOnly.value = false
  showKhmerTraditional.value = false
  showLowStockOnly.value = false
  searchQuery.value = ''
}

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const createProduct = () => {
  selectedProduct.value = null
  isEditing.value = false
  showProductDialog.value = true
}

const editProduct = (product: Product) => {
  selectedProduct.value = product
  isEditing.value = true
  showProductDialog.value = true
}

const duplicateProduct = async (product: Product) => {
  try {
    await productService.duplicateProduct(product._id)
    toast.success('Product duplicated successfully')
    await refreshProducts()
  } catch (err) {
    toast.error('Failed to duplicate product')
  }
}

const toggleAvailability = async (product: Product) => {
  try {
    await productService.toggleAvailability(product._id, !product.isAvailable)
    toast.success(`Product ${product.isAvailable ? 'disabled' : 'enabled'} successfully`)
    await refreshProducts()
  } catch (err) {
    toast.error('Failed to update product availability')
  }
}

const confirmDeleteProduct = (product: Product) => {
  productToDelete.value = product
  showDeleteDialog.value = true
}

const deleteProduct = async () => {
  if (!productToDelete.value) return
  
  try {
    await productService.deleteProduct(productToDelete.value._id)
    toast.success('Product deleted successfully')
    await refreshProducts()
    showDeleteDialog.value = false
    productToDelete.value = null
  } catch (err) {
    toast.error('Failed to delete product')
  }
}

const handleProductSaved = async (product: Product) => {
  try {
    showProductDialog.value = false
    
    // Add a small delay to ensure database consistency
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // Ensure products.value is always an array
    if (!Array.isArray(products.value)) {
      products.value = []
    }
    
    // If creating a new product, add it directly to the list first
    if (!isEditing.value) {
      products.value = [product, ...products.value]
    } else {
      // If editing, update the existing product in the list
      const index = products.value.findIndex(p => p._id === product._id)
      if (index !== -1) {
        products.value[index] = product
      }
    }
    
    // Then refresh from server to ensure consistency
    await refreshProducts()
    
    toast.success(isEditing.value ? 'Product updated successfully' : 'Product created successfully')
  } catch (error) {
    console.error('Error handling product save:', error)
    // If there's an error, still refresh to maintain consistency
    await refreshProducts()
  }
}

const handleProductCancel = () => {
  showProductDialog.value = false
  selectedProduct.value = null
}

const getCategoryName = (categoryId: string) => {
  const category = categories.value.find((c: Category) => c._id === categoryId)
  return category?.name || 'Unknown'
}

const getSpiceLevelBadge = (spiceLevel?: string) => {
  const level = spiceLevels.find(l => l.value === spiceLevel)
  return level ? { label: level.label, icon: level.icon } : null
}

const formatPrice = (price: number, currency = 'KHR') => {
  if (currency === 'USD') {
    return `$${price.toFixed(2)}`
  } else {
    return `${price.toLocaleString()} ៛`
  }
}

const getStockStatus = (product: Product) => {
  if (product.stock <= 0) return { label: 'Out of Stock', variant: 'destructive' as const, icon: XCircle }
  if (product.stock <= product.minStock) return { label: 'Low Stock', variant: 'secondary' as const, icon: AlertTriangle }
  return { label: 'In Stock', variant: 'default' as const, icon: CheckCircle }
}

const goBack = () => {
  router.back()
}

const goToCategories = () => {
  router.push('/dashboard/categories')
}

const exportProducts = () => {
  // TODO: Implement export functionality
  toast.info('Export functionality coming soon')
}

const importProducts = () => {
  // TODO: Implement import functionality
  toast.info('Import functionality coming soon')
}



// Watchers
watch([searchQuery, selectedCategory, selectedSpiceLevel, selectedMealTime, selectedDietaryRestriction, showBBQOnly, showAvailableOnly, showKhmerTraditional, showLowStockOnly], () => {
  refreshProducts()
})

watch([sortBy, sortOrder], () => {
  refreshProducts()
})

onMounted(async () => {
  await Promise.all([loadCategories(), loadProducts()])
})
</script>

<template>
  <div class="min-h-[calc(100vh-100px)] bg-background">
    <!-- Main Layout -->
    <div class="h-[calc(100vh-100px)] flex">
      <!-- Sidebar -->
      <div
        :class="[
          'transition-all duration-300 border-r bg-muted/10',
          sidebarOpen ? 'w-80' : 'w-0'
        ]"
        class="overflow-hidden"
      >
        <div class="p-6 space-y-6 overflow-y-auto">
          <!-- Sidebar Header -->
          <div class="space-y-2">
            <h3 class="font-semibold text-lg">Product Management</h3>
            <p class="text-sm text-muted-foreground">
              Manage your inventory and menu items
            </p>
          </div>

          <Separator />

          <!-- Quick Stats -->
          <div class="space-y-4">
            <h4 class="font-medium text-sm text-muted-foreground uppercase tracking-wide">Overview</h4>
            <div class="space-y-3">
              <div class="flex items-center justify-between p-3 rounded-lg bg-background border">
                <div class="flex items-center gap-2">
                  <Package class="w-4 h-4 text-blue-600" />
                  <span class="text-sm">Total Products</span>
                </div>
                <Badge variant="secondary">{{ productStats.total }}</Badge>
              </div>

              <div class="flex items-center justify-between p-3 rounded-lg bg-background border">
                <div class="flex items-center gap-2">
                  <CheckCircle class="w-4 h-4 text-green-600" />
                  <span class="text-sm">Available</span>
                </div>
                <Badge variant="secondary">{{ productStats.available }}</Badge>
              </div>

              <!-- Restaurant mode: Show unavailable instead of low stock -->
              <div v-if="isRestaurantSubscription" class="flex items-center justify-between p-3 rounded-lg bg-background border">
                <div class="flex items-center gap-2">
                  <XCircle class="w-4 h-4 text-red-600" />
                  <span class="text-sm">Unavailable</span>
                </div>
                <Badge variant="secondary">{{ productStats.unavailable }}</Badge>
              </div>

              <!-- Retail mode: Show low stock -->
              <div v-else class="flex items-center justify-between p-3 rounded-lg bg-background border">
                <div class="flex items-center gap-2">
                  <AlertTriangle class="w-4 h-4 text-orange-600" />
                  <span class="text-sm">Low Stock</span>
                </div>
                <Badge variant="secondary">{{ productStats.lowStock }}</Badge>
              </div>

              <div class="p-3 rounded-lg bg-background border space-y-2">
                <div class="flex items-center justify-between">
                  <span class="text-sm">Availability</span>
                  <span class="text-sm font-medium">{{ productStats.utilizationRate }}%</span>
                </div>
                <Progress :value="productStats.utilizationRate" class="h-2" />
              </div>
            </div>
          </div>

          <Separator />

          <!-- Search and Filters -->
          <div class="space-y-4">
            <h4 class="font-medium text-sm text-muted-foreground uppercase tracking-wide">Filters</h4>

            <!-- Search -->
            <div class="relative">
              <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="Search products..."
                class="pl-10"
              />
            </div>

            <!-- Category Filter -->
            <div class="space-y-2">
              <Label class="text-xs">Category</Label>
              <Select v-model="selectedCategory">
                <SelectTrigger>
                  <SelectValue placeholder="All categories" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem 
                    v-for="category in categories" 
                    :key="category._id"
                    :value="category._id"
                  >
                    {{ category.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Quick Filters -->
            <div class="space-y-3">
              <Label class="text-xs">Quick Filters</Label>
              <div class="space-y-2">
                <div class="flex items-center space-x-2">
                  <Switch 
                    id="availableOnly" 
                    v-model:checked="showAvailableOnly"
                  />
                  <Label for="availableOnly" class="text-sm">Available only</Label>
                </div>
                <!-- Only show stock filter for non-restaurant businesses -->
                <div v-if="!isRestaurantSubscription" class="flex items-center space-x-2">
                  <Switch 
                    id="lowStockOnly" 
                    v-model:checked="showLowStockOnly"
                  />
                  <Label for="lowStockOnly" class="text-sm">Low stock only</Label>
                </div>
              </div>
            </div>

            <!-- Restaurant-specific filters -->
            <div v-if="isRestaurantSubscription" class="space-y-4">
              <Separator />
              <Label class="text-xs">Restaurant Filters</Label>
              
              <div class="space-y-3">
                <div class="flex items-center space-x-2">
                  <Switch 
                    id="bbqOnly" 
                    v-model:checked="showBBQOnly"
                  />
                  <Label for="bbqOnly" class="text-sm">BBQ items only</Label>
                </div>

                <div class="flex items-center space-x-2">
                  <Switch 
                    id="khmerTraditional" 
                    v-model:checked="showKhmerTraditional"
                  />
                  <Label for="khmerTraditional" class="text-sm">Traditional Khmer</Label>
                </div>
              </div>

              <div class="space-y-2">
                <Label class="text-xs">Spice Level</Label>
                <Select v-model="selectedSpiceLevel">
                  <SelectTrigger>
                    <SelectValue placeholder="All spice levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Spice Levels</SelectItem>
                    <SelectItem 
                      v-for="level in spiceLevels" 
                      :key="level.value"
                      :value="level.value"
                    >
                      <div class="flex items-center">
                        <span class="mr-2">{{ level.icon }}</span>
                        {{ level.label }}
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div class="space-y-2">
                <Label class="text-xs">Meal Time</Label>
                <Select v-model="selectedMealTime">
                  <SelectTrigger>
                    <SelectValue placeholder="All meal times" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Meal Times</SelectItem>
                    <SelectItem 
                      v-for="meal in mealTimes" 
                      :key="meal.value"
                      :value="meal.value"
                    >
                      {{ meal.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div class="space-y-2">
                <Label class="text-xs">Dietary</Label>
                <Select v-model="selectedDietaryRestriction">
                  <SelectTrigger>
                    <SelectValue placeholder="All dietary" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Dietary</SelectItem>
                    <SelectItem 
                      v-for="restriction in dietaryRestrictions" 
                      :key="restriction.value"
                      :value="restriction.value"
                    >
                      {{ restriction.label }}
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <!-- Sort Options -->
            <div class="space-y-2">
              <Label class="text-xs">Sort by</Label>
              <div class="flex gap-2">
                <Select v-model="sortBy">
                  <SelectTrigger class="flex-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="price">Price</SelectItem>
                    <SelectItem value="stock">Stock</SelectItem>
                    <SelectItem value="createdAt">Created</SelectItem>
                  </SelectContent>
                </Select>

                <Button
                  variant="outline"
                  size="icon"
                  @click="sortOrder = sortOrder === 'asc' ? 'desc' : 'asc'"
                >
                  <SortAsc v-if="sortOrder === 'asc'" class="w-4 h-4" />
                  <SortDesc v-else class="w-4 h-4" />
                </Button>
              </div>
            </div>

            <Button variant="outline" size="sm" @click="clearFilters" class="w-full">
              Clear Filters
            </Button>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Header -->
        <div class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <Button variant="ghost" size="icon" @click="toggleSidebar">
                  <Grid3x3 class="w-4 h-4" />
                </Button>

                <div class="flex items-center gap-2">
                  <Button variant="ghost" size="sm" @click="goBack">
                    <ArrowLeft class="w-4 h-4 mr-2" />
                    Back
                  </Button>
                  <Separator orientation="vertical" class="h-4" />
                  <nav class="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>Dashboard</span>
                    <span>/</span>
                    <span>POS</span>
                    <span>/</span>
                    <span class="text-foreground font-medium">Products</span>
                  </nav>
                </div>
              </div>

              <div class="flex items-center gap-3">
                <!-- View Mode Toggle -->
                <div class="flex items-center border rounded-lg p-1 space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    :class="viewMode === 'grid' ? 'bg-muted' : ''"
                    @click="viewMode = 'grid'"
                  >
                    <Grid3x3 class="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    :class="viewMode === 'list' ? 'bg-muted' : ''"
                    @click="viewMode = 'list'"
                  >
                    <List class="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    :class="viewMode === 'table' ? 'bg-muted' : ''"
                    @click="viewMode = 'table'"
                  >
                    <FileText class="w-4 h-4" />
                  </Button>
                </div>

                <Button variant="outline" @click="refreshProducts" :disabled="isRefreshing">
                  <RefreshCw :class="['w-4 h-4 mr-2', isRefreshing && 'animate-spin']" />
                  Refresh
                </Button>

                              <Button @click="createProduct">
                <Plus class="w-4 h-4 mr-2" />
                Add Product
              </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger as-child>
                    <Button variant="outline" size="icon">
                      <MoreHorizontal class="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem @click="goToCategories">
                      <Tag class="w-4 h-4 mr-2" />
                      Manage Categories
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem @click="exportProducts">
                      <Download class="w-4 h-4 mr-2" />
                      Export Products
                    </DropdownMenuItem>
                    <DropdownMenuItem @click="importProducts">
                      <Upload class="w-4 h-4 mr-2" />
                      Import Products
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>

        <!-- Modern Analytics Dashboard -->
        <div class="w-full py-6 bg-gray-50/50 dark:bg-gray-900/50 border-b border-gray-200 dark:border-gray-800">
          <div class="w-full px-4 sm:px-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 w-full">
            
            <!-- Total Products Card -->
            <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-5 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
                  <Package class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ productStats.total }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Total Products</div>
                </div>
              </div>
              <div class="flex items-center text-sm">
                <TrendingUp class="w-4 h-4 text-green-500 mr-2" />
                <span class="text-green-600 dark:text-green-400 font-medium">+{{ productStats.recentlyAdded }}</span>
                <span class="text-gray-500 dark:text-gray-400 ml-1">this week</span>
              </div>
            </div>

            <!-- Available Products Card -->
            <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-5 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-green-50 dark:bg-green-900/30 rounded-lg">
                  <CheckCircle class="w-5 h-5 text-green-600 dark:text-green-400" />
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ productStats.available }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Available</div>
                </div>
              </div>
              <div class="space-y-2">
                <div class="flex items-center justify-between text-sm">
                  <span class="text-gray-600 dark:text-gray-400">Rate</span>
                  <span class="font-medium text-gray-900 dark:text-white">{{ productStats.utilizationRate }}%</span>
                </div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    class="bg-green-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${productStats.utilizationRate}%` }"
                  ></div>
                </div>
              </div>
            </div>

            <!-- Restaurant Mode: Unavailable -->
            <div v-if="isRestaurantSubscription" class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-5 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-red-50 dark:bg-red-900/30 rounded-lg">
                  <XCircle class="w-5 h-5 text-red-600 dark:text-red-400" />
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ productStats.unavailable }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Unavailable</div>
                </div>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                Temporarily disabled
              </div>
            </div>

            <!-- Retail Mode: Low Stock -->
            <div v-else class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-5 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-orange-50 dark:bg-orange-900/30 rounded-lg">
                  <AlertTriangle class="w-5 h-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ productStats.lowStock }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Low Stock</div>
                </div>
              </div>
              <div class="flex items-center text-sm">
                <AlertTriangle class="w-4 h-4 text-orange-500 mr-2" />
                <span class="text-gray-600 dark:text-gray-400">
                  {{ productStats.lowStock > 0 ? 'Need restocking' : 'Stock healthy' }}
                </span>
              </div>
            </div>

            <!-- Retail Mode: Out of Stock -->
            <div v-if="!isRestaurantSubscription" class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-5 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-red-50 dark:bg-red-900/30 rounded-lg">
                  <XCircle class="w-5 h-5 text-red-600 dark:text-red-400" />
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ productStats.outOfStock }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Out of Stock</div>
                </div>
              </div>
              <div class="flex items-center text-sm">
                <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                <span class="text-gray-600 dark:text-gray-400">
                  {{ productStats.outOfStock > 0 ? 'Urgent attention' : 'All in stock' }}
                </span>
              </div>
            </div>

            <!-- Average Price Card -->
            <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-5 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-purple-50 dark:bg-purple-900/30 rounded-lg">
                  <DollarSign class="w-5 h-5 text-purple-600 dark:text-purple-400" />
                </div>
                <div class="text-right">
                  <div class="text-xl font-bold text-gray-900 dark:text-white">{{ formatPrice(productStats.averagePrice) }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Avg. Price</div>
                </div>
              </div>
              <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <BarChart3 class="w-4 h-4 mr-2" />
                Per item average
              </div>
            </div>

            <!-- Restaurant Mode: Khmer Traditional -->
            <div v-if="isRestaurantSubscription" class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-5 hover:shadow-md transition-shadow duration-200">
              <div class="flex items-center justify-between mb-4">
                <div class="p-2 bg-amber-50 dark:bg-amber-900/30 rounded-lg">
                  <Star class="w-5 h-5 text-amber-600 dark:text-amber-400" />
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold text-gray-900 dark:text-white">{{ productStats.khmerTraditional }}</div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide font-medium">Khmer Traditional</div>
                </div>
              </div>
              <div class="space-y-1">
                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                  <div class="w-2 h-2 bg-amber-500 rounded-full mr-2"></div>
                  Heritage dishes
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ productStats.total > 0 ? Math.round((productStats.khmerTraditional / productStats.total) * 100) : 0 }}% of catalog
                </div>
              </div>

            </div>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="xcard flex-1 overflow-auto p-6">
          <!-- Loading State -->
          <div v-if="isLoading" class="flex justify-center items-center h-64">
            <div class="flex items-center gap-3">
              <div class="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
              <span class="text-muted-foreground">Loading products...</span>
            </div>
          </div>

          <!-- Error State -->
          <Card v-else-if="error" class="border-red-200 bg-red-50">
            <CardContent class="pt-6">
              <div class="flex items-center gap-2 text-red-600 justify-between">
                <span>{{ error }}</span>
                <Button variant="outline" size="sm" @click="refreshProducts">
                  Retry
                </Button>
              </div>
            </CardContent>
          </Card>

          <!-- Dynamic View Content -->
          <div v-else class=" view-container">
            <!-- Grid View -->
            <div v-if="viewMode === 'grid'" class="space-y-6">
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-xl font-bold tracking-tight">Products Gallery</h2>
                  <p class="text-muted-foreground">
                    Visual overview of your product catalog
                  </p>
                </div>
                <div class="flex items-center gap-2">
                  <Badge v-if="hasActiveFilters" variant="secondary" class="px-3 py-1">
                    {{ filteredProducts.length }} of {{ productStats.total }} results
                  </Badge>
                </div>
              </div>

              <!-- Enhanced Grid Layout -->
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
                <!-- Product Cards -->
                <div
                  v-for="product in filteredProducts"
                  :key="product._id"
                  class="group relative bg-white dark:bg-gray-800 rounded-2xl border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10 hover:-translate-y-1 overflow-hidden"
                >
                  <!-- Product Image -->
                  <div class="aspect-square bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 relative overflow-hidden">
                    <img
                      v-if="product.thumbnail"
                      :src="product.thumbnail"
                      :alt="product.name"
                      class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div v-else class="w-full h-full flex items-center justify-center">
                      <ImageIcon class="w-12 h-12 text-gray-400 dark:text-gray-500" />
                    </div>
                    
                    <!-- Status Badge -->
                    <div class="absolute top-3 right-3">
                      <Badge 
                        :variant="getStockStatus(product).variant" 
                        class="shadow-sm backdrop-blur-sm bg-red-500/90 dark:bg-gray-900/90 text-xs font-medium"
                      >
                        <component :is="getStockStatus(product).icon" class="w-3 h-3 mr-1" />
                        {{ getStockStatus(product).label }}
                      </Badge>
                    </div>

                    <!-- Action Menu -->
                    <div class="absolute top-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                      <DropdownMenu>
                        <DropdownMenuTrigger as-child>
                          <Button variant="secondary" size="icon" class="h-8 w-8 bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm shadow-sm">
                            <MoreHorizontal class="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start">
                          <DropdownMenuItem @click="editProduct(product)">
                            <Edit class="w-4 h-4 mr-2" />
                            Edit Product
                          </DropdownMenuItem>
                          <DropdownMenuItem @click="duplicateProduct(product)">
                            <Copy class="w-4 h-4 mr-2" />
                            Duplicate
                          </DropdownMenuItem>
                          <DropdownMenuItem @click="toggleAvailability(product)">
                            <component :is="product.isAvailable ? ToggleLeft : ToggleRight" class="w-4 h-4 mr-2" />
                            {{ product.isAvailable ? 'Disable' : 'Enable' }}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem @click="confirmDeleteProduct(product)" class="text-red-600">
                            <Trash2 class="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>

                  <!-- Product Info -->
                  <div class="p-4 space-y-3">
                    <!-- Product Name & Category -->
                    <div class="space-y-1">
                      <h3 class="font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors line-clamp-1">
                        {{ product.name }}
                      </h3>
                      <p v-if="product.nameKhmer" class="text-sm text-gray-500 dark:text-gray-400 line-clamp-1">
                        {{ product.nameKhmer }}
                      </p>
                      <div class="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                        <Tag class="w-3 h-3" />
                        <span class="truncate">{{ getCategoryName(product.categoryId) }}</span>
                      </div>
                    </div>

                    <!-- Price & Stock -->
                    <div class="flex items-center justify-between">
                      <div class="space-y-1">
                        <p class="text-lg font-bold text-gray-900 dark:text-white">
                          {{ formatPrice(product.price) }}
                        </p>
                        <p v-if="!isRestaurantSubscription" class="text-xs text-gray-500 dark:text-gray-400">
                          {{ product.stock }} in stock
                        </p>
                      </div>
                      <div class="text-right">
                        <p class="text-xs text-gray-500 dark:text-gray-400 mb-1">SKU</p>
                        <p class="text-xs font-mono text-gray-700 dark:text-gray-300">{{ product.sku }}</p>
                      </div>
                    </div>

                    <!-- Restaurant Features -->
                    <div v-if="isRestaurantSubscription && product.restaurantFeatures" class="flex flex-wrap gap-1">
                      <Badge v-if="product.restaurantFeatures.isTraditionalKhmer" variant="outline" class="text-xs bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-700">
                        <Star class="w-3 h-3 mr-1" />
                        Khmer
                      </Badge>
                      <Badge v-if="product.restaurantFeatures.isBBQItem" variant="outline" class="text-xs bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700">
                        <Zap class="w-3 h-3 mr-1" />
                        BBQ
                      </Badge>
                      <Badge v-if="product.restaurantFeatures.spiceLevel" variant="outline" class="text-xs bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-700">
                        {{ getSpiceLevelBadge(product.restaurantFeatures.spiceLevel)?.icon }}
                      </Badge>
                    </div>
                  </div>
                </div>

                <!-- Enhanced Add Product Card -->
                <div
                  class="group relative bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-2xl border-2 border-dashed border-blue-300 dark:border-blue-600 hover:border-blue-400 dark:hover:border-blue-500 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10 hover:-translate-y-1 cursor-pointer overflow-hidden min-h-[280px]"
                  @click="createProduct"
                >
                  <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div class="relative h-full flex flex-col items-center justify-center p-6 text-center">
                    <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Plus class="w-8 h-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      Add New Product
                    </h3>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      Create a new product for your catalog
                    </p>
                  </div>
                </div>
              </div>

              <!-- Empty State -->
              <div v-if="filteredProducts.length === 0" class="text-center py-12">
                <Package class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 class="text-lg font-medium mb-2">No products found</h3>
                <p class="text-muted-foreground mb-4">
                  {{ hasActiveFilters ? 'Try adjusting your filters' : 'Create your first product to get started' }}
                </p>
                <Button @click="hasActiveFilters ? clearFilters() : createProduct()">
                  {{ hasActiveFilters ? 'Clear Filters' : 'Create Product' }}
                </Button>
              </div>
            </div>

            <!-- List View -->
            <div v-else-if="viewMode === 'list'" class="space-y-6">
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-xl font-bold tracking-tight">Products Directory</h2>
                  <p class="text-muted-foreground">
                    Comprehensive view with detailed product information
                  </p>
                </div>
                <div class="flex items-center gap-2">
                  <Badge v-if="hasActiveFilters" variant="secondary" class="px-3 py-1">
                    {{ filteredProducts.length }} of {{ productStats.total }} results
                  </Badge>
                </div>
              </div>

              <!-- Enhanced List Layout -->
              <div class="space-y-3">
                <div
                  v-for="product in filteredProducts"
                  :key="product._id"
                  class="group bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300 hover:shadow-md hover:shadow-blue-500/5 overflow-hidden"
                >
                  <div class="p-6">
                    <div class="flex items-start gap-6">
                      <!-- Enhanced Product Image -->
                      <div class="w-20 h-20 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-xl flex items-center justify-center overflow-hidden flex-shrink-0 group-hover:scale-105 transition-transform duration-300">
                        <img
                          v-if="product.thumbnail"
                          :src="product.thumbnail"
                          :alt="product.name"
                          class="w-full h-full object-cover"
                        />
                        <ImageIcon v-else class="w-8 h-8 text-gray-400 dark:text-gray-500" />
                      </div>
                      
                      <!-- Product Information -->
                      <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between mb-3">
                          <div class="flex-1 min-w-0 space-y-1">
                            <div class="flex items-center gap-3">
                              <h3 class="font-semibold text-lg text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                {{ product.name }}
                              </h3>
                              <Badge 
                                :variant="getStockStatus(product).variant" 
                                class="text-xs font-medium"
                              >
                                <component :is="getStockStatus(product).icon" class="w-3 h-3 mr-1" />
                                {{ getStockStatus(product).label }}
                              </Badge>
                            </div>
                            <p v-if="product.nameKhmer" class="text-sm text-gray-500 dark:text-gray-400">
                              {{ product.nameKhmer }}
                            </p>
                            <p v-if="product.description" class="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 max-w-2xl">
                              {{ product.description }}
                            </p>
                          </div>
                          
                          <!-- Action Menu -->
                          <DropdownMenu>
                            <DropdownMenuTrigger as-child>
                              <Button variant="ghost" size="icon" class="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity">
                                <MoreHorizontal class="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem @click="editProduct(product)">
                                <Edit class="w-4 h-4 mr-2" />
                                Edit Product
                              </DropdownMenuItem>
                              <DropdownMenuItem @click="duplicateProduct(product)">
                                <Copy class="w-4 h-4 mr-2" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuItem @click="toggleAvailability(product)">
                                <component :is="product.isAvailable ? ToggleLeft : ToggleRight" class="w-4 h-4 mr-2" />
                                {{ product.isAvailable ? 'Disable' : 'Enable' }}
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem @click="confirmDeleteProduct(product)" class="text-red-600">
                                <Trash2 class="w-4 h-4 mr-2" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        
                        <!-- Product Details Grid -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                          <!-- Price -->
                          <div class="space-y-1">
                            <p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Price</p>
                            <div class="flex items-center gap-1">
                              <DollarSign class="w-4 h-4 text-green-600 dark:text-green-400" />
                              <span class="text-sm font-semibold text-gray-900 dark:text-white">{{ formatPrice(product.price) }}</span>
                            </div>
                          </div>

                          <!-- Stock -->
                          <div v-if="!isRestaurantSubscription" class="space-y-1">
                            <p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Stock</p>
                            <div class="flex items-center gap-1">
                              <Package class="w-4 h-4 text-blue-600 dark:text-blue-400" />
                              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ product.stock }} units</span>
                            </div>
                          </div>

                          <!-- Category -->
                          <div class="space-y-1">
                            <p class="text-xs text-gray-500 dark:text-gray-400 font-medium">Category</p>
                            <div class="flex items-center gap-1">
                              <Tag class="w-4 h-4 text-purple-600 dark:text-purple-400" />
                              <span class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ getCategoryName(product.categoryId) }}</span>
                            </div>
                          </div>

                          <!-- SKU -->
                          <div class="space-y-1">
                            <p class="text-xs text-gray-500 dark:text-gray-400 font-medium">SKU</p>
                            <div class="flex items-center gap-1">
                              <BarChart3 class="w-4 h-4 text-gray-600 dark:text-gray-400" />
                              <span class="text-sm font-mono text-gray-900 dark:text-white">{{ product.sku }}</span>
                            </div>
                          </div>
                        </div>

                        <!-- Restaurant Features -->
                        <div v-if="isRestaurantSubscription && product.restaurantFeatures" class="flex flex-wrap gap-2">
                          <Badge v-if="product.restaurantFeatures.isTraditionalKhmer" variant="outline" class="text-xs bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-700">
                            <Star class="w-3 h-3 mr-1" />
                            Traditional Khmer
                          </Badge>
                          <Badge v-if="product.restaurantFeatures.isBBQItem" variant="outline" class="text-xs bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700">
                            <Zap class="w-3 h-3 mr-1" />
                            BBQ Item
                          </Badge>
                          <Badge v-if="product.restaurantFeatures.spiceLevel" variant="outline" class="text-xs bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-700">
                            {{ getSpiceLevelBadge(product.restaurantFeatures.spiceLevel)?.icon }}
                            {{ getSpiceLevelBadge(product.restaurantFeatures.spiceLevel)?.label }}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Empty State -->
              <div v-if="filteredProducts.length === 0" class="text-center py-12">
                <Package class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 class="text-lg font-medium mb-2">No products found</h3>
                <p class="text-muted-foreground mb-4">
                  {{ hasActiveFilters ? 'Try adjusting your filters' : 'Create your first product to get started' }}
                </p>
                <Button @click="hasActiveFilters ? clearFilters() : createProduct()">
                  {{ hasActiveFilters ? 'Clear Filters' : 'Create Product' }}
                </Button>
              </div>
            </div>

            <!-- Table View -->
            <div v-else-if="viewMode === 'table'" class="space-y-6">
              <div class="flex items-center justify-between">
                <div>
                  <h2 class="text-xl font-bold tracking-tight">Products Database</h2>
                  <p class="text-muted-foreground">
                    Complete data table with sorting and comprehensive product details
                  </p>
                </div>
                <div class="flex items-center gap-2">
                  <Badge v-if="hasActiveFilters" variant="secondary" class="px-3 py-1">
                    {{ filteredProducts.length }} of {{ productStats.total }} results
                  </Badge>
                </div>
              </div>

              <!-- Enhanced Table Layout -->
              <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden">
                <div class="overflow-x-auto">
                  <table class="w-full">
                    <!-- Enhanced Table Header -->
                    <thead class="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
                      <tr>
                        <th class="text-left p-4 font-semibold text-gray-900 dark:text-white">
                          <div class="flex items-center gap-2">
                            <Package class="w-4 h-4" />
                            Product
                          </div>
                        </th>
                        <th class="text-left p-4 font-semibold text-gray-900 dark:text-white">
                          <div class="flex items-center gap-2">
                            <Tag class="w-4 h-4" />
                            Category
                          </div>
                        </th>
                        <th class="text-left p-4 font-semibold text-gray-900 dark:text-white">
                          <div class="flex items-center gap-2">
                            <DollarSign class="w-4 h-4" />
                            Price
                          </div>
                        </th>
                        <th v-if="!isRestaurantSubscription" class="text-left p-4 font-semibold text-gray-900 dark:text-white">
                          <div class="flex items-center gap-2">
                            <Boxes class="w-4 h-4" />
                            Stock
                          </div>
                        </th>
                        <th class="text-left p-4 font-semibold text-gray-900 dark:text-white">
                          <div class="flex items-center gap-2">
                            <Activity class="w-4 h-4" />
                            Status
                          </div>
                        </th>
                        <th v-if="isRestaurantSubscription" class="text-left p-4 font-semibold text-gray-900 dark:text-white">
                          <div class="flex items-center gap-2">
                            <ChefHat class="w-4 h-4" />
                            Features
                          </div>
                        </th>
                        <th class="text-left p-4 font-semibold text-gray-900 dark:text-white">
                          <div class="flex items-center gap-2">
                            <Calendar class="w-4 h-4" />
                            Created
                          </div>
                        </th>
                        <th class="text-right p-4 font-semibold text-gray-900 dark:text-white">Actions</th>
                      </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                      <tr
                        v-for="product in filteredProducts"
                        :key="product._id"
                        class="group hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
                      >
                        <!-- Product Column -->
                        <td class="p-4">
                          <div class="flex items-center gap-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
                              <img
                                v-if="product.thumbnail"
                                :src="product.thumbnail"
                                :alt="product.name"
                                class="w-full h-full object-cover"
                              />
                              <ImageIcon v-else class="w-5 h-5 text-gray-400 dark:text-gray-500" />
                            </div>
                            <div class="min-w-0 space-y-1">
                              <p class="font-semibold text-sm text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                {{ product.name }}
                              </p>
                              <p v-if="product.nameKhmer" class="text-xs text-gray-500 dark:text-gray-400 truncate">
                                {{ product.nameKhmer }}
                              </p>
                              <div class="flex items-center gap-1">
                                <BarChart3 class="w-3 h-3 text-gray-400" />
                                <p class="text-xs font-mono text-gray-500 dark:text-gray-400">{{ product.sku }}</p>
                              </div>
                            </div>
                          </div>
                        </td>

                        <!-- Category Column -->
                        <td class="p-4">
                          <Badge variant="outline" class="bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 border-purple-200 dark:border-purple-700">
                            {{ getCategoryName(product.categoryId) }}
                          </Badge>
                        </td>

                        <!-- Price Column -->
                        <td class="p-4">
                          <div class="space-y-1">
                            <span class="text-sm font-bold text-gray-900 dark:text-white">{{ formatPrice(product.price) }}</span>
                            <p v-if="product.cost > 0" class="text-xs text-gray-500 dark:text-gray-400">
                              Cost: {{ formatPrice(product.cost) }}
                            </p>
                          </div>
                        </td>

                        <!-- Stock Column (for non-restaurants) -->
                        <td v-if="!isRestaurantSubscription" class="p-4">
                          <div class="space-y-1">
                            <div class="flex items-center gap-2">
                              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ product.stock }}</span>
                              <span class="text-xs text-gray-500 dark:text-gray-400">units</span>
                            </div>
                            <div v-if="product.stock <= product.minStock" class="flex items-center gap-1">
                              <AlertTriangle class="w-3 h-3 text-orange-500" />
                              <span class="text-xs text-orange-600 dark:text-orange-400 font-medium">Low Stock</span>
                            </div>
                          </div>
                        </td>

                        <!-- Status Column -->
                        <td class="p-4">
                          <Badge 
                            :variant="getStockStatus(product).variant" 
                            class="text-xs font-medium"
                          >
                            <component :is="getStockStatus(product).icon" class="w-3 h-3 mr-1" />
                            {{ getStockStatus(product).label }}
                          </Badge>
                        </td>

                        <!-- Restaurant Features Column -->
                        <td v-if="isRestaurantSubscription" class="p-4">
                          <div class="flex flex-wrap gap-1">
                            <Badge v-if="product.restaurantFeatures?.isTraditionalKhmer" variant="outline" class="text-xs bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-300 border-amber-200 dark:border-amber-700">
                              <Star class="w-3 h-3 mr-1" />
                              Khmer
                            </Badge>
                            <Badge v-if="product.restaurantFeatures?.isBBQItem" variant="outline" class="text-xs bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-red-200 dark:border-red-700">
                              <Zap class="w-3 h-3 mr-1" />
                              BBQ
                            </Badge>
                            <Badge v-if="product.restaurantFeatures?.spiceLevel" variant="outline" class="text-xs bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border-orange-200 dark:border-orange-700">
                              {{ getSpiceLevelBadge(product.restaurantFeatures.spiceLevel)?.icon }}
                            </Badge>
                          </div>
                        </td>

                        <!-- Created Column -->
                        <td class="p-4">
                          <div class="space-y-1">
                            <span class="text-sm text-gray-900 dark:text-white">
                              {{ new Date(product.createdAt).toLocaleDateString() }}
                            </span>
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                              {{ new Date(product.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) }}
                            </p>
                          </div>
                        </td>

                        <!-- Actions Column -->
                        <td class="p-4 text-right">
                          <div class="flex items-center justify-end gap-1">
                            <!-- Quick Toggle -->
                            <Button
                              variant="ghost"
                              size="icon"
                              class="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                              @click="toggleAvailability(product)"
                              :title="product.isAvailable ? 'Disable product' : 'Enable product'"
                            >
                              <component :is="product.isAvailable ? ToggleRight : ToggleLeft" class="w-4 h-4" />
                            </Button>

                            <!-- Main Actions Menu -->
                            <DropdownMenu>
                              <DropdownMenuTrigger as-child>
                                <Button variant="ghost" size="icon" class="h-8 w-8">
                                  <MoreHorizontal class="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem @click="editProduct(product)">
                                  <Edit class="w-4 h-4 mr-2" />
                                  Edit Product
                                </DropdownMenuItem>
                                <DropdownMenuItem @click="duplicateProduct(product)">
                                  <Copy class="w-4 h-4 mr-2" />
                                  Duplicate
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem @click="confirmDeleteProduct(product)" class="text-red-600">
                                  <Trash2 class="w-4 h-4 mr-2" />
                                  Delete
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <!-- Empty State -->
              <div v-if="filteredProducts.length === 0" class="text-center py-12">
                <Package class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 class="text-lg font-medium mb-2">No products found</h3>
                <p class="text-muted-foreground mb-4">
                  {{ hasActiveFilters ? 'Try adjusting your filters' : 'Create your first product to get started' }}
                </p>
                <Button @click="hasActiveFilters ? clearFilters() : createProduct()">
                  {{ hasActiveFilters ? 'Clear Filters' : 'Create Product' }}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Product Form Dialog -->
    <Dialog v-model:open="showProductDialog">
      <DialogContent class="!max-w-[95vw] !w-[95vw] max-h-[95vh] overflow-y-auto sm:!max-w-[95vw] md:!max-w-[90vw] lg:!max-w-[85vw] xl:!max-w-[80vw] 2xl:!max-w-[1400px] p-0">
        <!-- <DialogHeader>
          <DialogTitle>{{ isEditing ? 'Edit Product' : 'Create New Product' }}</DialogTitle>
          <DialogDescription>
            {{ isEditing ? 'Update the product information below.' : 'Fill in the details to create a new product.' }}
          </DialogDescription>
        </DialogHeader> -->
        <ProductForm
          :product="selectedProduct || undefined"
          :is-editing="isEditing"
          @saved="handleProductSaved"
          @cancel="handleProductCancel"
        />
      </DialogContent>
    </Dialog>

    <!-- Delete Confirmation Dialog -->
    <AlertDialog v-model:open="showDeleteDialog">
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the product
            "{{ productToDelete?.name }}" and remove it from the system.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction @click="deleteProduct" class="bg-red-600 hover:bg-red-700">
            Delete Product
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
  </div>
</template>

<style scoped>
/* Text truncation utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Enhanced hover effects */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* Smooth transitions */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-transform {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-opacity {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Backdrop blur enhancements */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Custom shadows for enhanced depth */
.shadow-blue-500\/10 {
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.1), 0 2px 4px -1px rgba(59, 130, 246, 0.06);
}

.shadow-blue-500\/5 {
  box-shadow: 0 1px 3px 0 rgba(59, 130, 246, 0.05), 0 1px 2px 0 rgba(59, 130, 246, 0.05);
}

/* Gradient backgrounds */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* Enhanced dark mode support */
@media (prefers-color-scheme: dark) {
  .dark\:group-hover\:text-blue-400:hover {
    color: rgb(96, 165, 250);
  }
}
</style> 