<template>
  <div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">{{ $t('payment.history.title') }}</h1>
        <p class="text-gray-600 dark:text-gray-400">{{ $t('payment.history.subtitle') }}</p>
      </div>

      <div class="flex items-center space-x-3">
        <button
          @click="showCreatePaymentDialog = true"
          class="btn btn-primary flex items-center space-x-2"
        >
          <DuotoneIcon name="plus" class="w-4 h-4" />
          <span>{{ $t('payment.create.title') }}</span>
        </button>

        <button
          @click="refreshData"
          :disabled="loading"
          class="btn btn-secondary flex items-center space-x-2"
        >
          <DuotoneIcon
            name="refresh"
            class="w-4 h-4"
            :class="{ 'animate-spin': loading }"
          />
          <span>{{ $t('common.refresh') }}</span>
        </button>
      </div>
    </div>

    <!-- Analytics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <PaymentAnalyticsCard
        :title="$t('payment.analytics.totalRevenue')"
        :value="formatCurrency(summary?.completedAmount || 0)"
        :change="revenueChange"
        :icon="'chart-line'"
        :loading="loading"
        color="green"
      />

      <PaymentAnalyticsCard
        :title="$t('payment.analytics.totalTransactions')"
        :value="summary?.completedCount?.toString() || '0'"
        :change="transactionChange"
        :icon="'receipt'"
        :loading="loading"
        color="blue"
      />

      <PaymentAnalyticsCard
        :title="$t('payment.analytics.successRate')"
        :value="successRate + '%'"
        :change="successRateChange"
        :icon="'check-circle'"
        :loading="loading"
        color="purple"
      />

      <PaymentAnalyticsCard
        :title="$t('payment.analytics.avgTransaction')"
        :value="formatCurrency(summary?.averageAmount || 0)"
        :change="avgTransactionChange"
        :icon="'calculator'"
        :loading="loading"
        color="orange"
      />
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Revenue Trend Chart -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('payment.analytics.revenueTrend') }}
          </h3>
          <select
            v-model="selectedPeriod"
            @change="updateTrends"
            class="select select-sm"
          >
            <option value="daily">{{ $t('common.daily') }}</option>
            <option value="monthly">{{ $t('common.monthly') }}</option>
          </select>
        </div>
        <PaymentRevenueChart
          :data="trendData"
          :loading="chartsLoading"
          :period="selectedPeriod"
        />
      </div>

      <!-- Payment Methods Breakdown -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {{ $t('payment.analytics.paymentMethods') }}
        </h3>
        <PaymentMethodsChart
          :data="summary?.methodBreakdown || {}"
          :loading="loading"
        />
      </div>
    </div>

    <!-- Filters and Search -->
    <PaymentHistoryFilters
      :filters="filters"
      :search="searchTerm"
      @update:filters="filters = $event"
      @update:search="searchTerm = $event"
      @filter-change="handleFilterChange"
      @search="handleSearch"
      @reset="resetFilters"
    />

    <!-- Payment History Table -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('payment.history.payments') }}
          </h3>

          <div class="flex items-center space-x-3">
            <span class="text-sm text-gray-500 dark:text-gray-400">
              {{ $t('payment.history.showing', {
                start: (currentPage - 1) * pageSize + 1,
                end: Math.min(currentPage * pageSize, totalCount),
                total: totalCount
              }) }}
            </span>

            <select
              v-model="pageSize"
              @change="handlePageSizeChange"
              class="select select-sm"
            >
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
      </div>

      <PaymentHistoryTable
        :payments="payments"
        :loading="loading"
        :sort-field="sortField"
        :sort-order="sortOrder"
        @sort="handleSort"
        @view-details="showPaymentDetails"
        @simulate-success="handleSimulateSuccess"
      />

      <!-- Pagination -->
      <div class="p-6 border-t border-gray-200 dark:border-gray-700">
        <PaymentHistoryPagination
          :current-page="currentPage"
          :total-pages="totalPages"
          :has-next="hasNext"
          :has-prev="hasPrev"
          @page-change="handlePageChange"
        />
      </div>
    </div>

    <!-- Create Payment Dialog -->
    <CreatePaymentDialog
      :open="showCreatePaymentDialog"
      @update:open="showCreatePaymentDialog = $event"
      @payment-created="handlePaymentCreated"
    />

    <!-- Payment Details Dialog -->
    <PaymentDetailsDialog
      :open="showPaymentDetailsDialog"
      @update:open="showPaymentDetailsDialog = $event"
      :payment="selectedPayment"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { storeToRefs } from 'pinia'
import { useCompanyStore } from '@/stores/company'
import { paymentService } from '@/services/payment.service'
import type {
  PaymentHistory,
  PaymentHistoryFilter,
  PaymentSummary,
  PaymentAnalytics
} from '@/types/payment'

// Components
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import PaymentAnalyticsCard from '@/components/payment/PaymentAnalyticsCard.vue'
import PaymentRevenueChart from '@/components/payment/PaymentRevenueChart.vue'
import PaymentMethodsChart from '@/components/payment/PaymentMethodsChart.vue'
import PaymentHistoryFilters from '@/components/payment/PaymentHistoryFilters.vue'
import PaymentHistoryTable from '@/components/payment/PaymentHistoryTable.vue'
import PaymentHistoryPagination from '@/components/payment/PaymentHistoryPagination.vue'
import CreatePaymentDialog from '@/components/payment/CreatePaymentDialog.vue'
import PaymentDetailsDialog from '@/components/payment/PaymentDetailsDialog.vue'

const { t } = useI18n()
const companyStore = useCompanyStore()
const { currentCompany } = storeToRefs(companyStore)

// State
const loading = ref(false)
const chartsLoading = ref(false)
const payments = ref<PaymentHistory[]>([])
const summary = ref<PaymentSummary | null>(null)
const analytics = ref<PaymentAnalytics | null>(null)
const trendData = ref<any[]>([])

// Pagination
const currentPage = ref(1)
const totalPages = ref(1)
const totalCount = ref(0)
const pageSize = ref(25)
const hasNext = ref(false)
const hasPrev = ref(false)

// Filters and Search
const filters = ref<PaymentHistoryFilter>({})
const searchTerm = ref('')
const sortField = ref<'createdAt' | 'amount' | 'status' | 'paidAt' | 'module'>('createdAt')
const sortOrder = ref<'asc' | 'desc'>('desc')

// Analytics
const selectedPeriod = ref<'daily' | 'monthly'>('monthly')
const revenueChange = ref(0)
const transactionChange = ref(0)
const successRateChange = ref(0)
const avgTransactionChange = ref(0)

// Dialogs
const showCreatePaymentDialog = ref(false)
const showPaymentDetailsDialog = ref(false)
const selectedPayment = ref<PaymentHistory | null>(null)

// Computed
const successRate = computed(() => {
  if (!summary.value) return 0
  const { completedCount, totalCount } = summary.value
  return totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0
})

// Methods
const formatCurrency = (amount: number, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(amount)
}

const loadPaymentHistory = async () => {
  if (!currentCompany.value?.id) return

  loading.value = true
  try {
    const query = {
      filter: {
        companyId: currentCompany.value.id,
        ...filters.value,
        ...(searchTerm.value && { searchTerm: searchTerm.value })
      },
      sort: {
        field: sortField.value,
        order: sortOrder.value
      },
      pagination: {
        page: currentPage.value,
        limit: pageSize.value
      }
    }

    const response = await paymentService.getAdvancedPaymentHistory(query)

    payments.value = response.payments
    summary.value = response.summary
    totalPages.value = response.totalPages
    totalCount.value = response.totalCount
    hasNext.value = response.hasNext
    hasPrev.value = response.hasPrev
  } catch (error) {
    console.error('Failed to load payment history:', error)
  } finally {
    loading.value = false
  }
}

const loadAnalytics = async () => {
  if (!currentCompany.value?.id) return

  try {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    analytics.value = await paymentService.getPaymentAnalytics(
      currentCompany.value.id,
      thirtyDaysAgo.toISOString().split('T')[0],
      new Date().toISOString().split('T')[0]
    )

    // Calculate changes (mock for now)
    revenueChange.value = Math.random() * 20 - 10 // -10% to +10%
    transactionChange.value = Math.random() * 30 - 15
    successRateChange.value = Math.random() * 10 - 5
    avgTransactionChange.value = Math.random() * 15 - 7.5
  } catch (error) {
    console.error('Failed to load analytics:', error)
  }
}

const updateTrends = async () => {
  if (!currentCompany.value?.id) return

  chartsLoading.value = true
  try {
    const response = await paymentService.getPaymentTrends(
      currentCompany.value.id,
      { period: selectedPeriod.value }
    )

    trendData.value = response.trends.map(item => ({
      period: item.date || item.month,
      revenue: item.revenue,
      transactions: item.transactions
    }))
  } catch (error) {
    console.error('Failed to load trends:', error)
  } finally {
    chartsLoading.value = false
  }
}

const handleFilterChange = () => {
  currentPage.value = 1
  loadPaymentHistory()
}

const handleSearch = () => {
  currentPage.value = 1
  loadPaymentHistory()
}

const resetFilters = () => {
  filters.value = {}
  searchTerm.value = ''
  currentPage.value = 1
  loadPaymentHistory()
}

const handleSort = (field: string, order: 'asc' | 'desc') => {
  sortField.value = field as any
  sortOrder.value = order
  loadPaymentHistory()
}

const handlePageChange = (page: number) => {
  currentPage.value = page
  loadPaymentHistory()
}

const handlePageSizeChange = () => {
  currentPage.value = 1
  loadPaymentHistory()
}

const showPaymentDetails = (payment: PaymentHistory) => {
  selectedPayment.value = payment
  showPaymentDetailsDialog.value = true
}

const handleSimulateSuccess = async (paymentId: string) => {
  try {
    await paymentService.simulatePaymentSuccess(paymentId)
    await loadPaymentHistory()
  } catch (error) {
    console.error('Failed to simulate payment success:', error)
  }
}

const handlePaymentCreated = () => {
  showCreatePaymentDialog.value = false
  loadPaymentHistory()
}

const refreshData = async () => {
  await Promise.all([
    loadPaymentHistory(),
    loadAnalytics(),
    updateTrends()
  ])
}

// Watch for company changes
watch(currentCompany, (newCompany) => {
  if (newCompany?.id) {
    refreshData()
  }
}, { immediate: true })

// Load data on mount
onMounted(() => {
  if (currentCompany.value?.id) {
    refreshData()
  }
})
</script>
