<script setup lang="ts">
import { ref } from 'vue'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'

const tasks = ref([
  {
    id: 1,
    title: 'Design System Implementation',
    description: 'Implement the new design system across all components',
    project: 'E-commerce Dashboard',
    priority: 'High',
    dueDate: '2024-03-10',
    completed: false,
    assignee: '<PERSON>',
  },
  {
    id: 2,
    title: 'API Documentation',
    description: 'Write comprehensive documentation for the new API endpoints',
    project: 'API Integration',
    priority: 'Medium',
    dueDate: '2024-03-12',
    completed: true,
    assignee: '<PERSON>',
  },
  {
    id: 3,
    title: 'User Testing',
    description: 'Conduct user testing sessions for the new features',
    project: 'Mobile App Redesign',
    priority: 'High',
    dueDate: '2024-03-15',
    completed: false,
    assignee: '<PERSON>',
  },
])

const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return 'bg-red-100 text-red-800'
    case 'medium':
      return 'bg-yellow-100 text-yellow-800'
    case 'low':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const toggleTask = (taskId: number) => {
  const task = tasks.value.find((t) => t.id === taskId)
  if (task) {
    task.completed = !task.completed
  }
}
</script>

<template>
  <div class="py-6">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-semibold text-gray-900">Tasks</h1>
        <Button>
          <DuotoneIcon name="plus" size="sm" class="mr-2" />
          New Task
        </Button>
      </div>
    </div>

    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
      <div class="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>All Tasks</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="task in tasks"
                :key="task.id"
                class="flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50"
              >
                <Checkbox
                  :checked="task.completed"
                  @update:checked="toggleTask(task.id)"
                  class="mt-1"
                />

                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <p
                      class="text-sm font-medium text-gray-900"
                      :class="{ 'line-through': task.completed }"
                    >
                      {{ task.title }}
                    </p>
                    <Badge :class="getPriorityColor(task.priority)">
                      {{ task.priority }}
                    </Badge>
                  </div>
                  <p class="mt-1 text-sm text-gray-500" :class="{ 'line-through': task.completed }">
                    {{ task.description }}
                  </p>
                  <div class="mt-2 flex items-center space-x-4 text-sm">
                    <div class="flex items-center text-muted-foreground">
                      <DuotoneIcon name="folder" size="sm" class="mr-1" />
                      {{ task.project }}
                    </div>
                    <div class="flex items-center text-muted-foreground">
                      <DuotoneIcon name="user" size="sm" class="mr-1" />
                      {{ task.assignee }}
                    </div>
                    <div class="flex items-center text-muted-foreground">
                      <DuotoneIcon name="calendar" size="sm" class="mr-1" />
                      {{ new Date(task.dueDate).toLocaleDateString() }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>
