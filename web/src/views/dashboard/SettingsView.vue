<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import ThemeSwitcher from '@/components/ThemeSwitcher.vue'
import { useLanguage } from '@/composables/useLanguage'
import { useTheme } from '@/composables/useTheme'

const router = useRouter()
const { t } = useLanguage()
const { theme, currentTheme } = useTheme()

// Company Profile Settings
const companyName = ref('ElyPOS Company')
const companyEmail = ref('<EMAIL>')
const companyPhone = ref('+****************')
const companyAddress = ref('123 Business Street, City, State 12345')
const companyDescription = ref('A leading provider of business management solutions.')

// User Profile Settings
const name = ref('John Doe')
const email = ref('<EMAIL>')
const role = ref('admin')

// Notification Settings
const notifications = ref({
  email: true,
  push: true,
  weekly: false,
  marketing: false,
  security: true,
  billing: true
})

// Security Settings
const security = ref({
  twoFactor: false,
  sessionTimeout: '30',
  loginAlerts: true,
  dataExport: false
})

// Integration Settings
const integrations = ref({
  acleda: false,
  stripe: true,
  zapier: false,
  slack: false
})

const activeTab = ref('company')

const handleLogout = () => {
  localStorage.removeItem('token')
  router.push('/auth/login')
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
      <!-- Header -->
      <div class="mb-8">
        <div class="flex items-center gap-3 mb-4">
          <div class="w-12 h-12 bg-gray-600 rounded-2xl flex items-center justify-center">
            <DuotoneIcon name="settings" color="white" size="lg" />
          </div>
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Settings</h1>
            <p class="text-lg text-gray-600">Manage your company and account preferences</p>
          </div>
        </div>
    </div>

      <!-- Settings Tabs -->
      <Tabs v-model="activeTab" class="space-y-6">
        <TabsList class="grid w-full grid-cols-6 lg:w-auto lg:grid-cols-6">
          <TabsTrigger value="company" class="gap-2">
            <DuotoneIcon name="building" size="sm" />
            <span class="hidden sm:inline">Company</span>
          </TabsTrigger>
          <TabsTrigger value="users" class="gap-2">
            <DuotoneIcon name="users" size="sm" />
            <span class="hidden sm:inline">Users</span>
          </TabsTrigger>
          <TabsTrigger value="billing" class="gap-2">
            <DuotoneIcon name="credit-card" size="sm" />
            <span class="hidden sm:inline">Billing</span>
          </TabsTrigger>
          <TabsTrigger value="security" class="gap-2">
            <DuotoneIcon name="shield-check" size="sm" />
            <span class="hidden sm:inline">Security</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" class="gap-2">
            <DuotoneIcon name="bell" size="sm" />
            <span class="hidden sm:inline">Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="integrations" class="gap-2">
            <DuotoneIcon name="plug" size="sm" />
            <span class="hidden sm:inline">Integrations</span>
          </TabsTrigger>
        </TabsList>

        <!-- Company Profile Tab -->
        <TabsContent value="company">
          <div class="space-y-6">
        <Card>
          <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <DuotoneIcon name="building" color="blue" size="sm" />
                  Company Information
                </CardTitle>
                <CardDescription>Update your company details and business information</CardDescription>
          </CardHeader>
          <CardContent>
            <form class="space-y-4">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="space-y-2">
                      <Label for="company-name">Company Name</Label>
                      <Input id="company-name" v-model="companyName" placeholder="Enter company name" />
                    </div>

                    <div class="space-y-2">
                      <Label for="business-email">Business Email</Label>
                      <Input id="business-email" v-model="companyEmail" type="email" placeholder="Enter business email" />
                    </div>

                    <div class="space-y-2">
                      <Label for="phone-number">Phone Number</Label>
                      <Input id="phone-number" v-model="companyPhone" placeholder="Enter phone number" />
                    </div>

                    <div class="space-y-2">
                      <Label for="business-type">Business Type</Label>
                      <Select>
                        <SelectTrigger id="business-type">
                          <SelectValue placeholder="Select business type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="restaurant">Restaurant</SelectItem>
                          <SelectItem value="retail">Retail</SelectItem>
                          <SelectItem value="services">Services</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div class="space-y-2">
                    <Label for="business-address">Business Address</Label>
                    <Textarea id="business-address" v-model="companyAddress" placeholder="Enter complete business address" />
                  </div>

                  <div class="space-y-2">
                    <Label for="company-description">Company Description</Label>
                    <Textarea id="company-description" v-model="companyDescription" placeholder="Describe your business" />
                  </div>

                  <Button class="bg-blue-600 hover:bg-blue-700">Save Company Information</Button>
            </form>
          </CardContent>
        </Card>
          </div>
        </TabsContent>

        <!-- Users & Roles Tab -->
        <TabsContent value="users">
          <div class="space-y-6">
        <Card>
          <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <DuotoneIcon name="users" color="green" size="sm" />
                  User Management
                </CardTitle>
                <CardDescription>Manage users, roles, and permissions</CardDescription>
          </CardHeader>
          <CardContent>
                <div class="space-y-6">
                  <!-- Current User Profile -->
                  <div class="border rounded-lg p-4">
                    <h3 class="font-semibold mb-4">Your Profile</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div class="space-y-2">
                        <Label for="user-name">Full Name</Label>
                        <Input id="user-name" v-model="name" placeholder="Enter your name" />
                      </div>

                      <div class="space-y-2">
                        <Label for="user-email">Email</Label>
                        <Input id="user-email" v-model="email" type="email" placeholder="Enter your email" />
                      </div>

                      <div class="space-y-2">
                        <Label>Role</Label>
                        <div class="flex items-center gap-2">
                          <Badge variant="secondary" class="bg-green-100 text-green-700">{{ role.toUpperCase() }}</Badge>
                          <span class="text-sm text-gray-500">Administrator</span>
                        </div>
                      </div>
                    </div>
                    <Button class="mt-4 bg-green-600 hover:bg-green-700">Update Profile</Button>
                  </div>

                  <!-- Team Members -->
                  <div>
                    <div class="flex items-center justify-between mb-4">
                      <h3 class="font-semibold">Team Members</h3>
                      <Button variant="outline">
                        <DuotoneIcon name="plus" size="sm" class="mr-2" />
                        Invite User
                      </Button>
                    </div>
                    <div class="border rounded-lg">
                      <div class="p-4 text-center text-gray-500">
                        <DuotoneIcon name="users" color="gray" size="lg" class="mx-auto mb-2" />
                        <p>No team members yet. Invite users to collaborate.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <!-- Billing Tab -->
        <TabsContent value="billing">
          <div class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <DuotoneIcon name="credit-card" color="purple" size="sm" />
                  Billing & Subscriptions
                </CardTitle>
                <CardDescription>Manage your billing information and subscriptions</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <!-- Current Plan -->
                  <div class="border rounded-lg p-4 bg-purple-50">
                    <div class="flex items-center justify-between">
                      <div>
                        <h3 class="font-semibold text-purple-900">Current Plan</h3>
                        <p class="text-purple-700">Professional Plan - $99/month</p>
                      </div>
                      <Badge class="bg-purple-600 text-white">Active</Badge>
                    </div>
                  </div>

                  <!-- Payment Method -->
                  <div>
                    <h3 class="font-semibold mb-3">Payment Method</h3>
                    <div class="border rounded-lg p-4">
                      <div class="flex items-center gap-3">
                        <DuotoneIcon name="credit-card" color="purple" size="sm" />
                        <div>
                          <p class="font-medium">•••• •••• •••• 4242</p>
                          <p class="text-sm text-gray-500">Expires 12/25</p>
                        </div>
                      </div>
                    </div>
                    <Button variant="outline" class="mt-3">Update Payment Method</Button>
                  </div>

                  <!-- Billing History -->
                  <div>
                    <h3 class="font-semibold mb-3">Recent Invoices</h3>
                    <div class="border rounded-lg">
                      <div class="p-4 text-center text-gray-500">
                        <DuotoneIcon name="receipt" color="gray" size="lg" class="mx-auto mb-2" />
                        <p>No recent invoices to display.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <!-- Security Tab -->
        <TabsContent value="security">
          <div class="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <DuotoneIcon name="shield-check" color="red" size="sm" />
                  Security Settings
                </CardTitle>
                <CardDescription>Configure security options and access controls</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <!-- Two-Factor Authentication -->
                  <div class="flex items-center justify-between p-4 border rounded-lg">
                    <div class="space-y-0.5">
                      <div class="text-sm font-medium text-gray-900">Two-Factor Authentication</div>
                      <div class="text-sm text-gray-500">Add an extra layer of security to your account</div>
                    </div>
                    <Switch v-model="security.twoFactor" />
                  </div>

                  <!-- Session Timeout -->
                  <div class="space-y-3">
                    <label class="text-sm font-medium text-gray-900">Session Timeout</label>
                    <Select v-model="security.sessionTimeout">
                      <SelectTrigger class="w-full">
                        <SelectValue placeholder="Select timeout duration" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="15">15 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="60">1 hour</SelectItem>
                        <SelectItem value="240">4 hours</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <!-- Login Alerts -->
                  <div class="flex items-center justify-between p-4 border rounded-lg">
                    <div class="space-y-0.5">
                      <div class="text-sm font-medium text-gray-900">Login Alerts</div>
                      <div class="text-sm text-gray-500">Get notified of new login attempts</div>
                    </div>
                    <Switch v-model="security.loginAlerts" />
                  </div>

                  <!-- Data Export -->
                  <div class="space-y-3">
                    <h3 class="font-semibold text-red-900">Data Management</h3>
                    <div class="p-4 border border-red-200 rounded-lg bg-red-50">
                      <div class="space-y-3">
                        <div>
                          <h4 class="text-sm font-medium text-red-900">Export Your Data</h4>
                          <p class="text-sm text-red-700">Download a copy of all your data</p>
                          <Button variant="outline" class="mt-2 border-red-300 text-red-700 hover:bg-red-100">
                            Request Data Export
                          </Button>
                        </div>
                        <div>
                          <h4 class="text-sm font-medium text-red-900">Delete Account</h4>
                          <p class="text-sm text-red-700">Permanently delete your account and all data</p>
                          <Button variant="destructive" class="mt-2">Delete Account</Button>
                        </div>
                      </div>
                </div>
              </div>

                  <Button class="bg-red-600 hover:bg-red-700">Save Security Settings</Button>
            </div>
          </CardContent>
        </Card>
          </div>
        </TabsContent>

        <!-- Notifications Tab -->
        <TabsContent value="notifications">
          <div class="space-y-6">
        <Card>
          <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <DuotoneIcon name="bell" color="orange" size="sm" />
                  Notification Preferences
                </CardTitle>
                <CardDescription>Control how and when you receive notifications</CardDescription>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
                  <!-- Email Notifications -->
                  <div class="flex items-center justify-between p-4 border rounded-lg">
                <div class="space-y-0.5">
                  <div class="text-sm font-medium text-gray-900">Email Notifications</div>
                      <div class="text-sm text-gray-500">Receive notifications via email</div>
                </div>
                <Switch v-model="notifications.email" />
              </div>

                  <!-- Push Notifications -->
                  <div class="flex items-center justify-between p-4 border rounded-lg">
                <div class="space-y-0.5">
                  <div class="text-sm font-medium text-gray-900">Push Notifications</div>
                      <div class="text-sm text-gray-500">Receive push notifications in your browser</div>
                </div>
                <Switch v-model="notifications.push" />
              </div>

                  <!-- Security Alerts -->
                  <div class="flex items-center justify-between p-4 border rounded-lg">
                    <div class="space-y-0.5">
                      <div class="text-sm font-medium text-gray-900">Security Alerts</div>
                      <div class="text-sm text-gray-500">Get notified of security events</div>
                    </div>
                    <Switch v-model="notifications.security" />
                  </div>

                  <!-- Billing Notifications -->
                  <div class="flex items-center justify-between p-4 border rounded-lg">
                <div class="space-y-0.5">
                      <div class="text-sm font-medium text-gray-900">Billing Notifications</div>
                      <div class="text-sm text-gray-500">Get notified of billing events and payments</div>
                    </div>
                    <Switch v-model="notifications.billing" />
                  </div>

                  <!-- Weekly Digest -->
                  <div class="flex items-center justify-between p-4 border rounded-lg">
                    <div class="space-y-0.5">
                      <div class="text-sm font-medium text-gray-900">Weekly Digest</div>
                      <div class="text-sm text-gray-500">Get a weekly summary of your activity</div>
                </div>
                <Switch v-model="notifications.weekly" />
              </div>

                  <!-- Marketing Emails -->
                  <div class="flex items-center justify-between p-4 border rounded-lg">
                <div class="space-y-0.5">
                  <div class="text-sm font-medium text-gray-900">Marketing Emails</div>
                      <div class="text-sm text-gray-500">Receive updates about new features and promotions</div>
                    </div>
                    <Switch v-model="notifications.marketing" />
                  </div>

                  <Button class="bg-orange-600 hover:bg-orange-700">Save Notification Preferences</Button>
            </div>
          </CardContent>
        </Card>
          </div>
        </TabsContent>

        <!-- Integrations Tab -->
        <TabsContent value="integrations">
          <div class="space-y-6">
        <Card>
          <CardHeader>
                <CardTitle class="flex items-center gap-2">
                  <DuotoneIcon name="plug" color="indigo" size="sm" />
                  Third-Party Integrations
                </CardTitle>
                <CardDescription>Connect your account with external services and APIs</CardDescription>
          </CardHeader>
          <CardContent>
                <div class="space-y-6">
                  <!-- Payment Integrations -->
                  <div>
                    <h3 class="font-semibold mb-4">Payment Processors</h3>
                    <div class="space-y-3">
                      <!-- ACLEDA Bank -->
                      <div class="flex items-center justify-between p-4 border rounded-lg">
                        <div class="flex items-center gap-3">
                          <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <DuotoneIcon name="bank" color="blue" size="sm" />
                          </div>
                          <div>
                            <div class="font-medium">ACLEDA Bank</div>
                            <div class="text-sm text-gray-500">Local payment processing for Cambodia</div>
                          </div>
                        </div>
                        <div class="flex items-center gap-2">
                          <Badge v-if="integrations.acleda" class="bg-green-100 text-green-700">Connected</Badge>
                          <Badge v-else variant="secondary">Not Connected</Badge>
                          <Switch v-model="integrations.acleda" />
                        </div>
                      </div>

                      <!-- Stripe -->
                      <div class="flex items-center justify-between p-4 border rounded-lg">
                        <div class="flex items-center gap-3">
                          <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <DuotoneIcon name="credit-card" color="purple" size="sm" />
                          </div>
                          <div>
                            <div class="font-medium">Stripe</div>
                            <div class="text-sm text-gray-500">International payment processing</div>
                          </div>
                        </div>
                        <div class="flex items-center gap-2">
                          <Badge v-if="integrations.stripe" class="bg-green-100 text-green-700">Connected</Badge>
                          <Badge v-else variant="secondary">Not Connected</Badge>
                          <Switch v-model="integrations.stripe" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Productivity Integrations -->
                  <div>
                    <h3 class="font-semibold mb-4">Productivity Tools</h3>
                    <div class="space-y-3">
                      <!-- Zapier -->
                      <div class="flex items-center justify-between p-4 border rounded-lg">
                        <div class="flex items-center gap-3">
                          <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                            <DuotoneIcon name="bolt" color="orange" size="sm" />
                          </div>
                          <div>
                            <div class="font-medium">Zapier</div>
                            <div class="text-sm text-gray-500">Automate workflows between apps</div>
                          </div>
                        </div>
                        <div class="flex items-center gap-2">
                          <Badge v-if="integrations.zapier" class="bg-green-100 text-green-700">Connected</Badge>
                          <Badge v-else variant="secondary">Not Connected</Badge>
                          <Switch v-model="integrations.zapier" />
                        </div>
                      </div>

                      <!-- Slack -->
                      <div class="flex items-center justify-between p-4 border rounded-lg">
                        <div class="flex items-center gap-3">
                          <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <DuotoneIcon name="message-circle" color="green" size="sm" />
                          </div>
              <div>
                            <div class="font-medium">Slack</div>
                            <div class="text-sm text-gray-500">Team communication and notifications</div>
                          </div>
                        </div>
                        <div class="flex items-center gap-2">
                          <Badge v-if="integrations.slack" class="bg-green-100 text-green-700">Connected</Badge>
                          <Badge v-else variant="secondary">Not Connected</Badge>
                          <Switch v-model="integrations.slack" />
                        </div>
                      </div>
                </div>
              </div>

                  <!-- API Access -->
                  <div>
                    <h3 class="font-semibold mb-4">API Access</h3>
                    <div class="p-4 border rounded-lg bg-indigo-50">
                      <div class="flex items-center gap-3 mb-3">
                        <DuotoneIcon name="code" color="indigo" size="sm" />
              <div>
                          <div class="font-medium text-indigo-900">Developer API</div>
                          <div class="text-sm text-indigo-700">Integrate ElyPOS with your custom applications</div>
                        </div>
                      </div>
                      <div class="flex gap-2">
                        <Button variant="outline" class="border-indigo-300 text-indigo-700 hover:bg-indigo-100">
                          View API Documentation
                        </Button>
                        <Button variant="outline" class="border-indigo-300 text-indigo-700 hover:bg-indigo-100">
                          Generate API Key
                        </Button>
                </div>
              </div>
                  </div>

                  <Button class="bg-indigo-600 hover:bg-indigo-700">Save Integration Settings</Button>
            </div>
          </CardContent>
        </Card>
          </div>
        </TabsContent>
      </Tabs>

      <!-- Quick Actions -->
      <div class="mt-12 pt-8 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500">
            Settings last updated: {{ new Date().toLocaleDateString() }}
          </div>
          <div class="flex gap-3">
            <ThemeSwitcher />
            <Button variant="outline" @click="handleLogout">
              <DuotoneIcon name="log-out" size="sm" class="mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
