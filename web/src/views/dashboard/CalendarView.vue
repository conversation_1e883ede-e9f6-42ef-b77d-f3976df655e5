<script setup lang="ts">
import { ref } from 'vue'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const events = ref([
  {
    id: 1,
    title: 'Team Meeting',
    description: 'Weekly team sync',
    startTime: '09:00',
    endTime: '10:00',
    date: '2024-03-08',
    type: 'meeting',
    attendees: ['<PERSON>', '<PERSON>', '<PERSON>'],
  },
  {
    id: 2,
    title: 'Project Review',
    description: 'E-commerce Dashboard review',
    startTime: '14:00',
    endTime: '15:30',
    date: '2024-03-08',
    type: 'review',
    attendees: ['<PERSON>', '<PERSON>'],
  },
  {
    id: 3,
    title: 'Client Call',
    description: 'Discussion about new requirements',
    startTime: '11:00',
    endTime: '12:00',
    date: '2024-03-08',
    type: 'call',
    attendees: ['<PERSON>', 'Client Name'],
  },
])

const getEventTypeColor = (type: string) => {
  switch (type.toLowerCase()) {
    case 'meeting':
      return 'bg-blue-100 text-blue-800'
    case 'review':
      return 'bg-purple-100 text-purple-800'
    case 'call':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatTime = (time: string) => {
  return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: 'numeric',
    hour12: true,
  })
}
</script>

<template>
  <div class="py-6">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
      <div class="flex items-center justify-between">
        <h1 class="text-2xl font-semibold text-gray-900">Calendar</h1>
        <div class="flex items-center space-x-4">
          <Button variant="outline">
            <DuotoneIcon name="chevron-left" size="sm" class="mr-2" />
            Previous
          </Button>
          <Button variant="outline"> Today </Button>
          <Button variant="outline">
            Next
            <DuotoneIcon name="chevron-right" size="sm" class="ml-2" />
          </Button>
          <Button>
            <DuotoneIcon name="plus" size="sm" class="mr-2" />
            New Event
          </Button>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-7xl px-4 sm:px-6 md:px-8">
      <div class="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Today's Schedule</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-6">
              <div
                v-for="event in events"
                :key="event.id"
                class="flex space-x-4 p-4 rounded-lg hover:bg-gray-50"
              >
                <div class="flex-none w-16 text-sm text-muted-foreground">
                  {{ formatTime(event.startTime) }}
                </div>

                <div class="flex-1">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                      <h3 class="text-sm font-medium text-gray-900">
                        {{ event.title }}
                      </h3>
                      <Badge :class="getEventTypeColor(event.type)">
                        {{ event.type }}
                      </Badge>
                    </div>
                    <span class="text-sm text-muted-foreground">
                      {{ formatTime(event.endTime) }}
                    </span>
                  </div>

                  <p class="mt-1 text-sm text-gray-500">
                    {{ event.description }}
                  </p>

                  <div class="mt-2 flex items-center space-x-4">
                    <div class="flex items-center text-sm text-muted-foreground">
                      <DuotoneIcon name="users" size="sm" class="mr-1" />
                      {{ event.attendees.length }} attendees
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>
