<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { priceLevelService } from '@/services/price-level.service'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import {
  ArrowLeft,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  Settings,
  Crown,
  GripVertical,
  Percent,
  Star,
  Copy,
  Eye,
  EyeOff,
  RefreshCw,
  Download,
  Upload,
  AlertCircle,
  BookOpen,
  Layers
} from 'lucide-vue-next'
import type { PriceLevel } from '../../../../shared/types/pos'

type CreatePriceLevelRequest = Omit<PriceLevel, '_id' | 'companyId' | 'createdAt' | 'updatedAt' | 'isActive'>

const router = useRouter()
const authStore = useAuthStore()

// State
const priceLevels = ref<PriceLevel[]>([])
const isLoading = ref(true)
const isRefreshing = ref(false)
const error = ref('')

// Dialog states
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDeleteDialog = ref(false)
const selectedPriceLevel = ref<PriceLevel | null>(null)

// UI Enhancement states
const showSpeedDial = ref(false)
const showPerformanceToast = ref(false)
const loadTime = ref(250)

// Form states
const createForm = ref<CreatePriceLevelRequest>({
  name: '',
  description: '',
  code: '',
  priority: 0,
  discountPercentage: 0,
  color: '#3B82F6',
  isDefault: false
})

const editForm = ref<CreatePriceLevelRequest>({
  name: '',
  description: '',
  code: '',
  priority: 0,
  discountPercentage: 0,
  color: '#3B82F6',
  isDefault: false
})

const companyId = computed(() => authStore.currentUser?.currentCompanyId || '')

// Computed properties
const priceLevelStats = computed(() => {
  const total = priceLevels.value.length
  const defaultLevel = priceLevels.value.find(level => level.isDefault)
  const discountLevels = priceLevels.value.filter(level => level.discountPercentage && level.discountPercentage > 0).length
  const maxDiscount = Math.max(...priceLevels.value.map(level => level.discountPercentage || 0))

  return {
    total,
    defaultLevel,
    discountLevels,
    maxDiscount
  }
})

const predefinedColors = [
  '#3B82F6', // Blue
  '#10B981', // Emerald
  '#F59E0B', // Amber
  '#EF4444', // Red
  '#8B5CF6', // Violet
  '#EC4899', // Pink
  '#14B8A6', // Teal
  '#F97316', // Orange
  '#84CC16', // Lime
  '#6366F1'  // Indigo
]

// Navigation functions
const goBack = () => {
  router.back()
}

// Data loading
const loadPriceLevels = async () => {
  if (!companyId.value) return

  try {
    isLoading.value = true
    error.value = ''

    const initialLevelsCount = priceLevels.value.length
    const data = await priceLevelService.getPriceLevels(companyId.value)
    priceLevels.value = data

        // Show notification if default levels were just created
    if (initialLevelsCount === 0 && data.length === 5) {
      console.log('✅ Complete price level suite (Customer, Guest, Staff, VIP Member, Wholesale) created automatically')

      // Show a brief success toast
      setTimeout(() => {
        showPerformanceToast.value = false
        // You could show a different success message here if desired
      }, 3000)
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load price levels'
    console.error('Failed to load price levels:', err)
  } finally {
    isLoading.value = false
  }
}

const refreshData = async () => {
  isRefreshing.value = true
  await loadPriceLevels()
  isRefreshing.value = false
}

// CRUD operations
const handleCreatePriceLevel = async () => {
  if (!companyId.value || !createForm.value.name.trim() || !createForm.value.code.trim()) return

  try {
    await priceLevelService.createPriceLevel(companyId.value, {
      name: createForm.value.name.trim(),
      description: createForm.value.description?.trim(),
      code: createForm.value.code.trim().toUpperCase(),
      priority: createForm.value.priority,
      discountPercentage: createForm.value.discountPercentage,
      color: createForm.value.color,
      isDefault: createForm.value.isDefault
    })

    // Reset form and reload
    resetCreateForm()
    showCreateDialog.value = false
    await loadPriceLevels()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to create price level'
  }
}

const handleEditPriceLevel = async () => {
  if (!companyId.value || !selectedPriceLevel.value || !editForm.value.name.trim()) return

  try {
    await priceLevelService.updatePriceLevel(companyId.value, selectedPriceLevel.value._id, {
      name: editForm.value.name.trim(),
      description: editForm.value.description?.trim(),
      code: editForm.value.code?.trim().toUpperCase(),
      priority: editForm.value.priority,
      discountPercentage: editForm.value.discountPercentage,
      color: editForm.value.color,
      isDefault: editForm.value.isDefault
    })

    showEditDialog.value = false
    selectedPriceLevel.value = null
    await loadPriceLevels()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to update price level'
  }
}

const handleDeletePriceLevel = async () => {
  if (!companyId.value || !selectedPriceLevel.value) return

  try {
    await priceLevelService.deletePriceLevel(companyId.value, selectedPriceLevel.value._id)

    showDeleteDialog.value = false
    selectedPriceLevel.value = null
    await loadPriceLevels()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to delete price level'
  }
}

// Dialog helpers
const openEditDialog = (priceLevel: PriceLevel) => {
  selectedPriceLevel.value = priceLevel
  editForm.value = {
    name: priceLevel.name,
    description: priceLevel.description || '',
    code: priceLevel.code,
    priority: priceLevel.priority,
    discountPercentage: priceLevel.discountPercentage || 0,
    color: priceLevel.color || '#3B82F6',
    isDefault: priceLevel.isDefault
  }
  showEditDialog.value = true
}

const openDeleteDialog = (priceLevel: PriceLevel) => {
  selectedPriceLevel.value = priceLevel
  showDeleteDialog.value = true
}

const resetCreateForm = () => {
  createForm.value = {
    name: '',
    description: '',
    code: '',
    priority: priceLevels.value.length,
    discountPercentage: 0,
    color: predefinedColors[Math.floor(Math.random() * predefinedColors.length)],
    isDefault: false
  }
}

const duplicatePriceLevel = (priceLevel: PriceLevel) => {
  createForm.value = {
    name: `${priceLevel.name} Copy`,
    description: priceLevel.description,
    code: `${priceLevel.code}_COPY`,
    priority: priceLevels.value.length,
    discountPercentage: priceLevel.discountPercentage,
    color: priceLevel.color,
    isDefault: false
  }
  showCreateDialog.value = true
}

const setAsDefault = async (priceLevel: PriceLevel) => {
  if (!companyId.value || priceLevel.isDefault) return

  try {
    await priceLevelService.updatePriceLevel(companyId.value, priceLevel._id, {
      isDefault: true
    })
    await loadPriceLevels()
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to set as default'
  }
}

const generateCode = () => {
  const name = createForm.value.name.trim()
  if (name) {
    createForm.value.code = name
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '_')
      .replace(/_+/g, '_')
      .replace(/^_|_$/g, '')
      .substring(0, 10)
  }
}

onMounted(() => {
  const startTime = performance.now()
  loadPriceLevels().then(() => {
    const endTime = performance.now()
    loadTime.value = Math.round(endTime - startTime)

    // Show performance toast briefly
    setTimeout(() => {
      showPerformanceToast.value = true
    }, 500)

    // Auto-hide performance toast
    setTimeout(() => {
      showPerformanceToast.value = false
    }, priceLevels.value.length === 5 ? 6000 : 4000) // Show longer if defaults were created
  })
  resetCreateForm()
})

// Watch for name changes to auto-generate code
watch(() => createForm.value.name, generateCode)

// Close speed dial when clicking outside
const handleClickOutside = (event: Event) => {
  const speedDialElement = document.querySelector('[data-speed-dial]')
  if (speedDialElement && !speedDialElement.contains(event.target as Node)) {
    showSpeedDial.value = false
  }
}

// Add/remove event listener for speed dial
watch(showSpeedDial, (isOpen) => {
  if (isOpen) {
    document.addEventListener('click', handleClickOutside)
  } else {
    document.removeEventListener('click', handleClickOutside)
  }
})
</script>

<template>
  <div class="min-h-[calc(100vh-100px)] bg-background">
    <!-- Enhanced Sticky Header with Glass Morphism -->
    <div class="sticky top-0 z-50 border-b border-white/10 bg-white/80 dark:bg-gray-900/80 backdrop-blur-xl supports-[backdrop-filter]:bg-white/60 dark:supports-[backdrop-filter]:bg-gray-900/60">
      <!-- Animated gradient border -->
      <div class="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-transparent via-gray-400 to-transparent opacity-30"></div>

      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="flex h-16 items-center justify-between">
          <!-- Left Section with Enhanced Breadcrumbs -->
          <div class="flex items-center gap-6">
            <Button
              variant="ghost"
              size="sm"
              @click="goBack"
              class="group relative overflow-hidden rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 px-4 py-2 transition-all duration-300 hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30"
            >
              <div class="absolute inset-0 bg-gradient-to-r from-blue-600/0 via-blue-600/0 to-blue-600/0 group-hover:from-blue-600/10 group-hover:via-blue-600/5 group-hover:to-blue-600/0 transition-all duration-500"></div>
              <ArrowLeft class="w-4 h-4 mr-2 transition-transform group-hover:-translate-x-1" />
              <span class="relative font-medium">Back</span>
            </Button>

            <div class="flex items-center gap-3">
              <div class="h-6 w-px bg-gradient-to-b from-transparent via-gray-300 dark:via-gray-600 to-transparent"></div>

              <!-- Enhanced Breadcrumbs with Hover Effects -->
              <nav class="flex items-center gap-2 text-sm">
                <button class="group relative px-2 py-1 rounded-md transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                  <span class="text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 font-medium transition-colors">Dashboard</span>
                  <div class="absolute inset-x-0 bottom-0 h-0.5 bg-blue-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                </button>
                <div class="text-gray-400 dark:text-gray-500">/</div>
                <button class="group relative px-2 py-1 rounded-md transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/30">
                  <span class="text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 font-medium transition-colors">POS</span>
                  <div class="absolute inset-x-0 bottom-0 h-0.5 bg-blue-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                </button>
                <div class="text-gray-400 dark:text-gray-500">/</div>
                <span class="px-2 py-1 rounded-md bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 font-bold text-xs uppercase tracking-wider">
                  Price Levels
                </span>
              </nav>
            </div>
          </div>

          <!-- Right Section with Enhanced Actions -->
          <div class="flex items-center gap-2">
            <!-- Stats Quick View -->
            <div class="hidden lg:flex items-center gap-4 mr-4 px-4 py-2 bg-gray-50/80 dark:bg-gray-800/80 backdrop-blur rounded-xl border border-gray-200/50 dark:border-gray-700/50">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ priceLevelStats.total }} Levels</span>
              </div>
              <div class="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
              <div class="flex items-center gap-2">
                <Crown class="w-3 h-3 text-yellow-500" />
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">{{ priceLevelStats.defaultLevel?.name || 'No Default' }}</span>
              </div>
            </div>

            <!-- Enhanced Action Buttons -->
            <Button
              variant="outline"
              size="sm"
              @click="refreshData"
              :disabled="isRefreshing"
              class="group relative overflow-hidden bg-white/90 dark:bg-gray-800/90 backdrop-blur border-gray-200/50 dark:border-gray-700/50 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-300"
            >
              <div class="absolute inset-0 bg-gradient-to-r from-blue-600/0 to-blue-600/0 group-hover:from-blue-600/5 group-hover:to-blue-600/10 transition-all duration-500"></div>
              <RefreshCw :class="['w-4 h-4 relative z-10 transition-transform duration-300', isRefreshing && 'animate-spin']" />
              <span class="ml-2 relative z-10 font-medium">Refresh</span>
            </Button>

            <Dialog v-model:open="showCreateDialog">
              <DialogTrigger as-child>
                <Button
                  size="sm"
                  class="group relative overflow-hidden bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg shadow-blue-500/25 transition-all duration-300 hover:shadow-xl hover:shadow-blue-500/40 hover:scale-105"
                >
                  <div class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>
                  <Plus class="w-4 h-4 mr-2 relative z-10" />
                  <span class="relative z-10 font-medium">Create Level</span>
                </Button>
              </DialogTrigger>
              <DialogContent class="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>Create New Price Level</DialogTitle>
                  <DialogDescription>
                    Add a new pricing tier for your products.
                  </DialogDescription>
                </DialogHeader>
                <div class="space-y-4">
                  <div>
                    <Label for="create-name">Name</Label>
                    <Input
                      id="create-name"
                      v-model="createForm.name"
                      placeholder="e.g., Wholesale, VIP, Member"
                      class="mt-1"
                    />
                  </div>

                  <div>
                    <Label for="create-code">Code</Label>
                    <Input
                      id="create-code"
                      v-model="createForm.code"
                      placeholder="e.g., WHOLESALE, VIP"
                      class="mt-1"
                      maxlength="10"
                    />
                    <p class="text-xs text-muted-foreground mt-1">
                      Unique identifier (auto-generated from name)
                    </p>
                  </div>

                  <div>
                    <Label for="create-description">Description (Optional)</Label>
                    <Textarea
                      id="create-description"
                      v-model="createForm.description"
                      placeholder="Describe this price level..."
                      class="mt-1"
                      rows="2"
                    />
                  </div>

                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <Label for="create-discount">Discount %</Label>
                      <Input
                        id="create-discount"
                        v-model.number="createForm.discountPercentage"
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        placeholder="0"
                        class="mt-1"
                      />
                    </div>
                    <div>
                      <Label for="create-priority">Priority</Label>
                      <Input
                        id="create-priority"
                        v-model.number="createForm.priority"
                        type="number"
                        min="0"
                        placeholder="0"
                        class="mt-1"
                      />
                    </div>
                  </div>

                  <div>
                    <Label>Color</Label>
                    <div class="flex gap-2 mt-2">
                      <input
                        v-model="createForm.color"
                        type="color"
                        class="w-16 h-10 rounded border border-input"
                      />
                      <div class="flex gap-1 flex-wrap">
                        <button
                          v-for="color in predefinedColors"
                          :key="color"
                          type="button"
                          :style="{ backgroundColor: color }"
                          class="w-8 h-8 rounded border-2 border-white shadow-sm hover:scale-110 transition-transform"
                          :class="createForm.color === color ? 'ring-2 ring-primary' : ''"
                          @click="createForm.color = color"
                        />
                      </div>
                    </div>
                  </div>

                  <div class="flex items-center space-x-2">
                    <input
                      id="create-default"
                      v-model="createForm.isDefault"
                      type="checkbox"
                      class="rounded border-gray-300"
                    />
                    <Label for="create-default" class="text-sm">Set as default price level</Label>
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" @click="showCreateDialog = false">
                    Cancel
                  </Button>
                  <Button
                    @click="handleCreatePriceLevel"
                    :disabled="!createForm.name.trim() || !createForm.code.trim()"
                  >
                    Create Price Level
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button
                  variant="outline"
                  size="icon"
                  class="group relative bg-white/90 dark:bg-gray-800/90 backdrop-blur border-gray-200/50 dark:border-gray-700/50 hover:border-gray-300 dark:hover:border-gray-600 transition-all duration-300"
                >
                  <MoreHorizontal class="w-4 h-4 transition-transform group-hover:rotate-90 duration-300" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" class="w-48 bg-white/95 dark:bg-gray-800/95 backdrop-blur border-gray-200/50 dark:border-gray-700/50">
                <DropdownMenuItem class="group cursor-pointer">
                  <Download class="w-4 h-4 mr-3 text-blue-500 group-hover:text-blue-600 transition-colors" />
                  <span class="font-medium">Export Data</span>
                </DropdownMenuItem>
                <DropdownMenuItem class="group cursor-pointer">
                  <Upload class="w-4 h-4 mr-3 text-green-500 group-hover:text-green-600 transition-colors" />
                  <span class="font-medium">Import Data</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem class="group cursor-pointer">
                  <Settings class="w-4 h-4 mr-3 text-gray-500 group-hover:text-gray-600 transition-colors" />
                  <span class="font-medium">Settings</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
      <!-- Enhanced Page Header -->


              <!-- Revolutionary Statistics Dashboard -->
        <div class="xcard grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- Total Price Levels - Enhanced with 3D Effects -->
          <Card class="group relative overflow-hidden border-0 bg-gradient-to-br from-blue-50/90 via-indigo-50/90 to-blue-100/90 dark:from-blue-950/60 dark:via-indigo-950/60 dark:to-blue-900/60 backdrop-blur-sm hover:shadow-2xl hover:shadow-blue-500/20 transition-all duration-700 cursor-pointer transform hover:-translate-y-2 hover:rotate-1">
            <!-- Dynamic Background Elements -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-indigo-500/10 to-purple-500/5"></div>
            <div class="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-blue-400/30 to-indigo-400/30 rounded-full blur-3xl group-hover:scale-150 group-hover:rotate-180 transition-all duration-1000"></div>
            <div class="absolute -bottom-4 -left-4 w-24 h-24 bg-gradient-to-br from-indigo-400/20 to-blue-400/20 rounded-full blur-2xl group-hover:scale-125 transition-all duration-700"></div>

            <!-- Animated Border -->
            <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/20 via-transparent to-indigo-500/20 p-px opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <div class="w-full h-full bg-gradient-to-br from-blue-50/90 to-blue-100/90 dark:from-blue-950/60 dark:to-blue-900/60 rounded-2xl"></div>
            </div>

            <CardContent class="relative p-6 z-10">
              <div class="flex items-center justify-between mb-6">
                <!-- Enhanced Icon with Pulsing Ring -->
                <div class="relative">
                  <div class="absolute inset-0 bg-blue-500/20 rounded-full animate-ping scale-110"></div>
                  <div class="relative p-4 bg-gradient-to-br from-blue-500/10 to-indigo-500/20 rounded-2xl group-hover:from-blue-500/20 group-hover:to-indigo-500/30 transition-all duration-500 backdrop-blur-sm">
                    <Layers class="h-7 w-7 text-blue-600 dark:text-blue-400 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500" />
                  </div>
                </div>

                <!-- Animated Number Display -->
                <div class="text-right">
                  <div class="text-4xl font-black text-transparent bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-700 dark:from-blue-400 dark:via-blue-300 dark:to-indigo-300 bg-clip-text group-hover:scale-110 transition-transform duration-500">
                    {{ priceLevelStats.total }}
                  </div>
                  <div class="text-xs text-blue-600/70 dark:text-blue-400/70 font-bold uppercase tracking-widest mt-1">TOTAL LEVELS</div>
                </div>
              </div>

              <div class="space-y-4">
                <div>
                  <h3 class="text-lg font-bold text-blue-800 dark:text-blue-200 group-hover:text-blue-600 dark:group-hover:text-blue-300 transition-colors duration-300">Price Levels</h3>
                  <p class="text-sm text-blue-600/80 dark:text-blue-400/80 font-medium">Active pricing tiers</p>
                </div>

                <!-- Enhanced Progress Visualization -->
                <div class="space-y-2">
                  <div class="flex justify-between text-xs font-medium text-blue-700 dark:text-blue-300">
                    <span>Progress</span>
                    <span>{{ Math.min((priceLevelStats.total / 10) * 100, 100).toFixed(0) }}%</span>
                  </div>
                  <div class="h-3 bg-blue-200/50 dark:bg-blue-800/30 rounded-full overflow-hidden relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-200/50 to-indigo-200/50 dark:from-blue-800/30 dark:to-indigo-800/30"></div>
                    <div
                      class="h-full bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 rounded-full relative overflow-hidden transition-all duration-1500 ease-out group-hover:from-blue-400 group-hover:via-blue-500 group-hover:to-indigo-500"
                      :style="{ width: `${Math.min((priceLevelStats.total / 10) * 100, 100)}%` }"
                    >
                      <div class="absolute inset-0 bg-gradient-to-r from-white/20 via-white/40 to-white/20 -skew-x-12 animate-pulse"></div>
                    </div>
                  </div>
                </div>

                <!-- Status Indicator -->
                <div class="flex items-center justify-between pt-2">
                  <div class="flex items-center gap-2">
                    <div class="relative">
                      <div class="w-2.5 h-2.5 bg-green-500 rounded-full animate-pulse"></div>
                      <div class="absolute inset-0 w-2.5 h-2.5 bg-green-400 rounded-full animate-ping"></div>
                    </div>
                    <span class="text-xs text-green-600 dark:text-green-400 font-bold">ACTIVE</span>
                  </div>
                  <TrendingUp class="w-4 h-4 text-green-500 group-hover:scale-125 group-hover:text-green-400 transition-all duration-300" />
                </div>
              </div>
            </CardContent>
          </Card>

                  <!-- Default Price Level - Royal Design -->
          <Card class="group relative overflow-hidden border-0 bg-gradient-to-br from-amber-50/90 via-yellow-50/90 to-orange-100/90 dark:from-amber-950/60 dark:via-yellow-950/60 dark:to-orange-900/60 backdrop-blur-sm hover:shadow-2xl hover:shadow-amber-500/20 transition-all duration-700 cursor-pointer transform hover:-translate-y-2 hover:-rotate-1">
            <!-- Royal Background Elements -->
            <div class="absolute inset-0 bg-gradient-to-br from-amber-500/5 via-yellow-500/10 to-orange-500/5"></div>
            <div class="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-amber-400/30 to-yellow-400/30 rounded-full blur-3xl group-hover:scale-150 group-hover:rotate-180 transition-all duration-1000"></div>
            <div class="absolute -bottom-4 -left-4 w-24 h-24 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-2xl group-hover:scale-125 transition-all duration-700"></div>

            <!-- Golden Border Animation -->
            <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-amber-500/20 via-transparent to-yellow-500/20 p-px opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <div class="w-full h-full bg-gradient-to-br from-amber-50/90 to-orange-100/90 dark:from-amber-950/60 dark:to-orange-900/60 rounded-2xl"></div>
            </div>

            <CardContent class="relative p-6 z-10">
              <div class="flex items-center justify-between mb-6">
                <!-- Royal Crown Icon -->
                <div class="relative">
                  <div class="absolute inset-0 bg-amber-500/20 rounded-full animate-ping scale-110"></div>
                  <div class="relative p-4 bg-gradient-to-br from-amber-500/10 to-yellow-500/20 rounded-2xl group-hover:from-amber-500/20 group-hover:to-yellow-500/30 transition-all duration-500 backdrop-blur-sm">
                    <Crown class="h-7 w-7 text-amber-600 dark:text-amber-400 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500" />
                  </div>
                  <!-- Royal Sparkles -->
                  <div class="absolute -top-1 -right-1 w-2 h-2 bg-yellow-400 rounded-full animate-ping opacity-75"></div>
                  <div class="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-amber-400 rounded-full animate-pulse opacity-60"></div>
                </div>

                <!-- Status Indicator -->
                <div class="flex flex-col items-end gap-2">
                  <div class="flex items-center gap-2">
                    <div :class="[
                      'w-3 h-3 rounded-full transition-all duration-500 relative',
                      priceLevelStats.defaultLevel ? 'bg-green-500' : 'bg-gray-400'
                    ]">
                      <div v-if="priceLevelStats.defaultLevel" class="absolute inset-0 bg-green-400 rounded-full animate-ping"></div>
                    </div>
                    <span class="text-xs font-bold uppercase tracking-widest" :class="priceLevelStats.defaultLevel ? 'text-green-600 dark:text-green-400' : 'text-gray-500'">
                      {{ priceLevelStats.defaultLevel ? 'ACTIVE' : 'NOT SET' }}
                    </span>
                  </div>

                  <!-- Priority Badge -->
                  <div v-if="priceLevelStats.defaultLevel" class="px-2 py-1 bg-gradient-to-r from-amber-100 to-yellow-100 dark:from-amber-900/30 dark:to-yellow-900/30 rounded-full border border-amber-200 dark:border-amber-700">
                    <span class="text-xs font-bold text-amber-700 dark:text-amber-300">PRIORITY</span>
                  </div>
                </div>
              </div>

              <div class="space-y-4">
                <div>
                  <h3 class="text-lg font-bold text-amber-800 dark:text-amber-200 group-hover:text-amber-600 dark:group-hover:text-amber-300 transition-colors duration-300">Default Level</h3>
                  <div class="text-xl font-black text-transparent bg-gradient-to-br from-amber-600 via-amber-700 to-orange-700 dark:from-amber-400 dark:via-amber-300 dark:to-yellow-300 bg-clip-text truncate group-hover:scale-105 transition-transform duration-300">
                    {{ priceLevelStats.defaultLevel?.name || 'Not Set' }}
                  </div>
                  <p class="text-sm text-amber-600/80 dark:text-amber-400/80 font-medium">Primary pricing tier</p>
                </div>

                <!-- Royal Features -->
                <div class="space-y-3">
                  <div v-if="priceLevelStats.defaultLevel" class="flex items-center justify-between p-3 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 rounded-xl border border-amber-200/50 dark:border-amber-700/50">
                    <div class="flex items-center gap-2">
                      <Star class="w-4 h-4 text-amber-500 animate-pulse" />
                      <span class="text-sm font-bold text-amber-700 dark:text-amber-300">Priority Level</span>
                    </div>
                    <div class="text-xs font-medium text-amber-600 dark:text-amber-400">#1</div>
                  </div>

                  <div v-else class="flex items-center justify-center p-4 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 rounded-xl border border-gray-200 dark:border-gray-600 border-dashed">
                    <div class="text-center">
                      <div class="text-lg">👑</div>
                      <span class="text-sm font-medium text-gray-600 dark:text-gray-400">No default set</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

                  <!-- Discount Levels - Eco-Friendly Design -->
          <Card class="group relative overflow-hidden border-0 bg-gradient-to-br from-emerald-50/90 via-green-50/90 to-teal-100/90 dark:from-emerald-950/60 dark:via-green-950/60 dark:to-teal-900/60 backdrop-blur-sm hover:shadow-2xl hover:shadow-emerald-500/20 transition-all duration-700 cursor-pointer transform hover:-translate-y-2 hover:rotate-1">
            <!-- Natural Background Elements -->
            <div class="absolute inset-0 bg-gradient-to-br from-emerald-500/5 via-green-500/10 to-teal-500/5"></div>
            <div class="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-emerald-400/30 to-green-400/30 rounded-full blur-3xl group-hover:scale-150 group-hover:rotate-180 transition-all duration-1000"></div>
            <div class="absolute -bottom-4 -left-4 w-24 h-24 bg-gradient-to-br from-green-400/20 to-teal-400/20 rounded-full blur-2xl group-hover:scale-125 transition-all duration-700"></div>

            <!-- Eco Border Animation -->
            <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-500/20 via-transparent to-green-500/20 p-px opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <div class="w-full h-full bg-gradient-to-br from-emerald-50/90 to-teal-100/90 dark:from-emerald-950/60 dark:to-teal-900/60 rounded-2xl"></div>
            </div>

            <CardContent class="relative p-6 z-10">
              <div class="flex items-center justify-between mb-6">
                <!-- Percentage Icon with Animation -->
                <div class="relative">
                  <div class="absolute inset-0 bg-emerald-500/20 rounded-full animate-ping scale-110"></div>
                  <div class="relative p-4 bg-gradient-to-br from-emerald-500/10 to-green-500/20 rounded-2xl group-hover:from-emerald-500/20 group-hover:to-green-500/30 transition-all duration-500 backdrop-blur-sm">
                    <Percent class="h-7 w-7 text-emerald-600 dark:text-emerald-400 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500" />
                  </div>
                  <!-- Savings Sparkles -->
                  <div class="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-ping opacity-75"></div>
                  <div class="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-emerald-400 rounded-full animate-pulse opacity-60"></div>
                </div>

                <!-- Counter with Animation -->
                <div class="text-right">
                  <div class="text-4xl font-black text-transparent bg-gradient-to-br from-emerald-600 via-emerald-700 to-green-700 dark:from-emerald-400 dark:via-emerald-300 dark:to-green-300 bg-clip-text group-hover:scale-110 transition-transform duration-500">
                    {{ priceLevelStats.discountLevels }}
                  </div>
                  <div class="text-xs text-emerald-600/70 dark:text-emerald-400/70 font-bold uppercase tracking-widest mt-1">DISCOUNT LEVELS</div>
                </div>
              </div>

              <div class="space-y-4">
                <div>
                  <h3 class="text-lg font-bold text-emerald-800 dark:text-emerald-200 group-hover:text-emerald-600 dark:group-hover:text-emerald-300 transition-colors duration-300">Discount Levels</h3>
                  <p class="text-sm text-emerald-600/80 dark:text-emerald-400/80 font-medium">With discount applied</p>
                </div>

                <!-- Enhanced Coverage Visualization -->
                <div class="space-y-2">
                  <div class="flex justify-between text-xs font-medium text-emerald-700 dark:text-emerald-300">
                    <span>Coverage</span>
                    <span>{{ priceLevelStats.total > 0 ? Math.round((priceLevelStats.discountLevels / priceLevelStats.total) * 100) : 0 }}%</span>
                  </div>
                  <div class="h-3 bg-emerald-200/50 dark:bg-emerald-800/30 rounded-full overflow-hidden relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-emerald-200/50 to-green-200/50 dark:from-emerald-800/30 dark:to-green-800/30"></div>
                    <div
                      class="h-full bg-gradient-to-r from-emerald-500 via-emerald-600 to-green-600 rounded-full relative overflow-hidden transition-all duration-1500 ease-out group-hover:from-emerald-400 group-hover:via-emerald-500 group-hover:to-green-500"
                      :style="{ width: `${priceLevelStats.total > 0 ? (priceLevelStats.discountLevels / priceLevelStats.total) * 100 : 0}%` }"
                    >
                      <div class="absolute inset-0 bg-gradient-to-r from-white/20 via-white/40 to-white/20 -skew-x-12 animate-pulse"></div>
                    </div>
                  </div>
                </div>

                <!-- Savings Info -->
                <div class="p-3 bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-xl border border-emerald-200/50 dark:border-emerald-700/50">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                      <span class="text-sm font-bold text-emerald-700 dark:text-emerald-300">
                        {{ priceLevelStats.discountLevels > 0 ? 'Savings Active' : 'No Discounts' }}
                      </span>
                    </div>
                    <div class="text-xs font-medium text-emerald-600 dark:text-emerald-400">
                      {{ priceLevelStats.discountLevels }}/{{ priceLevelStats.total }}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

                  <!-- Max Discount - Premium Design -->
          <Card class="group relative overflow-hidden border-0 bg-gradient-to-br from-purple-50/90 via-violet-50/90 to-indigo-100/90 dark:from-purple-950/60 dark:via-violet-950/60 dark:to-indigo-900/60 backdrop-blur-sm hover:shadow-2xl hover:shadow-purple-500/20 transition-all duration-700 cursor-pointer transform hover:-translate-y-2 hover:-rotate-1">
            <!-- Premium Background Elements -->
            <div class="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-violet-500/10 to-indigo-500/5"></div>
            <div class="absolute -top-6 -right-6 w-32 h-32 bg-gradient-to-br from-purple-400/30 to-violet-400/30 rounded-full blur-3xl group-hover:scale-150 group-hover:rotate-180 transition-all duration-1000"></div>
            <div class="absolute -bottom-4 -left-4 w-24 h-24 bg-gradient-to-br from-violet-400/20 to-indigo-400/20 rounded-full blur-2xl group-hover:scale-125 transition-all duration-700"></div>

            <!-- Premium Border Animation -->
            <div class="absolute inset-0 rounded-2xl bg-gradient-to-r from-purple-500/20 via-transparent to-violet-500/20 p-px opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              <div class="w-full h-full bg-gradient-to-br from-purple-50/90 to-indigo-100/90 dark:from-purple-950/60 dark:to-indigo-900/60 rounded-2xl"></div>
            </div>

            <CardContent class="relative p-6 z-10">
              <div class="flex items-center justify-between mb-6">
                <!-- Trending Down Icon with Effects -->
                <div class="relative">
                  <div class="absolute inset-0 bg-purple-500/20 rounded-full animate-ping scale-110"></div>
                  <div class="relative p-4 bg-gradient-to-br from-purple-500/10 to-violet-500/20 rounded-2xl group-hover:from-purple-500/20 group-hover:to-violet-500/30 transition-all duration-500 backdrop-blur-sm">
                    <TrendingDown class="h-7 w-7 text-purple-600 dark:text-purple-400 group-hover:scale-125 group-hover:rotate-12 transition-all duration-500" />
                  </div>
                  <!-- Discount Sparkles -->
                  <div class="absolute -top-1 -right-1 w-2 h-2 bg-violet-400 rounded-full animate-ping opacity-75"></div>
                  <div class="absolute -bottom-1 -left-1 w-1.5 h-1.5 bg-purple-400 rounded-full animate-pulse opacity-60"></div>
                </div>

                <!-- Percentage Display -->
                <div class="text-right">
                  <div class="flex items-baseline justify-end">
                    <div class="text-4xl font-black text-transparent bg-gradient-to-br from-purple-600 via-purple-700 to-violet-700 dark:from-purple-400 dark:via-purple-300 dark:to-violet-300 bg-clip-text group-hover:scale-110 transition-transform duration-500">
                      {{ priceLevelStats.maxDiscount }}
                    </div>
                    <span class="text-2xl font-bold text-purple-600/80 dark:text-purple-400/80 ml-1">%</span>
                  </div>
                  <div class="text-xs text-purple-600/70 dark:text-purple-400/70 font-bold uppercase tracking-widest mt-1">MAX DISCOUNT</div>
                </div>
              </div>

              <div class="space-y-4">
                <div>
                  <h3 class="text-lg font-bold text-purple-800 dark:text-purple-200 group-hover:text-purple-600 dark:group-hover:text-purple-300 transition-colors duration-300">Max Discount</h3>
                  <p class="text-sm text-purple-600/80 dark:text-purple-400/80 font-medium">Highest discount offered</p>
                </div>

                <!-- Enhanced Savings Meter -->
                <div class="space-y-2">
                  <div class="flex justify-between text-xs font-medium text-purple-700 dark:text-purple-300">
                    <span>Savings Power</span>
                    <span>{{ priceLevelStats.maxDiscount }}% Max</span>
                  </div>
                  <div class="h-3 bg-purple-200/50 dark:bg-purple-800/30 rounded-full overflow-hidden relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-purple-200/50 to-violet-200/50 dark:from-purple-800/30 dark:to-violet-800/30"></div>
                    <div
                      class="h-full bg-gradient-to-r from-purple-500 via-purple-600 to-violet-600 rounded-full relative overflow-hidden transition-all duration-1500 ease-out group-hover:from-purple-400 group-hover:via-purple-500 group-hover:to-violet-500"
                      :style="{ width: `${Math.min(priceLevelStats.maxDiscount, 100)}%` }"
                    >
                      <div class="absolute inset-0 bg-gradient-to-r from-white/20 via-white/40 to-white/20 -skew-x-12 animate-pulse"></div>
                    </div>
                  </div>
                </div>

                <!-- Savings Achievement -->
                <div class="p-3 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 rounded-xl border border-purple-200/50 dark:border-purple-700/50">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                      <TrendingDown class="w-4 h-4 text-purple-500 animate-pulse" />
                      <span class="text-sm font-bold text-purple-700 dark:text-purple-300">
                        {{ priceLevelStats.maxDiscount > 0 ? `Up to ${priceLevelStats.maxDiscount}% Off` : 'No Discounts' }}
                      </span>
                    </div>
                    <div class="text-xs font-medium text-purple-600 dark:text-purple-400">
                      {{ priceLevelStats.maxDiscount > 0 ? 'ACTIVE' : 'INACTIVE' }}
                    </div>
                  </div>

                  <!-- Achievement Badge -->
                  <div v-if="priceLevelStats.maxDiscount > 0" class="mt-2 flex items-center gap-2">
                    <div class="flex-1 bg-purple-200/50 dark:bg-purple-800/30 rounded-full h-1">
                      <div
                        class="h-full bg-gradient-to-r from-purple-500 to-violet-500 rounded-full transition-all duration-1000"
                        :style="{ width: `${(priceLevelStats.maxDiscount / 50) * 100}%` }"
                      ></div>
                    </div>
                    <span class="text-xs font-bold text-purple-600 dark:text-purple-400">
                      {{ priceLevelStats.maxDiscount >= 30 ? '🏆 Premium' : priceLevelStats.maxDiscount >= 15 ? '🥈 Good' : '🥉 Basic' }}
                    </span>
                  </div>
                </div>
              </div>
                          </CardContent>
            </Card>
        </div>

      <!-- Enhanced Price Levels List -->
      <Card class="relative overflow-hidden border-0 bg-gradient-to-br from-gray-50/50 to-white dark:from-gray-950/50 dark:to-gray-900/50 backdrop-blur-sm">
        <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>

        <CardHeader class="relative border-b border-gray-200/50 dark:border-gray-700/50 pb-6">
          <CardTitle class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl">
                <DollarSign class="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 class="text-xl font-bold text-gray-900 dark:text-white">Price Levels</h2>
                <p class="text-sm text-gray-600 dark:text-gray-400">Manage your pricing tiers and strategies</p>
              </div>
            </div>
            <div class="flex items-center gap-3">
              <Badge variant="secondary" class="px-3 py-1 bg-blue-100/50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700">
                {{ priceLevels.length }} Active Levels
              </Badge>
              <Button variant="outline" size="sm" @click="refreshData" :disabled="isRefreshing" class="bg-white/50 dark:bg-gray-800/50 backdrop-blur">
                <RefreshCw :class="['w-4 h-4', isRefreshing && 'animate-spin']" />
              </Button>
            </div>
          </CardTitle>
        </CardHeader>

        <CardContent class="relative p-0">
          <!-- Loading State -->
          <div v-if="isLoading" class="flex justify-center py-16">
            <div class="flex flex-col items-center gap-4">
              <div class="relative">
                <div class="animate-spin h-8 w-8 border-3 border-blue-200 border-t-blue-600 rounded-full"></div>
                <div class="absolute inset-0 h-8 w-8 border-3 border-blue-200/20 rounded-full animate-ping"></div>
              </div>
              <div class="text-center">
                <p class="text-gray-600 dark:text-gray-400 font-medium">Loading price levels...</p>
                <p class="text-sm text-gray-500 dark:text-gray-500">This might take a moment</p>
              </div>
            </div>
          </div>

          <!-- Error State -->
          <div v-else-if="error" class="text-center py-16">
            <div class="p-4 bg-red-50 dark:bg-red-950/20 rounded-2xl mx-6 mb-6">
              <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <AlertCircle class="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 class="text-lg font-semibold text-red-800 dark:text-red-300 mb-2">Failed to Load</h3>
              <p class="text-red-600 dark:text-red-400 mb-4">{{ error }}</p>
              <Button variant="outline" @click="loadPriceLevels" class="border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 hover:bg-red-50 dark:hover:bg-red-950/30">
                <RefreshCw class="w-4 h-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>

          <!-- Enhanced Price Levels Grid -->
          <div v-else-if="priceLevels.length > 0" class="p-6 space-y-4">
            <div
              v-for="(priceLevel, index) in priceLevels"
              :key="priceLevel._id"
              class="group relative p-6 bg-white dark:bg-gray-800 rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-xl hover:shadow-blue-500/10 transition-all duration-300 hover:-translate-y-1"
            >
              <!-- Glow effect on hover -->
              <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <div class="relative flex items-center justify-between">
                <div class="flex items-center gap-6 flex-1">
                  <!-- Enhanced Drag Handle -->
                  <div class="opacity-0 group-hover:opacity-100 transition-all duration-300 cursor-grab active:cursor-grabbing">
                    <GripVertical class="w-5 h-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
                  </div>

                  <!-- Enhanced Color Indicator & Priority -->
                  <div class="flex items-center gap-4">
                    <div class="relative">
                      <div
                        class="w-6 h-6 rounded-full border-3 border-white dark:border-gray-800 shadow-lg transition-transform group-hover:scale-110"
                        :style="{ backgroundColor: priceLevel.color }"
                      ></div>
                      <div class="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 animate-pulse"></div>
                    </div>
                    <!-- <Badge
                      variant="outline"
                      class="px-3 py-1 text-xs font-bold bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300"
                    >
                      #{{priceLevel.priority + 1}}
                    </Badge> -->
                  </div>

                  <!-- Enhanced Level Info -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-center gap-3 mb-2">
                      <h3 class="text-lg font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {{ priceLevel.name }}
                      </h3>

                      <!-- Enhanced Badges -->
                      <div class="flex items-center gap-2">
                        <Badge
                          v-if="priceLevel.isDefault"
                          class="bg-gradient-to-r from-yellow-100 to-amber-100 dark:from-yellow-900/30 dark:to-amber-900/30 text-yellow-800 dark:text-yellow-300 border-yellow-300 dark:border-yellow-600 px-2 py-1"
                        >
                          <Crown class="w-3 h-3 mr-1" />
                          Default
                        </Badge>

                        <Badge variant="secondary" class="px-3 py-1 bg-blue-100/50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700 font-mono">
                          {{ priceLevel.code }}
                        </Badge>

                        <Badge
                          v-if="priceLevel.discountPercentage && priceLevel.discountPercentage > 0"
                          class="bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-800 dark:text-green-300 border-green-300 dark:border-green-600 px-2 py-1 font-bold"
                        >
                          <Percent class="w-3 h-3 mr-1" />
                          -{{ priceLevel.discountPercentage }}%
                        </Badge>
                      </div>
                    </div>

                    <p v-if="priceLevel.description" class="text-gray-600 dark:text-gray-400 text-sm mb-3 group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">
                      {{ priceLevel.description }}
                    </p>

                    <!-- Usage Statistics -->
                    <div class="flex items-center gap-4 text-xs">
                      <div class="flex items-center gap-1">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-green-600 dark:text-green-400 font-medium">Active Status</span>
                      </div>
                      <div class="w-px h-4 bg-gray-300 dark:bg-gray-600"></div>
                      <span class="text-gray-500 dark:text-gray-400">
                        Created {{ new Date(priceLevel.createdAt || Date.now()).toLocaleDateString() }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Enhanced Action Buttons -->
                <div class="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-4 group-hover:translate-x-0">
                  <Button
                    v-if="!priceLevel.isDefault"
                    variant="ghost"
                    size="sm"
                    @click="setAsDefault(priceLevel)"
                    title="Set as default"
                    class="hover:bg-yellow-100 dark:hover:bg-yellow-900/30 hover:text-yellow-700 dark:hover:text-yellow-300 transition-all duration-200"
                  >
                    <Crown class="w-4 h-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    @click="duplicatePriceLevel(priceLevel)"
                    title="Duplicate price level"
                    class="hover:bg-blue-100 dark:hover:bg-blue-900/30 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-200"
                  >
                    <Copy class="w-4 h-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    @click="openEditDialog(priceLevel)"
                    title="Edit price level"
                    class="hover:bg-green-100 dark:hover:bg-green-900/30 hover:text-green-700 dark:hover:text-green-300 transition-all duration-200"
                  >
                    <Edit class="w-4 h-4" />
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    @click="openDeleteDialog(priceLevel)"
                    :disabled="priceLevel.isDefault"
                    title="Delete price level"
                    class="hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-700 dark:hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <Trash2 class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

                    <!-- Enhanced Default Creation Message -->
          <div v-else class="text-center py-20 px-6">
            <div class="relative mb-8">
              <!-- Floating elements -->
              <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full blur-3xl opacity-60"></div>

              <div class="relative p-6 bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-900 dark:to-indigo-800 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center empty-state-icon">
                <DollarSign class="w-12 h-12 text-blue-500 dark:text-blue-400" />
              </div>
            </div>

                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-3">Setting Up Complete Price Level Suite</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-8 max-w-2xl mx-auto leading-relaxed">
              We're creating a comprehensive pricing foundation with <span class="font-semibold text-blue-600 dark:text-blue-400">Customer</span>, <span class="font-semibold text-gray-600 dark:text-gray-400">Guest</span>, <span class="font-semibold text-emerald-600 dark:text-emerald-400">Staff</span>, <span class="font-semibold text-amber-600 dark:text-amber-400">VIP Member</span>, and <span class="font-semibold text-purple-600 dark:text-purple-400">Wholesale</span> price levels. This covers all common business scenarios.
            </p>

            <!-- Enhanced Default Levels Preview -->
            <div class="max-w-5xl mx-auto mb-8">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Customer Default -->
                <div class="p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/50 dark:to-blue-900/50 rounded-2xl border border-blue-200 dark:border-blue-700">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                      <Users class="w-4 h-4 text-white" />
                    </div>
                    <div class="flex-1">
                      <h4 class="font-bold text-blue-900 dark:text-blue-100">Customer</h4>
                      <p class="text-xs text-blue-600 dark:text-blue-400">0% discount • Default</p>
                    </div>
                    <Crown class="w-4 h-4 text-yellow-500" />
                  </div>
                  <p class="text-sm text-blue-700 dark:text-blue-300">Standard pricing for registered customers</p>
                </div>

                <!-- Guest Default -->
                <div class="p-4 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-950/50 dark:to-gray-800/50 rounded-2xl border border-gray-200 dark:border-gray-700">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="w-8 h-8 rounded-full bg-gray-500 flex items-center justify-center">
                      <Star class="w-4 h-4 text-white" />
                    </div>
                    <div class="flex-1">
                      <h4 class="font-bold text-gray-900 dark:text-gray-100">Guest</h4>
                      <p class="text-xs text-gray-600 dark:text-gray-400">0% discount • Walk-ins</p>
                    </div>
                  </div>
                  <p class="text-sm text-gray-700 dark:text-gray-300">Guest and walk-in customer pricing</p>
                </div>

                <!-- Staff Level -->
                <div class="p-4 bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950/50 dark:to-emerald-800/50 rounded-2xl border border-emerald-200 dark:border-emerald-700">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="w-8 h-8 rounded-full bg-emerald-500 flex items-center justify-center">
                      <Users class="w-4 h-4 text-white" />
                    </div>
                    <div class="flex-1">
                      <h4 class="font-bold text-emerald-900 dark:text-emerald-100">Staff</h4>
                      <p class="text-xs text-emerald-600 dark:text-emerald-400">15% discount • Employees</p>
                    </div>
                  </div>
                  <p class="text-sm text-emerald-700 dark:text-emerald-300">Employee discount pricing for staff</p>
                </div>

                <!-- VIP Member Level -->
                <div class="p-4 bg-gradient-to-br from-amber-50 to-amber-100 dark:from-amber-950/50 dark:to-amber-800/50 rounded-2xl border border-amber-200 dark:border-amber-700">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="w-8 h-8 rounded-full bg-amber-500 flex items-center justify-center">
                      <Crown class="w-4 h-4 text-white" />
                    </div>
                    <div class="flex-1">
                      <h4 class="font-bold text-amber-900 dark:text-amber-100">VIP Member</h4>
                      <p class="text-xs text-amber-600 dark:text-amber-400">10% discount • Premium</p>
                    </div>
                  </div>
                  <p class="text-sm text-amber-700 dark:text-amber-300">Premium customers with exclusive discounts</p>
                </div>

                <!-- Wholesale Level -->
                <div class="p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/50 dark:to-purple-800/50 rounded-2xl border border-purple-200 dark:border-purple-700 md:col-span-2 lg:col-span-1">
                  <div class="flex items-center gap-3 mb-3">
                    <div class="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center">
                      <TrendingDown class="w-4 h-4 text-white" />
                    </div>
                    <div class="flex-1">
                      <h4 class="font-bold text-purple-900 dark:text-purple-100">Wholesale</h4>
                      <p class="text-xs text-purple-600 dark:text-purple-400">25% discount • Bulk</p>
                    </div>
                  </div>
                  <p class="text-sm text-purple-700 dark:text-purple-300">Bulk pricing for resellers and wholesale</p>
                </div>
              </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Button
                @click="showCreateDialog = true"
                size="lg"
                class="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg shadow-blue-500/25 px-8"
              >
                <Plus class="w-5 h-5 mr-2" />
                Add More Price Levels
              </Button>

              <Button variant="outline" size="lg" class="px-8" @click="refreshData">
                <RefreshCw class="w-5 h-5 mr-2" />
                Refresh View
              </Button>
            </div>

            <!-- Enhanced Info Banner -->
            <div class="mt-8 p-6 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-950/30 dark:via-indigo-950/30 dark:to-purple-950/30 rounded-xl border border-blue-200 dark:border-blue-800 max-w-2xl mx-auto">
              <div class="flex items-start gap-4">
                <div class="p-2 bg-gradient-to-br from-blue-100 to-indigo-100 dark:bg-blue-900/50 rounded-lg">
                  <Crown class="w-5 h-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div class="text-left flex-1">
                  <h5 class="font-bold text-blue-900 dark:text-blue-100 text-base">Complete Business Suite Ready! 🎉</h5>
                  <p class="text-sm text-blue-700 dark:text-blue-300 mt-2 leading-relaxed">
                    Your comprehensive pricing foundation includes levels for every business scenario: regular customers, walk-ins, staff discounts, VIP members, and wholesale pricing. This covers 95% of business needs right out of the box.
                  </p>

                  <!-- Features List -->
                  <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                    <div class="flex items-center gap-2">
                      <div class="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                      <span class="text-blue-600 dark:text-blue-400">Customer & Guest basics</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-1.5 h-1.5 bg-emerald-500 rounded-full"></div>
                      <span class="text-emerald-600 dark:text-emerald-400">Staff discount (15%)</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-1.5 h-1.5 bg-amber-500 rounded-full"></div>
                      <span class="text-amber-600 dark:text-amber-400">VIP member rewards (10%)</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                      <span class="text-purple-600 dark:text-purple-400">Wholesale pricing (25%)</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Floating Action Elements -->
      <div class="fixed bottom-8 right-8 z-40">
        <!-- Floating Speed Dial -->
        <div class="relative" data-speed-dial>
          <!-- Speed Dial Options -->
          <div v-if="showSpeedDial" class="absolute bottom-16 right-0 space-y-3 transform transition-all duration-300">
            <!-- Quick Create -->
            <button
              @click="showCreateDialog = true; showSpeedDial = false"
              class="speed-dial-item group flex items-center gap-3 bg-white dark:bg-gray-800 hover:bg-blue-50 dark:hover:bg-blue-900/30 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 border-glow-hover"
            >
              <div class="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50 transition-colors">
                <Plus class="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <span class="font-medium whitespace-nowrap">Quick Create</span>
            </button>

            <!-- Export -->
            <button
              @click="showSpeedDial = false"
              class="speed-dial-item group flex items-center gap-3 bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900/30 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 border-glow-hover"
            >
              <div class="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg group-hover:bg-green-200 dark:group-hover:bg-green-800/50 transition-colors">
                <Download class="w-5 h-5 text-green-600 dark:text-green-400" />
              </div>
              <span class="font-medium whitespace-nowrap">Export Data</span>
            </button>

            <!-- Settings -->
            <button
              @click="showSpeedDial = false"
              class="speed-dial-item group flex items-center gap-3 bg-white dark:bg-gray-800 hover:bg-purple-50 dark:hover:bg-purple-900/30 text-gray-700 dark:text-gray-300 px-4 py-3 rounded-xl shadow-lg hover:shadow-xl border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 border-glow-hover"
            >
              <div class="p-2 bg-purple-100 dark:bg-purple-900/50 rounded-lg group-hover:bg-purple-200 dark:group-hover:bg-purple-800/50 transition-colors">
                <Settings class="w-5 h-5 text-purple-600 dark:text-purple-400" />
              </div>
              <span class="font-medium whitespace-nowrap">Settings</span>
            </button>
          </div>

          <!-- Main FAB -->
          <button
            @click="showSpeedDial = !showSpeedDial"
            class="group relative flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 hover:scale-110 hover:rotate-12 fab-bounce gradient-animated"
          >
            <!-- Pulse Ring -->
            <div class="absolute inset-0 bg-blue-500 rounded-full animate-ping opacity-20"></div>

            <!-- Icon with Rotation -->
            <Plus :class="['w-7 h-7 transition-transform duration-300', showSpeedDial && 'rotate-45']" />

            <!-- Tooltip -->
            <div class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              Quick Actions
            </div>
          </button>
        </div>
      </div>

            <!-- Enhanced Performance & Setup Toast -->
      <div v-if="showPerformanceToast" class="fixed top-24 right-8 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-xl p-4 max-w-md transform transition-all duration-500 toast-enter border-glow-hover">
        <div class="flex items-start gap-3">
          <div v-if="priceLevelStats.total === 5" class="p-2 bg-blue-100 dark:bg-blue-900/50 rounded-lg">
            <Crown class="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div v-else class="p-2 bg-green-100 dark:bg-green-900/50 rounded-lg">
            <TrendingUp class="w-5 h-5 text-green-600 dark:text-green-400" />
          </div>
          <div class="flex-1">
            <h4 v-if="priceLevelStats.total === 5" class="font-semibold text-gray-900 dark:text-white">Complete Suite Ready! 🎉</h4>
            <h4 v-else class="font-semibold text-gray-900 dark:text-white">System Performance</h4>

            <p v-if="priceLevelStats.total === 5" class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Complete pricing suite created: <span class="font-semibold text-blue-600 dark:text-blue-400">Customer</span>, <span class="font-semibold text-gray-600 dark:text-gray-400">Guest</span>, <span class="font-semibold text-emerald-600 dark:text-emerald-400">Staff</span>, <span class="font-semibold text-amber-600 dark:text-amber-400">VIP</span>, and <span class="font-semibold text-purple-600 dark:text-purple-400">Wholesale</span>
            </p>
            <p v-else class="text-sm text-gray-600 dark:text-gray-400 mt-1">
              Page loaded in {{ loadTime }}ms with {{ priceLevelStats.total }} price levels
            </p>

            <div class="flex items-center gap-2 mt-2">
              <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                <div :class="[
                  'h-1 rounded-full transition-all duration-1000',
                  priceLevelStats.total === 5 ? 'bg-gradient-to-r from-blue-500 to-purple-500 w-full' : 'bg-green-500 w-3/4'
                ]"></div>
              </div>
              <span :class="[
                'text-xs font-medium',
                priceLevelStats.total === 5 ? 'text-blue-600 dark:text-blue-400' : 'text-green-600 dark:text-green-400'
              ]">
                {{ priceLevelStats.total === 5 ? 'All Set!' : '98%' }}
              </span>
            </div>
          </div>
          <button
            @click="showPerformanceToast = false"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Edit Price Level Dialog -->
    <Dialog v-model:open="showEditDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Price Level</DialogTitle>
          <DialogDescription>
            Update price level information.
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div>
            <Label for="edit-name">Name</Label>
            <Input
              id="edit-name"
              v-model="editForm.name"
              placeholder="e.g., Wholesale, VIP, Member"
              class="mt-1"
            />
          </div>

          <div>
            <Label for="edit-code">Code</Label>
            <Input
              id="edit-code"
              v-model="editForm.code"
              placeholder="e.g., WHOLESALE, VIP"
              class="mt-1"
              maxlength="10"
            />
          </div>

          <div>
            <Label for="edit-description">Description (Optional)</Label>
            <Textarea
              id="edit-description"
              v-model="editForm.description"
              placeholder="Describe this price level..."
              class="mt-1"
              rows="2"
            />
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <Label for="edit-discount">Discount %</Label>
              <Input
                id="edit-discount"
                v-model.number="editForm.discountPercentage"
                type="number"
                min="0"
                max="100"
                step="0.1"
                placeholder="0"
                class="mt-1"
              />
            </div>
            <div>
              <Label for="edit-priority">Priority</Label>
              <Input
                id="edit-priority"
                v-model.number="editForm.priority"
                type="number"
                min="0"
                placeholder="0"
                class="mt-1"
              />
            </div>
          </div>

          <div>
            <Label>Color</Label>
            <div class="flex gap-2 mt-2">
              <input
                v-model="editForm.color"
                type="color"
                class="w-16 h-10 rounded border border-input"
              />
              <div class="flex gap-1 flex-wrap">
                <button
                  v-for="color in predefinedColors"
                  :key="color"
                  type="button"
                  :style="{ backgroundColor: color }"
                  class="w-8 h-8 rounded border-2 border-white shadow-sm hover:scale-110 transition-transform"
                  :class="editForm.color === color ? 'ring-2 ring-primary' : ''"
                  @click="editForm.color = color"
                />
              </div>
            </div>
          </div>

          <div class="flex items-center space-x-2">
            <input
              id="edit-default"
              v-model="editForm.isDefault"
              type="checkbox"
              class="rounded border-gray-300"
            />
            <Label for="edit-default" class="text-sm">Set as default price level</Label>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showEditDialog = false">
            Cancel
          </Button>
          <Button
            @click="handleEditPriceLevel"
            :disabled="!editForm.name?.trim()"
          >
            Update Price Level
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Delete Price Level Dialog -->
    <Dialog v-model:open="showDeleteDialog">
      <DialogContent class="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Delete Price Level</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete "{{ selectedPriceLevel?.name }}"? This action cannot be undone.

            <div v-if="selectedPriceLevel?.isDefault" class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p class="text-sm text-yellow-800">
                ⚠️ This is the default price level and cannot be deleted. Set another level as default first.
              </p>
            </div>
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" @click="showDeleteDialog = false">
            Cancel
          </Button>
          <Button
            variant="destructive"
            @click="handleDeletePriceLevel"
            :disabled="selectedPriceLevel?.isDefault"
          >
            Delete Price Level
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<style scoped>
/* Enhanced grid pattern background */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(99, 102, 241, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(99, 102, 241, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced smooth transitions for drag and drop */
.sortable-ghost {
  opacity: 0.3;
  transform: scale(0.95);
  transition: all 0.3s ease;
}

.sortable-chosen {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

/* Enhanced color picker hover effects */
button[type="button"]:hover {
  transform: scale(1.2);
  transition: transform 0.2s ease;
}

/* Custom loading spinner with multiple layers */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse-glow {
  0%, 100% { opacity: 0.5; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

/* Enhanced card hover effects */
.card-hover-effect {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-effect:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(59, 130, 246, 0.2);
}

/* Floating animation for header elements */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

/* Gradient text shimmer effect */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.text-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Enhanced button hover states */
.btn-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
  transition: all 0.2s ease;
}

/* Progress bar animations */
@keyframes fill-progress {
  from { width: 0%; }
  to { width: var(--target-width); }
}

.progress-animated {
  animation: fill-progress 1.5s ease-out forwards;
}

/* Badge pulse effect */
@keyframes badge-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.9; }
}

.badge-pulse {
  animation: badge-pulse 2s ease-in-out infinite;
}

/* Enhanced focus states for accessibility */
.focus-enhanced:focus {
  outline: none;
  ring: 2px;
  ring-color: rgba(59, 130, 246, 0.5);
  ring-offset: 2px;
  ring-offset-color: white;
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(148, 163, 184, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(148, 163, 184, 0.1) 1px, transparent 1px);
  }

  .focus-enhanced:focus {
    ring-offset-color: rgb(31, 41, 55);
  }
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .card-hover-effect:hover {
    transform: none;
    box-shadow: none;
  }

  .floating-element {
    animation: none;
  }
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* Floating Action Button Animations */
@keyframes fab-bounce {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.05); }
}

.fab-bounce {
  animation: fab-bounce 2s ease-in-out infinite;
}

/* Speed Dial Slide In Animation */
@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.speed-dial-item {
  animation: slide-in-up 0.3s ease-out forwards;
}

.speed-dial-item:nth-child(1) { animation-delay: 0.1s; }
.speed-dial-item:nth-child(2) { animation-delay: 0.2s; }
.speed-dial-item:nth-child(3) { animation-delay: 0.3s; }

/* Performance Toast Animation */
@keyframes toast-slide-in {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.toast-enter {
  animation: toast-slide-in 0.5s ease-out forwards;
}

/* Enhanced Empty State Animation */
@keyframes empty-state-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(2deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

.empty-state-icon {
  animation: empty-state-float 4s ease-in-out infinite;
}

/* Statistics Card Number Counter Animation */
@keyframes counter-increment {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.counter-animate {
  animation: counter-increment 0.8s ease-out forwards;
}

/* Advanced Gradient Animations */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.gradient-animated {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

/* Sophisticated Border Glow */
@keyframes border-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.2),
                0 0 10px rgba(59, 130, 246, 0.1),
                0 0 15px rgba(59, 130, 246, 0.05);
  }
  50% {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.4),
                0 0 20px rgba(59, 130, 246, 0.2),
                0 0 30px rgba(59, 130, 246, 0.1);
  }
}

.border-glow-hover:hover {
  animation: border-glow 2s ease-in-out infinite;
}
</style>
