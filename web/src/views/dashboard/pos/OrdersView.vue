<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../../stores/auth'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Badge } from '../../../components/ui/badge'
import { Alert, AlertDescription } from '../../../components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs'
import DuotoneIcon from '../../../components/DuotoneIcon.vue'
import { ArrowLeft, Clock, CheckCircle, XCircle, Utensils, Eye, Printer } from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const router = useRouter()
const authStore = useAuthStore()

// Data
const isLoading = ref(true)
const error = ref('')
const orders = ref<any[]>([])
const searchQuery = ref('')
const statusFilter = ref('all')
const timeFilter = ref('today')

// Mock data for demonstration
const mockOrders = [
  {
    _id: '1',
    orderNumber: 'ORD-001',
    tableNumber: 'Table 5',
    tableId: 'table5',
    customerName: 'John Doe',
    status: 'pending',
    items: [
      { name: 'Pad Thai', quantity: 2, price: 12.99 },
      { name: 'Tom Yum Soup', quantity: 1, price: 8.99 },
      { name: 'Green Curry', quantity: 1, price: 14.99 }
    ],
    total: 49.97,
    paymentStatus: 'unpaid',
    orderType: 'dine-in',
    createdAt: '2025-01-10T14:30:00Z',
    estimatedTime: 25,
    notes: 'Extra spicy'
  },
  {
    _id: '2',
    orderNumber: 'ORD-002',
    tableNumber: 'Table 12',
    tableId: 'table12',
    customerName: 'Jane Smith',
    status: 'preparing',
    items: [
      { name: 'BBQ Ribs', quantity: 1, price: 24.99 },
      { name: 'Caesar Salad', quantity: 2, price: 9.99 },
      { name: 'Iced Tea', quantity: 2, price: 2.99 }
    ],
    total: 50.95,
    paymentStatus: 'paid',
    orderType: 'dine-in',
    createdAt: '2025-01-10T13:15:00Z',
    estimatedTime: 35,
    notes: 'Well done ribs'
  },
  {
    _id: '3',
    orderNumber: 'ORD-003',
    tableNumber: 'Table 3',
    tableId: 'table3',
    customerName: 'Mike Johnson',
    status: 'ready',
    items: [
      { name: 'Fish & Chips', quantity: 1, price: 16.99 },
      { name: 'Coleslaw', quantity: 1, price: 4.99 },
      { name: 'Beer', quantity: 2, price: 5.99 }
    ],
    total: 33.96,
    paymentStatus: 'paid',
    orderType: 'dine-in',
    createdAt: '2025-01-10T12:45:00Z',
    estimatedTime: 20,
    notes: ''
  },
  {
    _id: '4',
    orderNumber: 'ORD-004',
    tableNumber: 'Table 8',
    tableId: 'table8',
    customerName: 'Sarah Wilson',
    status: 'served',
    items: [
      { name: 'Chicken Teriyaki', quantity: 1, price: 18.99 },
      { name: 'Miso Soup', quantity: 1, price: 3.99 },
      { name: 'Green Tea', quantity: 1, price: 2.99 }
    ],
    total: 25.97,
    paymentStatus: 'paid',
    orderType: 'dine-in',
    createdAt: '2025-01-10T11:20:00Z',
    estimatedTime: 15,
    notes: 'No vegetables'
  }
]

// Computed
const filteredOrders = computed(() => {
  let filtered = orders.value

  // Status filter
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(order => order.status === statusFilter.value)
  }

  // Search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(order =>
      order.orderNumber.toLowerCase().includes(query) ||
      order.tableNumber.toLowerCase().includes(query) ||
      order.customerName.toLowerCase().includes(query) ||
      order.items.some((item: any) => item.name.toLowerCase().includes(query))
    )
  }

  return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
})

const ordersByStatus = computed(() => {
  const statuses = ['pending', 'preparing', 'ready', 'served']
  return statuses.map(status => ({
    status,
    orders: orders.value.filter(order => order.status === status),
    count: orders.value.filter(order => order.status === status).length
  }))
})

const totalRevenue = computed(() => {
  return orders.value
    .filter(order => order.paymentStatus === 'paid')
    .reduce((total, order) => total + order.total, 0)
})

const averageOrderValue = computed(() => {
  const paidOrders = orders.value.filter(order => order.paymentStatus === 'paid')
  return paidOrders.length > 0 ? totalRevenue.value / paidOrders.length : 0
})

// Methods
const goBack = () => {
  router.push('/dashboard/modules')
}

const loadOrders = async () => {
  try {
    isLoading.value = true
    error.value = ''

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    orders.value = mockOrders

  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load orders'
    console.error('Failed to load orders:', err)
  } finally {
    isLoading.value = false
  }
}

const updateOrderStatus = async (order: any, newStatus: string) => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))

    order.status = newStatus

    toast.success('Order status updated', {
      description: `${order.orderNumber} is now ${newStatus}`
    })
  } catch (err) {
    toast.error('Failed to update order status')
  }
}

const viewOrderDetails = (order: any) => {
  // This would open a detailed order view
  toast.info('Order Details', {
    description: `Opening details for ${order.orderNumber}`
  })
}

const printOrder = (order: any) => {
  toast.success('Printing order', {
    description: `${order.orderNumber} sent to printer`
  })
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'bg-yellow-100 text-yellow-700'
    case 'preparing': return 'bg-blue-100 text-blue-700'
    case 'ready': return 'bg-green-100 text-green-700'
    case 'served': return 'bg-gray-100 text-gray-700'
    default: return 'bg-gray-100 text-gray-700'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'pending': return Clock
    case 'preparing': return Utensils
    case 'ready': return CheckCircle
    case 'served': return CheckCircle
    default: return Clock
  }
}

const getPaymentStatusColor = (status: string) => {
  return status === 'paid' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const getElapsedTime = (createdAt: string) => {
  const now = new Date()
  const orderTime = new Date(createdAt)
  const diffMinutes = Math.floor((now.getTime() - orderTime.getTime()) / (1000 * 60))

  if (diffMinutes < 60) {
    return `${diffMinutes}m ago`
  } else {
    const hours = Math.floor(diffMinutes / 60)
    const minutes = diffMinutes % 60
    return `${hours}h ${minutes}m ago`
  }
}

onMounted(() => {
  loadOrders()
})
</script>

<template>
  <div class="min-h-[calc(100vh-100px)] bg-background">
    <div class="h-[calc(100vh-100px)] flex flex-col">
      <!-- Header -->
      <div class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <Button variant="ghost" size="sm" @click="goBack">
                <ArrowLeft class="w-4 h-4 mr-2" />
                Back
              </Button>
              <div class="h-4 w-px bg-border"></div>
              <nav class="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Dashboard</span>
                <span>/</span>
                <span>POS</span>
                <span>/</span>
                <span class="text-foreground font-medium">Orders</span>
              </nav>
            </div>

            <div class="flex items-center gap-3">
              <!-- Filters -->
              <Select v-model="statusFilter">
                <SelectTrigger class="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="preparing">Preparing</SelectItem>
                  <SelectItem value="ready">Ready</SelectItem>
                  <SelectItem value="served">Served</SelectItem>
                </SelectContent>
              </Select>

              <Select v-model="timeFilter">
                <SelectTrigger class="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Alert -->
      <div v-if="error" class="p-6">
        <Alert variant="destructive">
          <DuotoneIcon name="warning" class="h-4 w-4" />
          <AlertDescription>{{ error }}</AlertDescription>
        </Alert>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex-1 flex items-center justify-center">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 rounded-full border-2 border-rose-200 border-t-rose-500 animate-spin"></div>
          <span class="text-lg text-muted-foreground font-medium">Loading orders...</span>
        </div>
      </div>

      <!-- Main Content -->
      <div v-else class="flex-1 overflow-hidden p-6">
        <div class="h-full flex flex-col space-y-6">
          <!-- Stats Cards -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Total Orders</p>
                    <p class="text-2xl font-bold">{{ orders.length }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                    <DuotoneIcon name="receipt" class="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Pending</p>
                    <p class="text-2xl font-bold">{{ ordersByStatus.find(s => s.status === 'pending')?.count || 0 }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-yellow-100 flex items-center justify-center">
                    <Clock class="w-6 h-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Revenue</p>
                    <p class="text-2xl font-bold">{{ formatCurrency(totalRevenue) }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                    <DuotoneIcon name="dollar" class="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Avg Order</p>
                    <p class="text-2xl font-bold">{{ formatCurrency(averageOrderValue) }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                    <DuotoneIcon name="chart-bar" class="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Search -->
          <div class="flex items-center gap-4">
            <div class="flex-1 relative">
              <DuotoneIcon name="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="Search orders, tables, customers..."
                class="pl-10"
              />
            </div>
          </div>

          <!-- Content Tabs -->
          <Tabs default-value="list" class="flex-1 flex flex-col">
            <TabsList class="grid w-full grid-cols-2">
              <TabsTrigger value="list">Order List</TabsTrigger>
              <TabsTrigger value="kanban">Kitchen Board</TabsTrigger>
            </TabsList>

            <!-- List View -->
            <TabsContent value="list" class="flex-1 mt-6">
              <div v-if="filteredOrders.length === 0" class="flex-1 flex items-center justify-center">
                <div class="text-center">
                  <div class="w-16 h-16 rounded-lg bg-muted flex items-center justify-center mx-auto mb-4">
                    <DuotoneIcon name="receipt" class="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 class="text-xl font-semibold mb-2">No Orders Found</h3>
                  <p class="text-muted-foreground">
                    {{ searchQuery ? 'No orders match your search' : 'No orders for the selected filters' }}
                  </p>
                </div>
              </div>

              <div v-else class="space-y-4">
                <Card v-for="order in filteredOrders" :key="order._id" class="group">
                  <CardContent class="p-6">
                    <div class="flex items-center justify-between">
                      <!-- Order Info -->
                      <div class="flex-1">
                        <div class="flex items-center gap-4 mb-2">
                          <h3 class="font-semibold text-lg">{{ order.orderNumber }}</h3>
                          <Badge :class="getStatusColor(order.status)">{{ order.status }}</Badge>
                          <Badge :class="getPaymentStatusColor(order.paymentStatus)">{{ order.paymentStatus }}</Badge>
                          <span class="text-sm text-muted-foreground">{{ getElapsedTime(order.createdAt) }}</span>
                        </div>

                        <div class="flex items-center gap-6 text-sm text-muted-foreground mb-3">
                          <span><strong>Table:</strong> {{ order.tableNumber }}</span>
                          <span><strong>Customer:</strong> {{ order.customerName }}</span>
                          <span><strong>Total:</strong> {{ formatCurrency(order.total) }}</span>
                          <span><strong>Time:</strong> {{ formatTime(order.createdAt) }}</span>
                        </div>

                        <!-- Order Items -->
                        <div class="flex flex-wrap gap-2">
                          <Badge v-for="item in order.items.slice(0, 3)" :key="item.name" variant="outline" class="text-xs">
                            {{ item.quantity }}x {{ item.name }}
                          </Badge>
                          <Badge v-if="order.items.length > 3" variant="outline" class="text-xs">
                            +{{ order.items.length - 3 }} more
                          </Badge>
                        </div>

                        <!-- Notes -->
                        <p v-if="order.notes" class="text-sm text-muted-foreground mt-2 italic">
                          Note: {{ order.notes }}
                        </p>
                      </div>

                      <!-- Actions -->
                      <div class="flex items-center gap-2">
                        <!-- Status Update -->
                        <Select :model-value="order.status" @update:model-value="(value) => updateOrderStatus(order, value)">
                          <SelectTrigger class="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="preparing">Preparing</SelectItem>
                            <SelectItem value="ready">Ready</SelectItem>
                            <SelectItem value="served">Served</SelectItem>
                          </SelectContent>
                        </Select>

                        <Button size="sm" variant="outline" @click="viewOrderDetails(order)">
                          <Eye class="w-4 h-4 mr-2" />
                          View
                        </Button>

                        <Button size="sm" variant="outline" @click="printOrder(order)">
                          <Printer class="w-4 h-4 mr-2" />
                          Print
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <!-- Kanban View -->
            <TabsContent value="kanban" class="flex-1 mt-6">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-6 h-full">
                <div v-for="statusGroup in ordersByStatus" :key="statusGroup.status" class="space-y-4">
                  <!-- Column Header -->
                  <div class="flex items-center justify-between p-4 bg-muted rounded-lg">
                    <div class="flex items-center gap-2">
                      <component :is="getStatusIcon(statusGroup.status)" class="w-5 h-5" />
                      <h3 class="font-semibold capitalize">{{ statusGroup.status }}</h3>
                    </div>
                    <Badge variant="secondary">{{ statusGroup.count }}</Badge>
                  </div>

                  <!-- Orders in Column -->
                  <div class="space-y-3 min-h-[400px]">
                    <Card v-for="order in statusGroup.orders" :key="order._id" class="group cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent class="p-4">
                        <div class="space-y-2">
                          <div class="flex items-center justify-between">
                            <h4 class="font-medium">{{ order.orderNumber }}</h4>
                            <span class="text-xs text-muted-foreground">{{ formatTime(order.createdAt) }}</span>
                          </div>

                          <div class="text-sm text-muted-foreground">
                            <p>{{ order.tableNumber }} • {{ order.customerName }}</p>
                            <p class="font-medium text-foreground">{{ formatCurrency(order.total) }}</p>
                          </div>

                          <div class="flex flex-wrap gap-1">
                            <Badge v-for="item in order.items.slice(0, 2)" :key="item.name" variant="outline" class="text-xs">
                              {{ item.quantity }}x {{ item.name }}
                            </Badge>
                            <Badge v-if="order.items.length > 2" variant="outline" class="text-xs">
                              +{{ order.items.length - 2 }}
                            </Badge>
                          </div>

                          <div class="flex gap-1 pt-2">
                            <Button size="sm" variant="outline" @click="viewOrderDetails(order)" class="flex-1">
                              <Eye class="w-3 h-3" />
                            </Button>
                            <Button size="sm" variant="outline" @click="printOrder(order)" class="flex-1">
                              <Printer class="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  </div>
</template>
