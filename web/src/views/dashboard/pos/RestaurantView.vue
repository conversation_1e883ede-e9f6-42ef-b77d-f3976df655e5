<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../../stores/auth'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Badge } from '../../../components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs'
import { Alert, AlertDescription } from '../../../components/ui/alert'
import { Switch } from '../../../components/ui/switch'
import { Separator } from '../../../components/ui/separator'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '../../../components/ui/dialog'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import DuotoneIcon from '../../../components/DuotoneIcon.vue'
import { useI18n } from 'vue-i18n'
// @ts-ignore - vue-sonner types may not be fully available
import { toast } from 'vue-sonner'
import { restaurantService, type RestaurantTable, type RestaurantOrder, type RestaurantFloor } from '../../../services/restaurant.service'
import { vDraggable } from '@neodrag/vue'
import {
  ArrowLeft,
  Grid3x3,
  List,
  RefreshCw,
  TrendingUp,
  Activity,
  Package,
  DollarSign,
  Users,
  Clock,
  CheckCircle,
  Receipt,
  Settings,
  Plus,
  Eye,
  BarChart3,
  Move,
  RotateCcw,
  Copy,
  Edit2,
  Trash2,
  X,
  Check
} from 'lucide-vue-next'
import { AlertDialog, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogCancel, AlertDialogAction } from '../../../components/ui/alert-dialog'
import FloatingFloorTabs from '../../../components/FloatingFloorTabs.vue'
import FloorManagementDialog from '../../../components/FloorManagementDialog.vue'

const router = useRouter()
const { t } = useI18n()
const authStore = useAuthStore()

// UI State
const sidebarOpen = ref(true)
const viewPreference = ref<'grid' | 'list'>('grid')
const activeTab = ref('tables')
const isRefreshing = ref(false)
const isArrangeMode = ref(false)
const isResettingLayout = ref(false)
const hasCustomPositions = ref(false)

// Arrange Mode Enhancement State
const editingTableId = ref<string | null>(null)
const editingTableName = ref('')
const isDuplicatingTable = ref(false)
const selectedTableForActions = ref<string | null>(null)

// Preference Storage Keys
const VIEW_PREFERENCE_KEY = 'restaurant-view-preference'
const SIDEBAR_PREFERENCE_KEY = 'restaurant-sidebar-open'

// Load saved sidebar state or default to true
const getSavedSidebarState = (): boolean => {
  try {
    const saved = localStorage.getItem(SIDEBAR_PREFERENCE_KEY)
    if (saved !== null) {
      return JSON.parse(saved)
    }
  } catch (err) {
    console.error('Failed to load sidebar state:', err)
  }
  return true
}

// Load preferences from localStorage
const loadPreferences = () => {
  const savedView = localStorage.getItem(VIEW_PREFERENCE_KEY) as 'grid' | 'list' | null

  if (savedView) viewPreference.value = savedView
  sidebarOpen.value = getSavedSidebarState()
}

// Save preferences to localStorage
const savePreferences = () => {
  localStorage.setItem(VIEW_PREFERENCE_KEY, viewPreference.value)
  localStorage.setItem(SIDEBAR_PREFERENCE_KEY, JSON.stringify(sidebarOpen.value))
}

// Watch for preference changes
watch([viewPreference, sidebarOpen], savePreferences)

// Navigation functions
const goBack = () => {
  router.push('/dashboard')
}

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const refreshData = async () => {
  isRefreshing.value = true
  try {
    await loadTables()
    await loadActiveOrders()
    toast.success('Data refreshed successfully')
  } catch (err) {
    toast.error('Failed to refresh data')
  } finally {
    isRefreshing.value = false
  }
}

// Data
const tables = ref<RestaurantTable[]>([])
const qrMenus = ref<any[]>([])
const activeOrders = ref<RestaurantOrder[]>([])
const isLoading = ref(false)
const error = ref('')
const selectedTable = ref<RestaurantTable | null>(null)

// Floor Management
const floors = ref<RestaurantFloor[]>([])
const selectedFloorId = ref('')
const floorsLoading = ref(false)
const showFloorManagementDialog = ref(false)

// Load selected floor from localStorage on init
const loadSelectedFloorFromStorage = () => {
  const stored = localStorage.getItem('selectedFloorId')
  if (stored) {
    selectedFloorId.value = stored
    console.log('Restored selected floor from localStorage:', stored)
  }
}

// Save selected floor to localStorage
const saveSelectedFloorToStorage = (floorId: string) => {
  if (floorId) {
    localStorage.setItem('selectedFloorId', floorId)
    console.log('Saved selected floor to localStorage:', floorId)
  } else {
    localStorage.removeItem('selectedFloorId')
    console.log('Removed selected floor from localStorage')
  }
}

// Restaurant stats
const restaurantStats = computed(() => {
  const validTables = tables.value.filter(t => t && t.status)
  const totalTables = validTables.length
  const occupiedTables = validTables.filter(t => t.status === 'occupied').length
  const availableTables = validTables.filter(t => t.status === 'available').length
  const reservedTables = validTables.filter(t => t.status === 'reserved').length

  const validOrders = activeOrders.value.filter(o => o && o.status)
  const totalOrders = validOrders.length
  const pendingOrders = validOrders.filter(o => o.status === 'pending').length
  const preparingOrders = validOrders.filter(o => o.status === 'preparing').length
  const readyOrders = validOrders.filter(o => o.status === 'ready').length

  return {
    totalTables,
    occupiedTables,
    availableTables,
    reservedTables,
    occupancyRate: totalTables > 0 ? Math.round((occupiedTables / totalTables) * 100) : 0,
    totalOrders,
    pendingOrders,
    preparingOrders,
    readyOrders
  }
})

// Quick stats for sidebar
const quickStats = computed(() => [
  {
    title: 'Total Tables',
    value: restaurantStats.value.totalTables,
    description: `${restaurantStats.value.occupancyRate}% occupied`,
    icon: Grid3x3,
    color: 'blue',
    trend: '+0 this week'
  },
  {
    title: 'Available Tables',
    value: restaurantStats.value.availableTables,
    description: 'ready for guests',
    icon: CheckCircle,
    color: 'green',
    trend: 'Real-time'
  },
  {
    title: 'Active Orders',
    value: restaurantStats.value.totalOrders,
    description: `${restaurantStats.value.pendingOrders} pending`,
    icon: Receipt,
    color: 'orange',
    trend: 'Live tracking'
  },
  {
    title: 'Revenue Today',
    value: '$0',
    description: 'from orders',
    icon: DollarSign,
    color: 'purple',
    trend: '+0% vs yesterday'
  }
])

// Table status configuration with modern colors
const tableStatusConfig = {
  available: {
    color: 'bg-emerald-500',
    textColor: 'text-emerald-700',
    bgColor: 'bg-emerald-50',
    borderColor: 'border-emerald-200',
    label: 'Available',
    icon: 'check-circle',
    gradient: 'from-emerald-500 to-emerald-600'
  },
  occupied: {
    color: 'bg-rose-500',
    textColor: 'text-rose-700',
    bgColor: 'bg-rose-50',
    borderColor: 'border-rose-200',
    label: 'Occupied',
    icon: 'users',
    gradient: 'from-rose-500 to-rose-600'
  },
  reserved: {
    color: 'bg-amber-500',
    textColor: 'text-amber-700',
    bgColor: 'bg-amber-50',
    borderColor: 'border-amber-200',
    label: 'Reserved',
    icon: 'clock',
    gradient: 'from-amber-500 to-amber-600'
  },
  cleaning: {
    color: 'bg-sky-500',
    textColor: 'text-sky-700',
    bgColor: 'bg-sky-50',
    borderColor: 'border-sky-200',
    label: 'Cleaning',
    icon: 'sparkles',
    gradient: 'from-sky-500 to-sky-600'
  },
  maintenance: {
    color: 'bg-slate-500',
    textColor: 'text-slate-700',
    bgColor: 'bg-slate-50',
    borderColor: 'border-slate-200',
    label: 'Maintenance',
    icon: 'wrench',
    gradient: 'from-slate-500 to-slate-600'
  }
}

// Order status configuration
const orderStatusConfig = {
  pending: { color: 'bg-orange-500', label: 'Pending', icon: 'clock', gradient: 'from-orange-500 to-orange-600' },
  confirmed: { color: 'bg-blue-500', label: 'Confirmed', icon: 'check', gradient: 'from-blue-500 to-blue-600' },
  preparing: { color: 'bg-purple-500', label: 'Preparing', icon: 'chef-hat', gradient: 'from-purple-500 to-purple-600' },
  ready: { color: 'bg-green-500', label: 'Ready', icon: 'bell', gradient: 'from-green-500 to-green-600' },
  served: { color: 'bg-gray-500', label: 'Served', icon: 'utensils', gradient: 'from-gray-500 to-gray-600' },
  cancelled: { color: 'bg-red-500', label: 'Cancelled', icon: 'x', gradient: 'from-red-500 to-red-600' }
}

// Grid/List view computed classes
const tableGridClasses = computed(() => {
  return 'grid grid-cols-5 gap-6 auto-rows-max'
})

const tableCardClasses = computed(() => {
  const baseClasses = 'group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-2 overflow-hidden backdrop-blur-sm'

  if (viewPreference.value === 'list') {
    return `${baseClasses} flex flex-row items-center p-4 h-24`
  }


  return `${baseClasses} h-48`
})

// Table size based on capacity but with variations
const getTableSize = (table: any): { width: string, height: string, shape: string } => {
  try {
    if (!table || typeof table.capacity !== 'number') {
      console.warn('Invalid table data for getTableSize:', table)
      return { width: 'w-36', height: 'h-32', shape: 'rounded-lg' }
    }

    // All tables have the same size
    return { width: 'w-36', height: 'h-32', shape: 'rounded-lg' }
  } catch (error) {
    console.error('Error in getTableSize:', error)
    return { width: 'w-36', height: 'h-32', shape: 'rounded-lg' }
  }
}

// Get table revenue and timing (mock data for now)
const getTableInfo = (table: any) => {
  try {
    if (!table || !table.status) {
      console.warn('Invalid table data for getTableInfo:', table)
      return { revenue: null, time: null, hasInfo: false }
    }

    if (table.status === 'occupied') {
      // Generate realistic revenue values like in screenshot
      const baseRevenue = Math.random() * 400 + 20 // $20-420
      const revenue = Math.round(baseRevenue * 100) / 100 // Round to 2 decimal places
      const revenueStr = revenue % 1 === 0 ? `$${revenue}` : `$${revenue.toFixed(2)}`

      // Generate realistic timing
      const mockTime = Math.floor(Math.random() * 180) + 5 // 5-185 minutes
      const hours = Math.floor(mockTime / 60)
      const minutes = mockTime % 60
      const timeStr = hours > 0 ? `${hours}:${minutes.toString().padStart(2, '0')}h` : `0:${minutes}h`

      return {
        revenue: revenueStr,
        time: timeStr,
        hasInfo: true
      }
    } else if (table.status === 'reserved') {
      return {
        revenue: null,
        time: 'Reserved',
        hasInfo: false
      }
    }

    return {
      revenue: null,
      time: null,
      hasInfo: false
    }
  } catch (error) {
    console.error('Error in getTableInfo:', error)
    return { revenue: null, time: null, hasInfo: false }
  }
}

// Chair positioning function - enhanced for all capacities 2-12
const getChairPositions = (capacity: number) => {
  try {
    if (!capacity || typeof capacity !== 'number' || capacity <= 0) {
      console.warn('Invalid capacity for getChairPositions:', capacity)
      return []
    }

    const chairs = []

    // Position chairs around table edges with optimized layouts for each capacity
    if (capacity === 1) {
      // 1 chair: centered on one side
      chairs.push(
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '28px', height: '12px' } }
      )
    } else if (capacity === 2) {
      // 2 chairs: opposite sides (top and bottom)
      chairs.push(
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '28px', height: '12px' } },
        { style: { left: '50%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '28px', height: '12px' } }
      )
    } else if (capacity === 3) {
      // 3 chairs: evenly spaced around table (top and sides)
      chairs.push(
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '28px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '28px' } },
        { style: { left: '-8px', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '28px' } }
      )
    } else if (capacity === 4) {
      // 4 chairs: one on each side, evenly spaced
      chairs.push(
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '28px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '28px' } },
        { style: { left: '50%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '28px', height: '12px' } },
        { style: { left: '-8px', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '28px' } }
      )
    } else if (capacity === 5) {
      // 5 chairs: 2 on long sides, 1 on top
      chairs.push(
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '24px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '30%', transform: 'translate(-50%, -50%)', width: '12px', height: '24px' } },
        { style: { left: 'calc(100% + 8px)', top: '70%', transform: 'translate(-50%, -50%)', width: '12px', height: '24px' } },
        { style: { left: '-8px', top: '30%', transform: 'translate(-50%, -50%)', width: '12px', height: '24px' } },
        { style: { left: '-8px', top: '70%', transform: 'translate(-50%, -50%)', width: '12px', height: '24px' } }
      )
    } else if (capacity === 6) {
      // 6 chairs: 2 on long sides, 1 on short sides
      chairs.push(
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '24px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '30%', transform: 'translate(-50%, -50%)', width: '12px', height: '24px' } },
        { style: { left: 'calc(100% + 8px)', top: '70%', transform: 'translate(-50%, -50%)', width: '12px', height: '24px' } },
        { style: { left: '50%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '24px', height: '12px' } },
        { style: { left: '-8px', top: '70%', transform: 'translate(-50%, -50%)', width: '12px', height: '24px' } },
        { style: { left: '-8px', top: '30%', transform: 'translate(-50%, -50%)', width: '12px', height: '24px' } }
      )
    } else if (capacity === 7) {
      // 7 chairs: 3 on one long side, 2 on other long side, 1 on each short side
      chairs.push(
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '20px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '25%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } },
        { style: { left: 'calc(100% + 8px)', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } },
        { style: { left: 'calc(100% + 8px)', top: '75%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } },
        { style: { left: '50%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '20px', height: '12px' } },
        { style: { left: '-8px', top: '40%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } },
        { style: { left: '-8px', top: '60%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } }
      )
    } else if (capacity === 8) {
      // 8 chairs: 2 on each side
      chairs.push(
        { style: { left: '35%', top: '-10px', transform: 'translate(-50%, -50%)', width: '20px', height: '12px' } },
        { style: { left: '65%', top: '-10px', transform: 'translate(-50%, -50%)', width: '20px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '35%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } },
        { style: { left: 'calc(100% + 8px)', top: '65%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } },
        { style: { left: '65%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '20px', height: '12px' } },
        { style: { left: '35%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '20px', height: '12px' } },
        { style: { left: '-8px', top: '65%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } },
        { style: { left: '-8px', top: '35%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } }
      )
    } else if (capacity === 9) {
      // 9 chairs: 3 on top, 3 on bottom, 3 on sides (distributed)
      chairs.push(
        { style: { left: '25%', top: '-10px', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '75%', top: '-10px', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '30%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } },
        { style: { left: 'calc(100% + 8px)', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } },
        { style: { left: 'calc(100% + 8px)', top: '70%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } },
        { style: { left: '25%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '50%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '-8px', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } }
      )
    } else if (capacity === 10) {
      // 10 chairs: 3 on top, 3 on bottom, 2 on each side
      chairs.push(
        { style: { left: '25%', top: '-10px', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '75%', top: '-10px', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '35%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } },
        { style: { left: 'calc(100% + 8px)', top: '65%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } },
        { style: { left: '75%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '50%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '25%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '18px', height: '12px' } },
        { style: { left: '-8px', top: '65%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } },
        { style: { left: '-8px', top: '35%', transform: 'translate(-50%, -50%)', width: '12px', height: '20px' } }
      )
    } else if (capacity === 11) {
      // 11 chairs: 3 on top, 3 on bottom, 3 on right side, 2 on left side
      chairs.push(
        { style: { left: '25%', top: '-10px', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '75%', top: '-10px', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '25%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } },
        { style: { left: 'calc(100% + 8px)', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } },
        { style: { left: 'calc(100% + 8px)', top: '75%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } },
        { style: { left: '75%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '50%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '25%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '-8px', top: '40%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } },
        { style: { left: '-8px', top: '60%', transform: 'translate(-50%, -50%)', width: '12px', height: '18px' } }
      )
    } else if (capacity === 12) {
      // 12 chairs: 3 on each side
      chairs.push(
        { style: { left: '25%', top: '-10px', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '50%', top: '-10px', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '75%', top: '-10px', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: 'calc(100% + 8px)', top: '25%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } },
        { style: { left: 'calc(100% + 8px)', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } },
        { style: { left: 'calc(100% + 8px)', top: '75%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } },
        { style: { left: '75%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '50%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '25%', top: 'calc(100% + 10px)', transform: 'translate(-50%, -50%)', width: '16px', height: '12px' } },
        { style: { left: '-8px', top: '75%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } },
        { style: { left: '-8px', top: '50%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } },
        { style: { left: '-8px', top: '25%', transform: 'translate(-50%, -50%)', width: '12px', height: '16px' } }
      )
    } else {
      // For capacity > 12: distribute around perimeter (circular layout)
      for (let i = 0; i < capacity; i++) {
        const angle = (360 / capacity) * i
        const radians = (angle * Math.PI) / 180
        const radius = Math.min(40, 30 + capacity) // Adjust radius based on capacity

        const x = 50 + radius * Math.cos(radians)
        const y = 50 + radius * Math.sin(radians)

        chairs.push({
          style: {
            left: `${x}%`,
            top: `${y}%`,
            transform: 'translate(-50%, -50%)',
            width: '14px',
            height: '10px'
          }
        })
      }
    }

    return chairs
  } catch (error) {
    console.error('Error in getChairPositions:', error)
    return []
  }
}

// Tab configuration
const tabs = computed(() => [
  {
    id: 'tables',
    label: 'Tables',
    icon: 'grid',
    count: undefined
  },
  {
    id: 'orders',
    label: 'Orders',
    icon: 'receipt',
    count: activeOrders.value.length
  },
  {
    id: 'qr-menus',
    label: 'QR Menus',
    icon: 'qr-code',
    count: undefined
  },
  {
    id: 'kitchen',
    label: 'Kitchen',
    icon: 'chef-hat',
    count: undefined
  }
])

// Load data
const loadTables = async () => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    isLoading.value = true
    error.value = ''

    console.log('Loading tables for company:', authStore.currentUser.currentCompanyId)

    const tableData = await restaurantService.getTables(authStore.currentUser.currentCompanyId)
    tables.value = tableData

    console.log('Loaded tables:', tableData.length)
    console.log('Table data structure:', tableData)

    // Debug individual table data
    if (tableData.length > 0) {
      console.log('First table sample:', tableData[0])
      console.log('Table properties:', Object.keys(tableData[0]))
      console.log('First table capacity:', tableData[0].capacity)
      console.log('First table status:', tableData[0].status)
    }

    // Initialize table positions for draggable system
    initializeTablePositions()

    if (tableData.length === 0) {
      console.log('No tables found, offering to initialize defaults')
      toast.info('No tables found', {
        description: 'Use the "Create Table" button in the sidebar to add your first table',
        duration: 6000
      })
    }
  } catch (err) {
    console.error('Failed to load tables:', err)
    error.value = 'Failed to load tables. Please try again.'

    toast.error('Failed to load tables', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  } finally {
    isLoading.value = false
  }
}



const loadActiveOrders = async () => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    // Get active orders for all tables
    const allOrders: RestaurantOrder[] = []

    for (const table of tables.value) {
      if (!table || !table._id || !table.tableNumber) {
        console.warn('Skipping invalid table:', table)
        continue
      }

      try {
        const tableOrders = await restaurantService.getTableOrders(
          authStore.currentUser.currentCompanyId,
          table._id,
          true // activeOnly
        )
        allOrders.push(...tableOrders)
      } catch (err) {
        console.warn(`Failed to load orders for table ${table.tableNumber}:`, err)
      }
    }

    activeOrders.value = allOrders
    console.log('Loaded active orders:', allOrders.length)
  } catch (err) {
    console.error('Failed to load active orders:', err)
  }
}

// Floor Management Functions
const loadFloors = async () => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    floorsLoading.value = true
    const floorData = await restaurantService.getFloors(authStore.currentUser.currentCompanyId)
    floors.value = floorData
    console.log('Loaded floors:', floorData.length)
  } catch (err) {
    console.error('Failed to load floors:', err)
    toast.error('Failed to load floors', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  } finally {
    floorsLoading.value = false
  }
}

const onFloorChanged = async (floorId: string | null) => {
  selectedFloorId.value = floorId || ''

  // Save to localStorage
  saveSelectedFloorToStorage(floorId || '')

  // Reload tables for the selected floor
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    isLoading.value = true
    const tableData = await restaurantService.getTables(authStore.currentUser.currentCompanyId, floorId || undefined)
    tables.value = tableData
    initializeTablePositions()
    console.log(`Loaded ${tableData.length} tables for floor:`, floorId || 'all floors')
  } catch (err) {
    console.error('Failed to load tables for floor:', err)
    toast.error('Failed to load tables for floor', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  } finally {
    isLoading.value = false
  }
}

const refreshFloors = async () => {
  await loadFloors()
  toast.success('Floors refreshed')
}

const onFloorsUpdated = async () => {
  await loadFloors()
  // Reload tables in case floor assignments changed
  await loadTables()
}

// Table actions
const updateTableStatus = async (table: RestaurantTable, newStatus: RestaurantTable['status']) => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    console.log(`Updating table ${table.tableNumber} status to ${newStatus}`)

    const updatedTable = await restaurantService.updateTableStatus(
      authStore.currentUser.currentCompanyId,
      table._id,
      newStatus
    )

    // Update local table data
    const index = tables.value.findIndex(t => t._id === table._id)
    if (index !== -1) {
      tables.value[index] = updatedTable
    }

    toast.success(`Table ${table.tableNumber} status updated`, {
      description: `Status changed to ${tableStatusConfig[newStatus].label}`
    })

    // Reload orders after status change
    await loadActiveOrders()
  } catch (err) {
    console.error('Failed to update table status:', err)
    toast.error('Failed to update table status', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  }
}

const createQRMenu = async (table: RestaurantTable, menuType: 'standard' | 'bbq_unlimited' = 'standard') => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    console.log(`Creating QR menu for table ${table.tableNumber}, type: ${menuType}`)

    const qrMenu = await restaurantService.createQRMenu(
      authStore.currentUser.currentCompanyId,
      table._id,
      table.tableNumber,
      menuType
    )

    qrMenus.value.push(qrMenu)

    toast.success(`QR Menu Created! 📱`, {
      description: `${menuType === 'bbq_unlimited' ? 'BBQ Unlimited' : 'Standard'} menu for Table ${table.tableNumber}`
    })

    console.log('QR Menu created:', qrMenu)
  } catch (err) {
    console.error('Failed to create QR menu:', err)
    toast.error('Failed to create QR menu', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  }
}

const startBBQSession = async (table: RestaurantTable) => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    const partySizeStr = prompt('Enter party size for BBQ unlimited session:')
    if (!partySizeStr) return

    const partySize = parseInt(partySizeStr, 10)
    if (isNaN(partySize) || partySize < 1) {
      toast.error('Invalid party size', {
        description: 'Please enter a valid number greater than 0'
      })
      return
    }

    console.log(`Starting BBQ session for table ${table.tableNumber}, party size: ${partySize}`)

    // Create QR menu first
    const qrMenu = await restaurantService.createQRMenu(
      authStore.currentUser.currentCompanyId,
      table._id,
      table.tableNumber,
      'bbq_unlimited'
    )

    // Start BBQ session
    const bbqOrder = await restaurantService.startBBQSession(
      authStore.currentUser.currentCompanyId,
      qrMenu._id,
      partySize
    )

    // Update table status to occupied
    await updateTableStatus(table, 'occupied')

    // Add to active orders
    activeOrders.value.push(bbqOrder)

    toast.success(`BBQ Session Started! 🔥`, {
      description: `90-minute unlimited BBQ for ${partySize} people at Table ${table.tableNumber}`
    })

    console.log('BBQ session started:', bbqOrder)
  } catch (err) {
    console.error('Failed to start BBQ session:', err)
    toast.error('Failed to start BBQ session', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  }
}

// Order actions
const updateOrderStatus = async (order: RestaurantOrder, newStatus: RestaurantOrder['status']) => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    console.log(`Updating order ${order._id} status to ${newStatus}`)

    const updatedOrder = await restaurantService.updateOrderStatus(
      authStore.currentUser.currentCompanyId,
      order._id,
      newStatus
    )

    // Update local order data
    const index = activeOrders.value.findIndex(o => o._id === order._id)
    if (index !== -1) {
      activeOrders.value[index] = updatedOrder
    }

    toast.success(`Order #${order._id} updated`, {
      description: `Status changed to ${orderStatusConfig[newStatus].label}`
    })

    console.log('Order status updated:', updatedOrder)
  } catch (err) {
    console.error('Failed to update order status:', err)
    toast.error('Failed to update order status', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  }
}

// Initialize Khmer categories
const initializeKhmerCategories = async () => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    console.log('Initializing Khmer food categories...')

    await restaurantService.initializeKhmerCategories(authStore.currentUser.currentCompanyId)

    toast.success('Khmer Food Categories Created! 🇰🇭', {
      description: 'Traditional Cambodian restaurant categories are now available'
    })

    console.log('Khmer food categories initialized')
  } catch (err) {
    console.error('Failed to initialize Khmer categories:', err)
    toast.error('Failed to create categories', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  }
}

// Toggle arrange mode
const toggleArrangeMode = () => {
  if (isArrangeMode.value) {
    // Save layout when exiting arrange mode
    saveTableLayout()
  } else {
    // When entering arrange mode, initialize positions if not already set
    tables.value.forEach((table, index) => {
      if (!tablePositions.value[table._id]) {
        tablePositions.value[table._id] = calculateGridPosition(index)
      }
    })
    isArrangeMode.value = true
  }
}

// Table position tracking for drag and drop
const tablePositions = ref<{ [tableId: string]: { x: number; y: number } }>({})
const isSavingLayout = ref(false)

// Calculate grid position for a table based on its index
const calculateGridPosition = (index: number) => {
  const GRID_COLS = 5 // 5 tables per row
  const CELL_WIDTH = 250  // Increased to accommodate larger tables
  const CELL_HEIGHT = 200 // Increased to accommodate larger tables
  const GRID_GAP = 60    // Adjusted gap for better spacing

  const col = index % GRID_COLS
  const row = Math.floor(index / GRID_COLS)

  return {
    x: col * (CELL_WIDTH + GRID_GAP) + 40, // 40px initial padding
    y: row * (CELL_HEIGHT + GRID_GAP) + 40  // 40px initial padding
  }
}

// Reset table layout to default grid
const resetTableLayout = async () => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    isResettingLayout.value = true

    // Calculate grid positions for each table
    const updates = tables.value.map((table, index) => ({
      tableId: table._id,
      position: calculateGridPosition(index)
    }))

    if (updates.length === 0) {
      isResettingLayout.value = false
      return
    }

    console.log('Resetting layout with grid positions:', updates)

    const success = await restaurantService.updateMultipleTablePositions(
      authStore.currentUser.currentCompanyId,
      updates
    )

    if (success) {
      // Update local positions with calculated grid positions
      tables.value.forEach((table, index) => {
        const gridPos = calculateGridPosition(index)
        table.position = gridPos
        tablePositions.value[table._id] = gridPos
      })
      hasCustomPositions.value = false

      toast.success('Layout Reset! 🔄', {
        description: 'Tables arranged in grid layout'
      })
    } else {
      throw new Error('Failed to reset table positions')
    }
  } catch (err) {
    console.error('Failed to reset layout:', err)
    toast.error('Failed to reset layout', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  } finally {
    isResettingLayout.value = false
  }
}

// Initialize table positions from loaded data
const initializeTablePositions = () => {
  tablePositions.value = {}
  let hasCustomPos = false

  tables.value.forEach((table, index) => {
    if (table.position && (table.position.x !== 0 || table.position.y !== 0)) {
      tablePositions.value[table._id] = {
        x: table.position.x,
        y: table.position.y
      }
      hasCustomPos = true
    } else {
      // Use grid position for tables without custom positions
      tablePositions.value[table._id] = calculateGridPosition(index)
    }
  })

  hasCustomPositions.value = hasCustomPos
  console.log('Initialized table positions:', tablePositions.value)
  console.log('Has custom positions:', hasCustomPos)
}

// Handle drag end event - get position from neodrag event
const handleDragEnd = (tableId: string, event: any) => {
  if (!isArrangeMode.value) return

  // neodrag provides the final position in the event detail
  const { offsetX, offsetY } = event.detail || {}

  if (typeof offsetX === 'number' && typeof offsetY === 'number') {
    // Update local position tracking
    tablePositions.value[tableId] = { x: offsetX, y: offsetY }
    hasCustomPositions.value = true

    console.log(`Table ${tableId} moved to position:`, { x: offsetX, y: offsetY })
  } else {
    console.warn('Invalid drag event data for table:', tableId, event)
  }
}

// Save table layout to backend
const saveTableLayout = async () => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    isSavingLayout.value = true

    // Prepare updates array for tables that have moved
    const updates = Object.entries(tablePositions.value)
      .filter(([tableId, position]) => {
        const table = tables.value.find(t => t._id === tableId)
        return table && (
          table.position?.x !== position.x ||
          table.position?.y !== position.y
        )
      })
      .map(([tableId, position]) => ({
        tableId,
        position
      }))

    if (updates.length === 0) {
      isArrangeMode.value = false
      toast.success('Layout Saved', {
        description: 'No changes to save'
      })
      return
    }

    console.log('Saving table layout with updates:', updates)

    const success = await restaurantService.updateMultipleTablePositions(
      authStore.currentUser.currentCompanyId,
      updates
    )

    if (success) {
      // Update local table data with new positions
      updates.forEach(({ tableId, position }) => {
        const table = tables.value.find(t => t._id === tableId)
        if (table) {
          table.position = position
        }
      })

      isArrangeMode.value = false
      toast.success('Layout Saved! 💾', {
        description: `Updated positions for ${updates.length} tables`
      })
    } else {
      throw new Error('Failed to save some table positions')
    }
  } catch (err) {
    console.error('Failed to save table layout:', err)
    toast.error('Failed to save layout', {
      description: err instanceof Error ? err.message : 'Unknown error occurred'
    })
  } finally {
    isSavingLayout.value = false
  }
}

// Cancel arrange mode without saving
const cancelArrangeMode = () => {
  isArrangeMode.value = false
  initializeTablePositions() // Reset positions to original values
  toast.info('Arrange Mode Cancelled', {
    description: 'Changes discarded'
  })
}

// Create Table Dialog state
const showCreateTableDialog = ref(false)
const newTableCapacity = ref(4)
const newTableNumber = ref('')
const isCreatingTable = ref(false)
const isLoadingNextNumber = ref(false)
const tableNumberError = ref('')

// Load next available table number
const loadNextTableNumber = async () => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    isLoadingNextNumber.value = true
    const nextNumber = await restaurantService.getNextTableNumber(authStore.currentUser.currentCompanyId)
    newTableNumber.value = nextNumber
    tableNumberError.value = ''
  } catch (err) {
    console.error('Failed to get next table number:', err)
    newTableNumber.value = (tables.value.length + 1).toString() // Fallback
  } finally {
    isLoadingNextNumber.value = false
  }
}

// Validate table number availability
const validateTableNumber = async () => {
  if (!authStore.currentUser?.currentCompanyId || !newTableNumber.value.trim()) {
    tableNumberError.value = ''
    return
  }

  try {
    const isAvailable = await restaurantService.checkTableNumberAvailability(
      authStore.currentUser.currentCompanyId,
      newTableNumber.value.trim()
    )

    if (!isAvailable) {
      tableNumberError.value = `Table number "${newTableNumber.value}" is already taken`
    } else {
      tableNumberError.value = ''
    }
  } catch (err) {
    console.error('Failed to validate table number:', err)
    tableNumberError.value = 'Unable to validate table number'
  }
}

// Create table function
const createNewTable = async () => {
  if (!authStore.currentUser?.currentCompanyId) return

  // Validate table number is provided
  if (!newTableNumber.value.trim()) {
    tableNumberError.value = 'Table number is required'
    return
  }

  // Check for validation errors
  if (tableNumberError.value) {
    toast.error('Cannot create table', {
      description: tableNumberError.value
    })
    return
  }

  try {
    isCreatingTable.value = true

    // Determine table size and shape based on capacity
    let size: 'small' | 'medium' | 'large' = 'medium'
    let shape: 'square' | 'round' | 'rectangle' = 'rectangle'

    if (newTableCapacity.value <= 2) {
      size = 'small'
      shape = 'square'
    } else if (newTableCapacity.value <= 4) {
      size = 'medium'
      shape = 'square'
    } else if (newTableCapacity.value <= 8) {
      size = 'large'
      shape = 'rectangle'
    } else {
      size = 'large'
      shape = 'round'
    }

    console.log(`Creating new table ${newTableNumber.value} with capacity ${newTableCapacity.value}`)

    const newTable = await restaurantService.createTable(authStore.currentUser.currentCompanyId, {
      tableNumber: newTableNumber.value.trim(),
      capacity: newTableCapacity.value,
      status: 'available',
      position: { x: 0, y: 0 }, // Default position
      shape,
      size,
      isVipTable: false,
      // Assign to currently selected floor (if any)
      floorId: selectedFloorId.value || undefined,
      floorName: selectedFloorId.value ? floors.value.find(f => f._id === selectedFloorId.value)?.name : undefined
    })

    // Add to local tables array only if it belongs to the current floor filter
    if (!selectedFloorId.value || newTable.floorId === selectedFloorId.value) {
      tables.value.push(newTable)
    }

    // Re-initialize positions for all tables
    initializeTablePositions()

    toast.success(`Table ${newTableNumber.value} Created! 🍽️`, {
      description: `${newTableCapacity.value} seats${selectedFloorId.value ? ` on ${floors.value.find(f => f._id === selectedFloorId.value)?.name}` : ''} with ${getChairPositions(newTableCapacity.value).length} chairs arranged`
    })

    // Reset form
    showCreateTableDialog.value = false
    newTableCapacity.value = 4
    newTableNumber.value = ''
    tableNumberError.value = ''

    // Reload floors to update table counts
    await loadFloors()

    console.log('New table created:', newTable)
  } catch (err) {
    console.error('Failed to create table:', err)
    const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'

    // Handle specific backend validation errors
    if (errorMessage.includes('already exists')) {
      tableNumberError.value = errorMessage
      toast.error('Table number already exists', {
        description: 'Please choose a different table number'
      })
    } else {
      toast.error('Failed to create table', {
        description: errorMessage
      })
    }
  } finally {
    isCreatingTable.value = false
  }
}

// Watch for dialog open to load next table number
watch(showCreateTableDialog, (isOpen) => {
  if (isOpen) {
    loadNextTableNumber()
  }
})

// Watch table number changes for real-time validation
watch(newTableNumber, () => {
  if (newTableNumber.value.trim()) {
    validateTableNumber()
  } else {
    tableNumberError.value = ''
  }
})

// Lifecycle
onMounted(async () => {
  loadPreferences()
  loadSelectedFloorFromStorage() // Restore saved floor selection

  if (authStore.currentUser?.currentCompanyId) {
    await loadFloors()

    // Load tables for the selected floor (or all tables if no floor selected)
    const floorToLoad = selectedFloorId.value || undefined
    const tableData = await restaurantService.getTables(authStore.currentUser.currentCompanyId, floorToLoad)
    tables.value = tableData
    initializeTablePositions()
    console.log(`Initial load: ${tableData.length} tables for floor:`, floorToLoad || 'all floors')

    await loadActiveOrders()
  }
})

// Watch for company changes
watch(
  () => authStore.currentUser?.currentCompanyId,
  async (newCompanyId) => {
    if (newCompanyId) {
      tables.value = []
      activeOrders.value = []
      qrMenus.value = []
      floors.value = []
      // Don't reset selectedFloorId - keep the user's preference
      error.value = ''

      await loadFloors()

      // Load tables for the selected floor
      const floorToLoad = selectedFloorId.value || undefined
      const tableData = await restaurantService.getTables(newCompanyId, floorToLoad)
      tables.value = tableData
      initializeTablePositions()
      console.log(`Company change: ${tableData.length} tables for floor:`, floorToLoad || 'all floors')

      await loadActiveOrders()
    }
  }
)

// Layout info text
const layoutInfo = computed(() => {
  return `${tables.value.length} tables • 5 per row`
})

// Arrange Mode Enhancement Functions

// Generate next available table number
const generateNextTableNumber = (baseName: string): string => {
  const existingNumbers = tables.value
    .map(t => t.tableNumber)
    .filter(num => num.startsWith(baseName))
    .map(num => {
      const match = num.match(/(\d+)$/)
      return match ? parseInt(match[1]) : 0
    })
    .sort((a, b) => b - a)

  const nextNumber = existingNumbers.length > 0 ? existingNumbers[0] + 1 : 1
  return `${baseName}${nextNumber}`
}

// Duplicate table with auto-generated name
const duplicateTable = async (originalTable: RestaurantTable) => {
  if (!authStore.currentUser?.currentCompanyId) return

  try {
    isDuplicatingTable.value = true

    // Generate new table number
    const baseNumber = originalTable.tableNumber.replace(/\d+$/, '') || 'Table '
    const newTableNumber = generateNextTableNumber(baseNumber)

    // Get current position or generate offset position
    const originalPos = tablePositions.value[originalTable._id] || { x: 0, y: 0 }
    const offsetX = 50 // 50px offset
    const offsetY = 50

    const duplicatedTable = await restaurantService.createTable(authStore.currentUser.currentCompanyId, {
      tableNumber: newTableNumber,
      capacity: originalTable.capacity,
      shape: originalTable.shape || 'round',
      size: originalTable.size || 'medium',
      status: 'available',
      floorId: selectedFloorId.value || originalTable.floorId,
      floorName: selectedFloorId.value ? floors.value.find(f => f._id === selectedFloorId.value)?.name : originalTable.floorName,
      position: {
        x: originalPos.x + offsetX,
        y: originalPos.y + offsetY
      }
    })

    // Add to local tables array if it matches current floor filter
    if (!selectedFloorId.value || duplicatedTable.floorId === selectedFloorId.value) {
      tables.value.push(duplicatedTable)

      // Set position for the new table
      tablePositions.value[duplicatedTable._id] = {
        x: originalPos.x + offsetX,
        y: originalPos.y + offsetY
      }
    }

    toast.success(`Table Duplicated! 🍽️`, {
      description: `Created "${newTableNumber}" with ${duplicatedTable.capacity} seats${selectedFloorId.value ? ` on ${floors.value.find(f => f._id === selectedFloorId.value)?.name}` : ''}`
    })

    // Update floor table counts
    await refreshFloors()

  } catch (err) {
    console.error('Failed to duplicate table:', err)
    toast.error('Failed to duplicate table', {
      description: err instanceof Error ? err.message : 'Please try again'
    })
  } finally {
    isDuplicatingTable.value = false
  }
}

// Start editing table name
const startEditingTableName = (table: RestaurantTable) => {
  editingTableId.value = table._id
  editingTableName.value = table.tableNumber
  selectedTableForActions.value = table._id
}

// Save table name
const saveTableName = async (tableId: string) => {
  if (!authStore.currentUser?.currentCompanyId || !editingTableName.value.trim()) {
    cancelEditingTableName()
    return
  }

  try {
    // Check if name already exists
    const existingTable = tables.value.find(t => t._id !== tableId && t.tableNumber.trim() === editingTableName.value.trim())
    if (existingTable) {
      toast.error('Table number already exists', {
        description: 'Please choose a different table number'
      })
      return
    }

    const table = tables.value.find(t => t._id === tableId)
    if (!table) return

    // TODO: Add updateTable method to restaurantService for updating table properties
    // For now, just update local state
    const index = tables.value.findIndex(t => t._id === tableId)
    if (index !== -1) {
      tables.value[index] = { ...tables.value[index], tableNumber: editingTableName.value.trim() }
    }

    toast.success('Table renamed successfully! ✨', {
      description: `Table renamed to "${editingTableName.value}" (local only - server update pending)`
    })

    cancelEditingTableName()

  } catch (err) {
    console.error('Failed to rename table:', err)
    toast.error('Failed to rename table', {
      description: err instanceof Error ? err.message : 'Please try again'
    })
  }
}

// Cancel editing table name
const cancelEditingTableName = () => {
  editingTableId.value = null
  editingTableName.value = ''
  selectedTableForActions.value = null
}

// Delete table (using status update workaround until DELETE endpoint is fixed)
const deleteTable = async (table: RestaurantTable) => {
  if (!authStore.currentUser?.currentCompanyId) return

  // Confirm deletion
  if (!confirm(`Are you sure you want to delete table "${table.tableNumber}"? This action cannot be undone.`)) {
    return
  }

  try {
    // Use the working PATCH endpoint via API Gateway to set status to 'maintenance' as a delete workaround
    const response = await fetch(`http://localhost:3000/api/pos/restaurant/tables/${table._id}/status`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        companyId: authStore.currentUser.currentCompanyId,
        status: 'maintenance'
      })
    })

    const result = await response.json()

    if (result.success) {
      // Remove from local state
      const index = tables.value.findIndex(t => t._id === table._id)
      if (index !== -1) {
        tables.value.splice(index, 1)
      }

      // Remove position data
      delete tablePositions.value[table._id]

      toast.success('Table deleted successfully! 🗑️', {
        description: `Table "${table.tableNumber}" has been removed`
      })

      // Update floor table counts
      await refreshFloors()
    } else {
      throw new Error(result.error || 'Failed to delete table')
    }
  } catch (err) {
    console.error('Failed to delete table:', err)
    const errorMessage = err instanceof Error ? err.message : 'Please try again'

    if (errorMessage.includes('occupied')) {
      toast.error('Cannot delete occupied table', {
        description: 'Please clear the table before deleting it'
      })
    } else if (errorMessage.includes('reserved')) {
      toast.error('Cannot delete reserved table', {
        description: 'Please cancel the reservation before deleting the table'
      })
    } else {
      toast.error('Failed to delete table', {
        description: errorMessage
      })
    }
  }
}

// Watch for dialog open to load next table number
</script>

<template>
  <div class="min-h-[calc(100vh-100px)] bg-background">
    <!-- Main Layout -->
    <div class="h-[calc(100vh-100px)] flex">
      <!-- Sidebar -->
      <div
        :class="[
          'transition-all duration-300 border-r bg-muted/10',
          sidebarOpen ? 'w-80' : 'w-0'
        ]"
        class="overflow-hidden"
      >
        <div class="p-6 space-y-6 overflow-y-auto">
          <!-- Sidebar Header -->
          <div class="space-y-2">
            <h3 class="font-semibold text-lg">Restaurant Management</h3>
            <p class="text-sm text-muted-foreground">
              Manage your restaurant tables and orders efficiently
            </p>
          </div>

          <Separator />

          <!-- Quick Stats -->
          <div class="space-y-3">
            <h4 class="font-medium text-sm text-muted-foreground uppercase tracking-wide">Quick Stats</h4>
            <div class="space-y-2">
              <div
                v-for="stat in quickStats"
                :key="stat.title"
                class="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
              >
                <!-- Icon -->
                <div
                  :class="[
                    'w-8 h-8 rounded-md flex items-center justify-center flex-shrink-0',
                    stat.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                    stat.color === 'green' ? 'bg-green-100 text-green-600' :
                    stat.color === 'orange' ? 'bg-orange-100 text-orange-600' :
                    'bg-purple-100 text-purple-600'
                  ]"
                >
                  <component :is="stat.icon" class="w-4 h-4" />
                </div>

                <!-- Content -->
                <div class="flex-1 min-w-0">
                  <div class="flex items-center justify-between">
                    <p class="text-xs font-medium text-muted-foreground truncate">{{ stat.title }}</p>
                    <p class="text-lg font-bold">{{ stat.value }}</p>
                  </div>
                  <p class="text-xs text-muted-foreground/80 truncate">{{ stat.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          <!-- Quick Actions -->
          <div class="space-y-4">
            <h4 class="font-medium text-sm text-muted-foreground uppercase tracking-wide">Quick Actions</h4>
            <div class="space-y-2">
              <!-- Create Table Button with Dialog -->
              <Dialog v-model:open="showCreateTableDialog">
                <DialogTrigger as-child>
                  <Button
                    variant="outline"
                    class="w-full justify-start"
                  >
                    <Plus class="w-4 h-4 mr-2" />
                    Create Table
                  </Button>
                </DialogTrigger>
                <DialogContent class="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Create New Table</DialogTitle>
                    <DialogDescription>
                      Create a new restaurant table with custom seating capacity. Chairs will be automatically arranged based on the capacity.
                    </DialogDescription>
                  </DialogHeader>

                                    <div class="space-y-6 py-4">
                    <!-- Table Number Input -->
                    <div class="space-y-2">
                      <Label for="tableNumber">Table Number</Label>
                      <div class="relative">
                        <Input
                          id="tableNumber"
                          v-model="newTableNumber"
                          placeholder="Enter table number"
                          :class="tableNumberError ? 'border-red-500 focus:border-red-500' : ''"
                          :disabled="isLoadingNextNumber"
                        />
                        <div v-if="isLoadingNextNumber" class="absolute right-2 top-1/2 transform -translate-y-1/2">
                          <div class="w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
                        </div>
                      </div>
                      <div v-if="tableNumberError" class="text-sm text-red-600 flex items-center gap-1">
                        <span>⚠️</span>
                        {{ tableNumberError }}
                      </div>
                      <div v-else-if="newTableNumber && !tableNumberError" class="text-sm text-green-600 flex items-center gap-1">
                        <span>✅</span>
                        Table number is available
                      </div>
                    </div>

                    <!-- Table Capacity Selection -->
                    <div class="space-y-2">
                      <Label for="capacity">Table Capacity (2-12 seats)</Label>
                      <Select :model-value="newTableCapacity.toString()" @update:model-value="(value) => value && (newTableCapacity = parseInt(String(value)))">
                        <SelectTrigger>
                          <SelectValue placeholder="Select capacity" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="2">2 seats</SelectItem>
                          <SelectItem value="3">3 seats</SelectItem>
                          <SelectItem value="4">4 seats</SelectItem>
                          <SelectItem value="5">5 seats</SelectItem>
                          <SelectItem value="6">6 seats</SelectItem>
                          <SelectItem value="7">7 seats</SelectItem>
                          <SelectItem value="8">8 seats</SelectItem>
                          <SelectItem value="9">9 seats</SelectItem>
                          <SelectItem value="10">10 seats</SelectItem>
                          <SelectItem value="11">11 seats</SelectItem>
                          <SelectItem value="12">12 seats</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <!-- Table Preview -->
                    <div class="space-y-2">
                      <Label>Table Preview</Label>
                      <div class="relative w-32 h-24 mx-auto border-2 border-dashed border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center">
                        <!-- Preview chairs -->
                        <div
                          v-for="(chair, index) in getChairPositions(newTableCapacity).slice(0, 8)"
                          :key="index"
                          class="absolute bg-gray-400 rounded-sm"
                          :style="{
                            left: chair.style.left,
                            top: chair.style.top,
                            transform: chair.style.transform,
                            width: '8px',
                            height: '6px'
                          }"
                        ></div>

                        <!-- Preview table -->
                        <div class="relative bg-white border-2 border-gray-400 rounded-lg w-16 h-12 flex items-center justify-center">
                          <span class="text-xs font-semibold text-gray-600">
                            {{ newTableNumber || 'T#' }}
                          </span>
                        </div>

                        <!-- Show remaining chairs count if more than 8 -->
                        <div v-if="getChairPositions(newTableCapacity).length > 8" class="absolute -bottom-4 text-xs text-gray-500">
                          +{{ getChairPositions(newTableCapacity).length - 8 }} more chairs
                        </div>
                      </div>
                      <div class="text-center text-sm text-muted-foreground">
                        {{ getChairPositions(newTableCapacity).length }} chairs will be arranged
                      </div>
                    </div>
                  </div>

                  <DialogFooter>
                    <Button variant="outline" @click="showCreateTableDialog = false" :disabled="isCreatingTable">
                      Cancel
                    </Button>
                    <Button
                      @click="createNewTable"
                      :disabled="isCreatingTable || !newTableNumber.trim() || !!tableNumberError || isLoadingNextNumber"
                    >
                      <span v-if="isCreatingTable" class="flex items-center">
                        <div class="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Creating...
                      </span>
                      <span v-else class="flex items-center">
                        <Plus class="w-4 h-4 mr-2" />
                        Create Table
                      </span>
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>


              <Button @click="initializeKhmerCategories" variant="outline" class="w-full justify-start">
                <Package class="w-4 h-4 mr-2" />
                Setup Menu
              </Button>
              <Button variant="outline" class="w-full justify-start">
                <Settings class="w-4 h-4 mr-2" />
                Restaurant Settings
              </Button>
            </div>
          </div>

          <Separator />

          <!-- Status Overview -->
          <div class="space-y-4">
            <h4 class="font-medium text-sm text-muted-foreground uppercase tracking-wide">Status Overview</h4>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-emerald-500"></div>
                  <span class="text-sm">Available</span>
                </div>
                <span class="text-sm font-medium">{{ restaurantStats.availableTables }}</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-rose-500"></div>
                  <span class="text-sm">Occupied</span>
                </div>
                <span class="text-sm font-medium">{{ restaurantStats.occupiedTables }}</span>
              </div>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <div class="w-3 h-3 rounded-full bg-amber-500"></div>
                  <span class="text-sm">Reserved</span>
                </div>
                <span class="text-sm font-medium">{{ restaurantStats.reservedTables }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Header -->
        <div class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div class="px-6 py-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-4">
                <Button variant="ghost" size="icon" @click="toggleSidebar">
                  <Grid3x3 class="w-4 h-4" />
                </Button>

                <div class="flex items-center gap-2">
                  <Button variant="ghost" size="sm" @click="goBack">
                    <ArrowLeft class="w-4 h-4 mr-2" />
                    Back
                  </Button>
                  <Separator orientation="vertical" class="h-4" />
                  <nav class="flex items-center gap-2 text-sm text-muted-foreground">
                    <span>Dashboard</span>
                    <span>/</span>
                    <span>POS</span>
                    <span>/</span>
                    <span class="text-foreground font-medium">Restaurant</span>
                  </nav>
                </div>
              </div>

              <div class="flex items-center gap-3">
                <!-- View Mode Toggle -->
                <div class="flex items-center border rounded-lg p-1 space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    :class="viewPreference === 'grid' ? 'bg-muted' : ''"
                    @click="viewPreference = 'grid'"
                  >
                    <Grid3x3 class="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    :class="viewPreference === 'list' ? 'bg-muted' : ''"
                    @click="viewPreference = 'list'"
                  >
                    <List class="w-4 h-4" />
                  </Button>
                </div>

                <!-- Table Layout Controls -->
                <div class="flex items-center gap-2">
                  <!-- Arrange Tables Button -->
                  <Button
                    variant="outline"
                    @click="toggleArrangeMode"
                    :class="isArrangeMode ? 'bg-blue-50 border-blue-300 text-blue-700' : ''"
                    :disabled="isSavingLayout || isResettingLayout"
                  >
                    <Move :class="['w-4 h-4 mr-2', isArrangeMode && 'text-blue-600']" />
                    <span v-if="isSavingLayout" class="flex items-center">
                      <div class="w-3 h-3 mr-2 border-2 border-blue-300 border-t-blue-600 rounded-full animate-spin"></div>
                      Saving...
                    </span>
                    <span v-else>
                      {{ isArrangeMode ? 'Save Layout' : 'Arrange Tables' }}
                    </span>
                  </Button>

                  <!-- Reset Layout Button -->
                  <Button
                    v-if="hasCustomPositions"
                    variant="outline"
                    @click="resetTableLayout"
                    :disabled="isArrangeMode || isResettingLayout"
                    class="text-amber-700 border-amber-200 hover:bg-amber-50 hover:text-amber-800"
                  >
                    <span v-if="isResettingLayout" class="flex items-center">
                      <div class="w-3 h-3 mr-2 border-2 border-amber-300 border-t-amber-600 rounded-full animate-spin"></div>
                      Resetting...
                    </span>
                    <span v-else class="flex items-center">
                      <RotateCcw class="w-4 h-4 mr-2" />
                      Reset Layout
                    </span>
                  </Button>

                  <!-- Cancel Arrange Mode Button -->
                  <Button
                    v-if="isArrangeMode"
                    variant="outline"
                    @click="cancelArrangeMode"
                    :disabled="isSavingLayout || isResettingLayout"
                    class="text-gray-600 border-gray-300 hover:bg-gray-50"
                  >
                    Cancel
                  </Button>
                </div>

                <!-- Refresh Button -->
                <Button variant="outline" @click="refreshData" :disabled="isRefreshing || isResettingLayout">
                  <RefreshCw :class="['w-4 h-4 mr-2', isRefreshing && 'animate-spin']" />
                  Refresh
                </Button>

                <Button>
                  <Eye class="w-4 h-4 mr-2" />
                  Analytics
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Alert -->
        <div v-if="error" class="p-6">
          <Alert variant="destructive">
            <DuotoneIcon name="warning" class="h-4 w-4" />
            <AlertDescription>{{ error }}</AlertDescription>
          </Alert>
        </div>

        <!-- Loading State -->
        <div v-if="isLoading" class="flex-1 flex items-center justify-center pb-24">
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 rounded-full border-2 border-orange-200 border-t-orange-500 animate-spin"></div>
            <span class="text-lg text-muted-foreground font-medium">Loading restaurant data...</span>
          </div>
        </div>

        <!-- No Tables State -->
        <div v-if="!isLoading && tables.length === 0" class="flex-1 flex items-center justify-center pb-24">
          <div class="text-center">
            <div class="w-16 h-16 rounded-lg bg-muted flex items-center justify-center mx-auto mb-4">
              <DuotoneIcon name="utensils" class="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 class="text-xl font-semibold mb-2">No Tables Found</h3>
            <p class="text-muted-foreground mb-6">Create your first table to get started</p>

            <!-- Create Table Button -->
            <div class="flex items-center justify-center gap-3">
              <Button @click="showCreateTableDialog = true" class="bg-primary hover:bg-primary/90">
                <Plus class="w-4 h-4 mr-2" />
                Create First Table
              </Button>

              <Button variant="outline" @click="refreshData" :disabled="isRefreshing">
                <RefreshCw :class="['w-4 h-4 mr-2', isRefreshing && 'animate-spin']" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div v-if="!isLoading && tables.length > 0" class="flex-1 overflow-hidden p-6 pb-24">
          <div class="h-full flex flex-col space-y-6">
            <!-- Navigation Tabs -->
            <div class="border-b">
              <nav class="flex space-x-8">
                <button
                  v-for="tab in tabs"
                  :key="tab.id"
                  @click="activeTab = tab.id"
                  :class="[
                    'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
                  ]"
                >
                  <div class="flex items-center gap-2">
                    <DuotoneIcon :name="tab.icon" class="h-4 w-4" />
                    {{ tab.label }}
                    <span v-if="tab.count !== undefined" class="bg-muted text-muted-foreground px-2 py-0.5 rounded-full text-xs">
                      {{ tab.count }}
                    </span>
                  </div>
                </button>
              </nav>
            </div>

            <!-- Tables Tab -->
            <div v-if="activeTab === 'tables'" class="flex-1 flex flex-col space-y-6">
              <!-- View Controls -->
              <div class="flex items-center justify-between">
                <h2 class="text-xl font-semibold">Restaurant Floor Plan</h2>
                <div class="flex items-center gap-4">
                  <!-- Status Legend -->
                  <div class="flex items-center gap-4 text-sm">
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 rounded-full bg-emerald-500"></div>
                      <span class="text-muted-foreground">Available</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 rounded-full bg-rose-500"></div>
                      <span class="text-muted-foreground">Occupied</span>
                    </div>
                    <div class="flex items-center gap-2">
                      <div class="w-3 h-3 rounded-full bg-amber-500"></div>
                      <span class="text-muted-foreground">Reserved</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Table Content Area -->
              <div class="xcard flex-1 overflow-hidden">
                <!-- Table Grid View -->
                <div v-if="viewPreference === 'grid'">
                  <Card class="h-full border-0 shadow-sm">
                    <CardContent class="p-0 h-full">
                      <!-- Restaurant Floor Plan Container with Zones -->
                      <div class="relative h-full min-h-[700px] bg-gradient-to-br from-slate-50 via-gray-50 to-slate-100">
                        <!-- Floor Plan Grid Background -->
                        <div class="absolute inset-0 opacity-20" style="
                          background-image:
                            linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
                            linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
                          background-size: 50px 50px;
                        "></div>

                        <!-- Restaurant Zones -->
                        <div class="absolute inset-0 p-6">
                          <!-- Entrance Zone -->
                          <div class="absolute top-4 left-6 right-6 h-54 bg-blue-50/40 rounded-lg border border-blue-200/60">
                            <div class="absolute top-2 left-3 text-xs font-medium text-blue-600/80">Entrance Area</div>
                          </div>

                          <!-- Main Dining Zone -->
                          <div class="absolute top-62 left-6 right-6 bottom-32 bg-green-50/30 rounded-lg border border-green-200/60">
                            <div class="absolute top-2 left-3 text-xs font-medium text-green-600/80">Main Dining Area</div>
                          </div>

                          <!-- Private Zone -->
                          <!-- <div class="absolute bottom-4 left-6 right-6 h-24 bg-purple-50/40 rounded-lg border border-purple-200/60">
                            <div class="absolute top-2 left-3 text-xs font-medium text-purple-600/80">Private Dining</div>
                          </div> -->
                        </div>

                        <!-- Tables Container with Zones -->
                        <div class="relative w-full h-full p-8">



                          <!-- Restaurant Table Grid/Absolute Container -->
                          <div
                            :class="[
                              'relative w-full h-full min-h-[600px]',
                              (isArrangeMode || hasCustomPositions) ? 'relative' : 'grid grid-cols-5 gap-20 auto-rows-max',
                              'z-10'
                            ]"
                          >
                            <div
                              v-for="(table, index) in tables"
                              :key="table._id"
                              v-draggable="{
                                disabled: !isArrangeMode,
                                bounds: 'parent',
                                position: {
                                  x: tablePositions[table._id]?.x || 0,
                                  y: tablePositions[table._id]?.y || 0
                                }
                              }"
                              :class="[
                                'cursor-pointer transition-all duration-300 group',
                                isArrangeMode ? 'hover:scale-105 hover:z-30 cursor-move' : 'hover:scale-110 hover:z-30',
                                (isArrangeMode || hasCustomPositions) ? 'absolute' : 'relative'
                              ]"
                              @click="!isArrangeMode && (selectedTable = table)"
                              @neodrag:end="(event: any) => handleDragEnd(table._id, event)"
                            >
                              <!-- Drag Handle (only visible in arrange mode) -->
                              <div v-if="isArrangeMode" class="drag-handle absolute -top-2 -right-2 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs z-40 cursor-move shadow-lg">
                                <Move class="w-3 h-3" />
                              </div>

                              <!-- Table Shadow -->
                              <div class="absolute inset-0 bg-black/10 rounded-lg blur-sm transform translate-y-1 scale-95"></div>

                              <!-- Chair Indicators -->
                              <div
                                v-for="(chair, chairIndex) in getChairPositions(table.capacity)"
                                :key="`chair-${chairIndex}`"
                                :class="[
                                  'absolute rounded-sm shadow-sm border transition-all duration-200',
                                  table.status === 'available' ? 'bg-gray-100 border-gray-300' :
                                  table.status === 'occupied' ? 'bg-orange-200 border-orange-400' :
                                  table.status === 'reserved' ? 'bg-yellow-200 border-yellow-400' :
                                  'bg-gray-200 border-gray-400'
                                ]"
                                :style="chair.style"
                              ></div>

                              <!-- Table Container with Enhanced Design -->
                              <div
                                :class="[
                                  'relative shadow-xl border-2 transition-all duration-300 flex flex-col items-center justify-center backdrop-blur-sm mx-auto',
                                  getTableSize(table).shape,
                                  getTableSize(table).width,
                                  getTableSize(table).height,
                                  table.status === 'available' ? 'bg-white/95 border-gray-300 hover:border-gray-400' :
                                  table.status === 'occupied' ? 'bg-gradient-to-br from-orange-50 to-orange-100 border-orange-400 hover:border-orange-500' :
                                  table.status === 'reserved' ? 'bg-gradient-to-br from-yellow-50 to-yellow-100 border-yellow-400 hover:border-yellow-500' :
                                  'bg-gray-100 border-gray-400',
                                  isArrangeMode && 'border-dashed border-blue-300 bg-white/90'
                                ]"
                              >
                                <!-- Status Indicator with Pulse Animation -->
                                <div
                                  :class="[
                                    'absolute -top-2 -right-2 w-5 h-5 rounded-full border-3 border-white shadow-lg',
                                    table.status === 'available' ? 'bg-green-500' :
                                    table.status === 'occupied' ? 'bg-orange-500 animate-pulse' :
                                    table.status === 'reserved' ? 'bg-yellow-500' :
                                    'bg-gray-500'
                                  ]"
                                ></div>

                                <!-- VIP Crown Badge -->
                                <div v-if="table.isVipTable" class="absolute -top-3 -left-3 bg-gradient-to-r from-yellow-400 to-orange-400 text-white px-2 py-1 rounded-full text-xs font-bold shadow-lg">
                                  ⭐ VIP
                                </div>

                                <!-- Table Content with Better Typography -->
                                <div class="text-center px-3 py-2 w-full">
                                  <!-- Table Number with Enhanced Style -->
                                  <div class="font-bold text-xl text-gray-800 mb-2 tracking-wide">
                                    {{ table.tableNumber }}
                                  </div>

                                  <!-- Revenue and Time Info for Occupied Tables -->
                                  <div v-if="getTableInfo(table).hasInfo && !isArrangeMode" class="space-y-2">
                                    <div v-if="getTableInfo(table).revenue" class="flex items-center justify-center text-sm font-bold text-orange-800 bg-orange-100/70 rounded-full px-2 py-1">
                                      <DollarSign class="w-3 h-3 mr-1" />
                                      {{ getTableInfo(table).revenue }}
                                    </div>
                                    <div v-if="getTableInfo(table).time" class="flex items-center justify-center text-xs text-orange-700 bg-white/70 rounded-full px-2 py-1">
                                      <Clock class="w-3 h-3 mr-1" />
                                      {{ getTableInfo(table).time }}
                                    </div>
                                  </div>

                                  <!-- Seat Count for Available Tables -->
                                  <div v-else-if="table.status === 'available'" class="text-sm text-gray-700 bg-gray-100/70 rounded-full px-2 py-1">
                                    {{ table.capacity }} seats
                                  </div>

                                  <!-- Reserved Info -->
                                  <div v-else-if="table.status === 'reserved' && !isArrangeMode" class="text-xs text-yellow-800 font-semibold bg-yellow-100/70 rounded-full px-2 py-1">
                                    Reserved
                                  </div>

                                  <!-- Arrange Mode Indicator -->
                                  <div v-if="isArrangeMode" class="text-xs text-blue-600 bg-blue-100/70 rounded-full px-2 py-1">
                                    Drag to move
                                  </div>
                                </div>

                                <!-- Arrange Mode Actions (only visible in arrange mode) -->
                                <div v-if="isArrangeMode" class="absolute -top-12 left-1/2 transform -translate-x-1/2 flex gap-1 opacity-0 group-hover:opacity-100 transition-all duration-300 z-50">
                                  <!-- Duplicate Button -->
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    class="h-8 px-2 bg-white/95 hover:bg-blue-50 border-blue-200 text-blue-600 shadow-md"
                                    @click.stop="duplicateTable(table)"
                                    :disabled="isDuplicatingTable"
                                    :title="`Duplicate ${table.tableNumber}`"
                                  >
                                    <Copy class="w-3 h-3" />
                                  </Button>

                                  <!-- Edit Name Button -->
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    class="h-8 px-2 bg-white/95 hover:bg-green-50 border-green-200 text-green-600 shadow-md"
                                    @click.stop="startEditingTableName(table)"
                                    :title="`Rename ${table.tableNumber}`"
                                  >
                                    <Edit2 class="w-3 h-3" />
                                  </Button>

                                  <!-- Delete Button -->
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    class="h-8 px-2 bg-white/95 hover:bg-red-50 border-red-200 text-red-600 shadow-md"
                                    @click.stop="deleteTable(table)"
                                    :title="`Delete ${table.tableNumber}`"
                                  >
                                    <Trash2 class="w-3 h-3" />
                                  </Button>
                                </div>

                                <!-- Table Name Editing Input (only visible when editing) -->
                                <div v-if="isArrangeMode && editingTableId === table._id" class="absolute -bottom-12 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-white/95 rounded-lg shadow-lg border p-2 z-50">
                                  <Input
                                    v-model="editingTableName"
                                    class="w-24 h-7 text-xs"
                                    placeholder="Table name"
                                    @keyup.enter="saveTableName(table._id)"
                                    @keyup.escape="cancelEditingTableName"
                                    @blur="saveTableName(table._id)"
                                    ref="editingInput"
                                  />
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    class="h-7 px-2"
                                    @click.stop="saveTableName(table._id)"
                                  >
                                    <Check class="w-3 h-3 text-green-600" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    class="h-7 px-2"
                                    @click.stop="cancelEditingTableName"
                                  >
                                    <X class="w-3 h-3 text-red-600" />
                                  </Button>
                                </div>

                                <!-- Enhanced Quick Actions (hidden in arrange mode) -->
                                <div v-if="!isArrangeMode" class="absolute -bottom-10 left-1/2 transform -translate-x-1/2 flex gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300 scale-90 group-hover:scale-100">
                                  <Button
                                    v-if="table.status === 'available'"
                                    size="sm"
                                    class="text-xs px-3 py-2 bg-green-500 hover:bg-green-600 text-white shadow-lg border-0"
                                    @click.stop="updateTableStatus(table, 'occupied')"
                                  >
                                    <Users class="h-3 w-3 mr-1" />
                                    Seat
                                  </Button>

                                  <Button
                                    v-if="table.status === 'occupied'"
                                    size="sm"
                                    class="text-xs px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white shadow-lg border-0"
                                    @click.stop="updateTableStatus(table, 'available')"
                                  >
                                    <CheckCircle class="h-3 w-3 mr-1" />
                                    Clear
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>

                        <!-- Enhanced Floor Plan Legend -->
                        <div class="absolute bottom-4 right-4 bg-white/95 backdrop-blur-sm rounded-lg shadow-lg border p-4 z-20">
                          <div class="text-xs font-semibold text-gray-700 mb-3">Restaurant Layout</div>
                          <div class="space-y-2">
                            <!-- Status Legend -->
                            <div class="space-y-1 text-xs mb-3">
                              <div class="flex items-center gap-2">
                                <div class="w-3 h-3 rounded-full bg-green-500"></div>
                                <span class="text-gray-600">Available</span>
                              </div>
                              <div class="flex items-center gap-2">
                                <div class="w-3 h-3 rounded-full bg-orange-500"></div>
                                <span class="text-gray-600">Occupied</span>
                              </div>
                              <div class="flex items-center gap-2">
                                <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                                <span class="text-gray-600">Reserved</span>
                              </div>
                            </div>

                            <!-- Zone Legend -->
                            <div class="border-t pt-2 space-y-1 text-xs">
                              <div class="flex items-center gap-2">
                                <div class="w-3 h-2 rounded bg-blue-200"></div>
                                <span class="text-gray-600">Entrance</span>
                              </div>
                              <div class="flex items-center gap-2">
                                <div class="w-3 h-2 rounded bg-green-200"></div>
                                <span class="text-gray-600">Main Dining</span>
                              </div>
                              <!-- <div class="flex items-center gap-2">
                                <div class="w-3 h-2 rounded bg-purple-200"></div>
                                <span class="text-gray-600">Private Dining</span>
                              </div> -->
                            </div>

                            <!-- Layout Info -->
                            <div class="border-t pt-2 text-xs text-gray-500">
                              {{ layoutInfo }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <!-- Table List View -->
                <div v-else class="space-y-4 h-full overflow-y-auto">
                  <Card
                    v-for="table in tables"
                    :key="table._id"
                    :class="[
                      'cursor-pointer transition-all duration-200 hover:shadow-md border-l-4',
                      tableStatusConfig[table.status].borderColor
                    ]"
                    @click="selectedTable = table"
                  >
                    <CardContent class="p-4">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                          <!-- Mini Table Visual -->
                          <div
                            :class="[
                              'w-12 h-12 rounded-lg flex items-center justify-center relative',
                              tableStatusConfig[table.status].bgColor
                            ]"
                          >
                            <DuotoneIcon
                              :name="table.shape === 'round' ? 'circle' : 'square'"
                              :class="`h-6 w-6 ${tableStatusConfig[table.status].textColor}`"
                            />
                          </div>

                          <div>
                            <h3 class="font-semibold text-lg">Table {{ table.tableNumber }}</h3>
                            <p class="text-sm text-muted-foreground">{{ table.capacity }} seats • {{ table.shape }} • {{ table.size }}</p>

                            <!-- Time info -->
                            <div v-if="table.status === 'reserved' && table.reservationInfo" class="text-xs text-amber-700 font-semibold mt-1">
                              Reserved {{ new Date(table.reservationInfo.reservationTime).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) }}
                            </div>
                            <div v-else-if="table.status === 'occupied'" class="text-xs text-rose-700 font-semibold mt-1">
                              Checked-in {{ new Date().toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) }}
                            </div>
                          </div>
                        </div>

                        <div class="flex items-center gap-3">
                          <Badge :class="[tableStatusConfig[table.status].color, 'text-white']">
                            <DuotoneIcon :name="tableStatusConfig[table.status].icon" class="h-3 w-3 mr-1" />
                            {{ tableStatusConfig[table.status].label }}
                          </Badge>

                          <Badge v-if="table.isVipTable" variant="outline" class="border-amber-300 text-amber-700">
                            <DuotoneIcon name="star" class="h-3 w-3 mr-1" />
                            VIP
                          </Badge>

                          <!-- Actions -->
                          <div class="flex gap-2">
                            <Button
                              v-if="table.status === 'available'"
                              size="sm"
                              variant="outline"
                              @click.stop="updateTableStatus(table, 'occupied')"
                            >
                              <Users class="h-3 w-3 mr-1" />
                              Seat
                            </Button>

                            <Button
                              v-if="table.status === 'occupied'"
                              size="sm"
                              variant="outline"
                              @click.stop="updateTableStatus(table, 'available')"
                            >
                              <CheckCircle class="h-3 w-3 mr-1" />
                              Clear
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Floor Management Dialog -->
    <FloorManagementDialog
      v-model:open="showFloorManagementDialog"
      @floors-updated="onFloorsUpdated"
    />

    <!-- Floating Floor Tabs -->
    <FloatingFloorTabs
      :floors="floors"
      :selected-floor-id="selectedFloorId"
      :loading="floorsLoading"
      @floor-changed="onFloorChanged"
      @manage-floors="showFloorManagementDialog = true"
      @refresh-floors="refreshFloors"
      @create-floor="showFloorManagementDialog = true"
    />
  </div>
</template>

<style scoped>
/* Custom scrollbar for sidebar */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* View container transitions */
.view-container {
  animation: fadeIn 0.3s ease-in-out;
}

.view-container > div {
  animation: slideIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced view mode toggle buttons */
.flex.items-center.border.rounded-lg.p-1.space-x-1 button {
  transition: all 0.2s ease-in-out;
}

.flex.items-center.border.rounded-lg.p-1.space-x-1 button:hover {
  transform: translateY(-1px);
}

/* Table hover effects */
.group:hover {
  transform: scale(1.02);
}

/* Stats card hover effects */
.hover\:shadow-md:hover {
  transform: translateY(-2px);
}
</style>
