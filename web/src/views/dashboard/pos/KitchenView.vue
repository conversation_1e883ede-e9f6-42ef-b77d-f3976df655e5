<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../../stores/auth'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Badge } from '../../../components/ui/badge'
import { Alert, AlertDescription } from '../../../components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import DuotoneIcon from '../../../components/DuotoneIcon.vue'
import { ArrowLeft, Clock, CheckCircle, AlertTriangle, ChefHat, Users, Timer, Bell } from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const router = useRouter()
const authStore = useAuthStore()

// Data
const isLoading = ref(true)
const error = ref('')
const orders = ref<any[]>([])
const currentTime = ref(new Date())
const soundEnabled = ref(true)
const stationFilter = ref('all')

let timeInterval: NodeJS.Timeout | null = null

// Mock data for demonstration
const mockOrders = [
  {
    _id: '1',
    orderNumber: 'ORD-001',
    tableNumber: 'Table 5',
    customerName: 'John Doe',
    status: 'pending',
    priority: 'normal',
    items: [
      {
        name: 'Pad Thai',
        quantity: 2,
        notes: 'Extra spicy, no peanuts',
        station: 'hot',
        cookTime: 15,
        status: 'pending'
      },
      {
        name: 'Tom Yum Soup',
        quantity: 1,
        notes: 'Medium spice',
        station: 'hot',
        cookTime: 10,
        status: 'pending'
      }
    ],
    createdAt: '2025-01-10T14:30:00Z',
    estimatedTime: 18,
    actualStartTime: null,
    completedAt: null
  },
  {
    _id: '2',
    orderNumber: 'ORD-002',
    tableNumber: 'Table 12',
    customerName: 'Jane Smith',
    status: 'preparing',
    priority: 'high',
    items: [
      {
        name: 'BBQ Ribs',
        quantity: 1,
        notes: 'Well done, extra sauce',
        station: 'grill',
        cookTime: 45,
        status: 'preparing'
      },
      {
        name: 'Caesar Salad',
        quantity: 2,
        notes: 'Dressing on the side',
        station: 'cold',
        cookTime: 5,
        status: 'ready'
      }
    ],
    createdAt: '2025-01-10T13:15:00Z',
    estimatedTime: 45,
    actualStartTime: '2025-01-10T13:18:00Z',
    completedAt: null
  }
]

// Computed
const activeOrders = computed(() => {
  let filtered = orders.value.filter(order => order.status !== 'served')

  if (stationFilter.value !== 'all') {
    filtered = filtered.filter(order =>
      order.items.some((item: any) => item.station === stationFilter.value)
    )
  }

  return filtered.sort((a, b) => {
    // Priority first
    if (a.priority === 'high' && b.priority !== 'high') return -1
    if (b.priority === 'high' && a.priority !== 'high') return 1

    // Then by order time
    return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  })
})

const kitchenStats = computed(() => {
  const pending = orders.value.filter(o => o.status === 'pending').length
  const preparing = orders.value.filter(o => o.status === 'preparing').length
  const ready = orders.value.filter(o => o.status === 'ready').length

  return { pending, preparing, ready }
})

// Methods
const goBack = () => {
  router.push('/dashboard/modules')
}

const loadOrders = async () => {
  try {
    isLoading.value = true
    error.value = ''

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    orders.value = mockOrders

  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load kitchen orders'
    console.error('Failed to load orders:', err)
  } finally {
    isLoading.value = false
  }
}

const startOrder = async (order: any) => {
  try {
    order.status = 'preparing'
    order.actualStartTime = new Date().toISOString()

    toast.success('Order started!', {
      description: `${order.orderNumber} is now being prepared`
    })
  } catch (err) {
    toast.error('Failed to start order')
  }
}

const completeOrder = async (order: any) => {
  try {
    order.status = 'ready'
    order.completedAt = new Date().toISOString()

    toast.success('Order completed!', {
      description: `${order.orderNumber} is ready for pickup`
    })
  } catch (err) {
    toast.error('Failed to complete order')
  }
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getElapsedTime = (startTime: string | null) => {
  if (!startTime) return 0

  const now = currentTime.value
  const start = new Date(startTime)
  return Math.floor((now.getTime() - start.getTime()) / 1000 / 60) // minutes
}

// Lifecycle
onMounted(() => {
  loadOrders()

  // Update current time every minute
  timeInterval = setInterval(() => {
    currentTime.value = new Date()
  }, 60000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<template>
  <div class="min-h-[calc(100vh-100px)] bg-background">
    <div class="h-[calc(100vh-100px)] flex flex-col">
      <!-- Header -->
      <div class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <Button variant="ghost" size="sm" @click="goBack">
                <ArrowLeft class="w-4 h-4 mr-2" />
                Back
              </Button>
              <div class="h-4 w-px bg-border"></div>
              <nav class="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Dashboard</span>
                <span>/</span>
                <span>POS</span>
                <span>/</span>
                <span class="text-foreground font-medium">Kitchen Display</span>
              </nav>
            </div>

            <div class="flex items-center gap-3">
              <!-- Station Filter -->
              <Select v-model="stationFilter">
                <SelectTrigger class="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Stations</SelectItem>
                  <SelectItem value="hot">Hot Kitchen</SelectItem>
                  <SelectItem value="cold">Cold Station</SelectItem>
                  <SelectItem value="grill">Grill</SelectItem>
                  <SelectItem value="fryer">Fryer</SelectItem>
                </SelectContent>
              </Select>

              <!-- Sound Toggle -->
              <Button
                variant="outline"
                size="sm"
                @click="soundEnabled = !soundEnabled"
                :class="soundEnabled ? 'bg-green-50 text-green-700' : 'bg-gray-50'"
              >
                <Bell class="w-4 h-4 mr-2" />
                {{ soundEnabled ? 'Sound On' : 'Sound Off' }}
              </Button>

              <!-- Current Time -->
              <div class="text-sm font-mono bg-muted px-3 py-2 rounded-md">
                {{ currentTime.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Alert -->
      <div v-if="error" class="p-6">
        <Alert variant="destructive">
          <DuotoneIcon name="warning" class="h-4 w-4" />
          <AlertDescription>{{ error }}</AlertDescription>
        </Alert>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex-1 flex items-center justify-center">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 rounded-full border-2 border-amber-200 border-t-amber-500 animate-spin"></div>
          <span class="text-lg text-muted-foreground font-medium">Loading kitchen display...</span>
        </div>
      </div>

      <!-- Main Content -->
      <div v-else class="flex-1 overflow-hidden p-6">
        <div class="h-full flex flex-col space-y-6">
          <!-- Kitchen Stats -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Pending Orders</p>
                    <p class="text-2xl font-bold">{{ kitchenStats.pending }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-yellow-100 flex items-center justify-center">
                    <Clock class="w-6 h-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Preparing</p>
                    <p class="text-2xl font-bold">{{ kitchenStats.preparing }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                    <ChefHat class="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Ready</p>
                    <p class="text-2xl font-bold">{{ kitchenStats.ready }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                    <CheckCircle class="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Orders Grid -->
          <div v-if="activeOrders.length === 0" class="flex-1 flex items-center justify-center">
            <div class="text-center">
              <div class="w-16 h-16 rounded-lg bg-muted flex items-center justify-center mx-auto mb-4">
                <ChefHat class="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 class="text-xl font-semibold mb-2">No Active Orders</h3>
              <p class="text-muted-foreground">
                All orders have been completed! Great job! 👨‍🍳
              </p>
            </div>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card v-for="order in activeOrders" :key="order._id"
                  :class="['group transition-all hover:shadow-lg',
                          order.priority === 'high' ? 'border-l-4 border-l-red-500' : 'border-l-4 border-l-green-500']">
              <CardContent class="p-6">
                <div class="space-y-4">
                  <!-- Order Header -->
                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="font-semibold text-lg">{{ order.orderNumber }}</h4>
                      <p class="text-muted-foreground">{{ order.tableNumber }} • {{ order.customerName }}</p>
                    </div>
                    <div class="text-right">
                      <Badge :variant="order.priority === 'high' ? 'destructive' : 'secondary'">
                        {{ order.priority }}
                      </Badge>
                      <p class="text-xs text-muted-foreground mt-1">
                        {{ formatTime(order.createdAt) }}
                      </p>
                    </div>
                  </div>

                  <!-- Timer -->
                  <div class="flex items-center justify-between bg-muted rounded-lg p-4">
                    <div class="flex items-center gap-2">
                      <Timer class="w-5 h-5" />
                      <span class="font-medium">
                        {{ order.status === 'preparing' ? 'Cooking Time' : 'Wait Time' }}:
                      </span>
                    </div>
                    <span class="text-lg font-mono font-bold text-blue-600">
                      {{ order.status === 'preparing'
                         ? getElapsedTime(order.actualStartTime)
                         : Math.floor((currentTime.getTime() - new Date(order.createdAt).getTime()) / 1000 / 60) }}m
                    </span>
                  </div>

                  <!-- Items -->
                  <div class="space-y-2">
                    <div v-for="(item, index) in order.items" :key="index"
                         class="flex items-center justify-between p-3 bg-background rounded-lg border">
                      <div class="flex-1">
                        <p class="font-semibold">{{ item.quantity }}x {{ item.name }}</p>
                        <p v-if="item.notes" class="text-sm text-muted-foreground italic">{{ item.notes }}</p>
                        <Badge :class="item.station === 'hot' ? 'bg-red-100 text-red-700' :
                                      item.station === 'cold' ? 'bg-blue-100 text-blue-700' :
                                      item.station === 'grill' ? 'bg-orange-100 text-orange-700' :
                                      'bg-yellow-100 text-yellow-700'" class="text-xs mt-1">
                          {{ item.station }}
                        </Badge>
                      </div>
                      <Badge :variant="item.status === 'ready' ? 'default' : 'secondary'">
                        {{ item.status }}
                      </Badge>
                    </div>
                  </div>

                  <!-- Actions -->
                  <div class="flex gap-3">
                    <Button
                      v-if="order.status === 'pending'"
                      @click="startOrder(order)"
                      class="flex-1"
                    >
                      <ChefHat class="w-4 h-4 mr-2" />
                      Start Cooking
                    </Button>

                    <Button
                      v-if="order.status === 'preparing'"
                      @click="completeOrder(order)"
                      class="flex-1"
                    >
                      <CheckCircle class="w-4 h-4 mr-2" />
                      Mark Ready
                    </Button>

                    <Button
                      v-if="order.status === 'ready'"
                      variant="outline"
                      @click="() => { order.status = 'served'; toast.success('Order served!') }"
                      class="flex-1"
                    >
                      <Users class="w-4 h-4 mr-2" />
                      Mark Served
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
