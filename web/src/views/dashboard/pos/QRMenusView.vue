<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../../stores/auth'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Badge } from '../../../components/ui/badge'
import { Alert, AlertDescription } from '../../../components/ui/alert'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../../../components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import DuotoneIcon from '../../../components/DuotoneIcon.vue'
import { ArrowLeft, Plus, QrCode, Download, Eye, Settings, Edit, Trash2 } from 'lucide-vue-next'
import { toast } from 'vue-sonner'

const router = useRouter()
const authStore = useAuthStore()

// Data
const isLoading = ref(true)
const error = ref('')
const qrMenus = ref<any[]>([])
const tables = ref<any[]>([])
const showCreateDialog = ref(false)
const searchQuery = ref('')

// Create QR Menu form
const newQRMenu = ref({
  tableId: '',
  menuType: 'standard',
  isActive: true,
  customMessage: ''
})

const isCreating = ref(false)

// Mock data for demonstration
const mockQRMenus = [
  {
    _id: '1',
    tableNumber: 'Table 1',
    tableId: 'table1',
    qrCode: 'QR001',
    menuType: 'standard',
    isActive: true,
    scans: 24,
    lastScanned: '2025-01-10T10:30:00Z',
    createdAt: '2025-01-01T00:00:00Z'
  },
  {
    _id: '2',
    tableNumber: 'Table 5',
    tableId: 'table5',
    qrCode: 'QR005',
    menuType: 'bbq',
    isActive: true,
    scans: 18,
    lastScanned: '2025-01-10T15:45:00Z',
    createdAt: '2025-01-02T00:00:00Z'
  },
  {
    _id: '3',
    tableNumber: 'Table 10',
    tableId: 'table10',
    qrCode: 'QR010',
    menuType: 'standard',
    isActive: false,
    scans: 5,
    lastScanned: '2025-01-08T12:15:00Z',
    createdAt: '2025-01-03T00:00:00Z'
  }
]

const mockTables = [
  { _id: 'table1', tableNumber: 'Table 1', status: 'available' },
  { _id: 'table2', tableNumber: 'Table 2', status: 'occupied' },
  { _id: 'table3', tableNumber: 'Table 3', status: 'available' },
  { _id: 'table5', tableNumber: 'Table 5', status: 'available' },
  { _id: 'table10', tableNumber: 'Table 10', status: 'reserved' }
]

// Computed
const filteredQRMenus = computed(() => {
  if (!searchQuery.value.trim()) {
    return qrMenus.value
  }

  const query = searchQuery.value.toLowerCase()
  return qrMenus.value.filter(menu =>
    menu.tableNumber.toLowerCase().includes(query) ||
    menu.qrCode.toLowerCase().includes(query) ||
    menu.menuType.toLowerCase().includes(query)
  )
})

const availableTables = computed(() => {
  const assignedTableIds = qrMenus.value.map(menu => menu.tableId)
  return tables.value.filter(table => !assignedTableIds.includes(table._id))
})

const totalScans = computed(() => {
  return qrMenus.value.reduce((total, menu) => total + menu.scans, 0)
})

const activeMenus = computed(() => {
  return qrMenus.value.filter(menu => menu.isActive).length
})

// Methods
const goBack = () => {
  router.push('/dashboard/modules')
}

const loadData = async () => {
  try {
    isLoading.value = true
    error.value = ''

    // Simulate API calls
    await new Promise(resolve => setTimeout(resolve, 1000))

    qrMenus.value = mockQRMenus
    tables.value = mockTables

  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load QR menus'
    console.error('Failed to load QR menus:', err)
  } finally {
    isLoading.value = false
  }
}

const createQRMenu = async () => {
  if (!newQRMenu.value.tableId) {
    toast.error('Please select a table')
    return
  }

  try {
    isCreating.value = true

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))

    const selectedTable = tables.value.find(t => t._id === newQRMenu.value.tableId)
    if (!selectedTable) return

    const newMenu = {
      _id: `qr${Date.now()}`,
      tableNumber: selectedTable.tableNumber,
      tableId: newQRMenu.value.tableId,
      qrCode: `QR${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`,
      menuType: newQRMenu.value.menuType,
      isActive: newQRMenu.value.isActive,
      scans: 0,
      lastScanned: null,
      createdAt: new Date().toISOString()
    }

    qrMenus.value.unshift(newMenu)

    toast.success('QR Menu created successfully! 🎉', {
      description: `QR code ${newMenu.qrCode} generated for ${selectedTable.tableNumber}`
    })

    // Reset form
    newQRMenu.value = {
      tableId: '',
      menuType: 'standard',
      isActive: true,
      customMessage: ''
    }
    showCreateDialog.value = false

  } catch (err) {
    toast.error('Failed to create QR menu', {
      description: err instanceof Error ? err.message : 'Please try again'
    })
  } finally {
    isCreating.value = false
  }
}

const downloadQR = (menu: any) => {
  toast.success(`QR Code downloaded!`, {
    description: `${menu.qrCode} for ${menu.tableNumber}`
  })
}

const previewMenu = (menu: any) => {
  toast.info(`Opening menu preview`, {
    description: `${menu.menuType} menu for ${menu.tableNumber}`
  })
}

const toggleMenuStatus = async (menu: any) => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))

    menu.isActive = !menu.isActive

    toast.success(`QR Menu ${menu.isActive ? 'activated' : 'deactivated'}`, {
      description: `${menu.tableNumber} menu is now ${menu.isActive ? 'active' : 'inactive'}`
    })
  } catch (err) {
    toast.error('Failed to update menu status')
  }
}

const deleteQRMenu = async (menu: any) => {
  if (!confirm(`Are you sure you want to delete the QR menu for ${menu.tableNumber}?`)) {
    return
  }

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))

    const index = qrMenus.value.findIndex(m => m._id === menu._id)
    if (index !== -1) {
      qrMenus.value.splice(index, 1)
    }

    toast.success('QR Menu deleted successfully', {
      description: `${menu.tableNumber} QR menu removed`
    })
  } catch (err) {
    toast.error('Failed to delete QR menu')
  }
}

const getMenuTypeColor = (type: string) => {
  switch (type) {
    case 'bbq': return 'bg-red-100 text-red-700'
    case 'drinks': return 'bg-blue-100 text-blue-700'
    case 'desserts': return 'bg-pink-100 text-pink-700'
    default: return 'bg-gray-100 text-gray-700'
  }
}

const formatDate = (dateString: string | null) => {
  if (!dateString) return 'Never'
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="min-h-[calc(100vh-100px)] bg-background">
    <div class="h-[calc(100vh-100px)] flex flex-col">
      <!-- Header -->
      <div class="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-4">
              <Button variant="ghost" size="sm" @click="goBack">
                <ArrowLeft class="w-4 h-4 mr-2" />
                Back
              </Button>
              <div class="h-4 w-px bg-border"></div>
              <nav class="flex items-center gap-2 text-sm text-muted-foreground">
                <span>Dashboard</span>
                <span>/</span>
                <span>POS</span>
                <span>/</span>
                <span class="text-foreground font-medium">QR Menus</span>
              </nav>
            </div>

            <div class="flex items-center gap-3">
              <!-- Create QR Menu Button -->
              <Dialog v-model:open="showCreateDialog">
                <DialogTrigger as-child>
                  <Button>
                    <Plus class="w-4 h-4 mr-2" />
                    Create QR Menu
                  </Button>
                </DialogTrigger>
                <DialogContent class="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>Create QR Menu</DialogTitle>
                  </DialogHeader>

                  <div class="space-y-4 py-4">
                    <!-- Table Selection -->
                    <div class="space-y-2">
                      <Label for="table">Select Table</Label>
                      <Select v-model="newQRMenu.tableId">
                        <SelectTrigger>
                          <SelectValue placeholder="Choose a table" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem
                            v-for="table in availableTables"
                            :key="table._id"
                            :value="table._id"
                          >
                            {{ table.tableNumber }} ({{ table.status }})
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <p v-if="availableTables.length === 0" class="text-sm text-muted-foreground">
                        All tables have QR menus assigned
                      </p>
                    </div>

                    <!-- Menu Type -->
                    <div class="space-y-2">
                      <Label for="menuType">Menu Type</Label>
                      <Select v-model="newQRMenu.menuType">
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="standard">Standard Menu</SelectItem>
                          <SelectItem value="bbq">BBQ Menu</SelectItem>
                          <SelectItem value="drinks">Drinks Only</SelectItem>
                          <SelectItem value="desserts">Desserts</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <!-- Custom Message -->
                    <div class="space-y-2">
                      <Label for="message">Custom Message (Optional)</Label>
                      <Input
                        id="message"
                        v-model="newQRMenu.customMessage"
                        placeholder="e.g., Welcome to our restaurant!"
                      />
                    </div>
                  </div>

                  <div class="flex justify-end gap-3">
                    <Button variant="outline" @click="showCreateDialog = false" :disabled="isCreating">
                      Cancel
                    </Button>
                    <Button @click="createQRMenu" :disabled="isCreating">
                      <span v-if="isCreating" class="flex items-center">
                        <div class="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Creating...
                      </span>
                      <span v-else class="flex items-center">
                        <QrCode class="w-4 h-4 mr-2" />
                        Create QR Menu
                      </span>
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>
      </div>

      <!-- Error Alert -->
      <div v-if="error" class="p-6">
        <Alert variant="destructive">
          <DuotoneIcon name="warning" class="h-4 w-4" />
          <AlertDescription>{{ error }}</AlertDescription>
        </Alert>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex-1 flex items-center justify-center">
        <div class="flex items-center gap-3">
          <div class="w-8 h-8 rounded-full border-2 border-purple-200 border-t-purple-500 animate-spin"></div>
          <span class="text-lg text-muted-foreground font-medium">Loading QR menus...</span>
        </div>
      </div>

      <!-- Main Content -->
      <div v-else class="flex-1 overflow-hidden p-6">
        <div class="h-full flex flex-col space-y-6">
          <!-- Stats Cards -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Total QR Menus</p>
                    <p class="text-2xl font-bold">{{ qrMenus.length }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                    <QrCode class="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Active Menus</p>
                    <p class="text-2xl font-bold">{{ activeMenus }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center">
                    <Eye class="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent class="p-6">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-muted-foreground">Total Scans</p>
                    <p class="text-2xl font-bold">{{ totalScans }}</p>
                  </div>
                  <div class="w-12 h-12 rounded-lg bg-orange-100 flex items-center justify-center">
                    <DuotoneIcon name="chart-line" class="w-6 h-6 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Search -->
          <div class="flex items-center gap-4">
            <div class="flex-1 relative">
              <DuotoneIcon name="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                v-model="searchQuery"
                placeholder="Search QR menus..."
                class="pl-10"
              />
            </div>
          </div>

          <!-- QR Menus Grid -->
          <div v-if="filteredQRMenus.length === 0" class="flex-1 flex items-center justify-center">
            <div class="text-center">
              <div class="w-16 h-16 rounded-lg bg-muted flex items-center justify-center mx-auto mb-4">
                <QrCode class="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 class="text-xl font-semibold mb-2">No QR Menus Found</h3>
              <p class="text-muted-foreground mb-4">
                {{ searchQuery ? 'No menus match your search' : 'Create your first QR menu to get started' }}
              </p>
              <Button v-if="!searchQuery" @click="showCreateDialog = true">
                <Plus class="w-4 h-4 mr-2" />
                Create QR Menu
              </Button>
            </div>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card v-for="menu in filteredQRMenus" :key="menu._id" class="group">
              <CardHeader class="pb-3">
                <div class="flex items-center justify-between">
                  <div>
                    <CardTitle class="text-lg">{{ menu.tableNumber }}</CardTitle>
                    <p class="text-sm text-muted-foreground">{{ menu.qrCode }}</p>
                  </div>
                  <div class="flex items-center gap-2">
                    <Badge :class="getMenuTypeColor(menu.menuType)">
                      {{ menu.menuType }}
                    </Badge>
                    <Badge :variant="menu.isActive ? 'default' : 'secondary'">
                      {{ menu.isActive ? 'Active' : 'Inactive' }}
                    </Badge>
                  </div>
                </div>
              </CardHeader>

              <CardContent class="space-y-4">
                <!-- QR Code Placeholder -->
                <div class="bg-muted rounded-lg p-4 flex items-center justify-center h-32">
                  <QrCode class="w-16 h-16 text-muted-foreground" />
                </div>

                <!-- Stats -->
                <div class="flex justify-between text-sm">
                  <span class="text-muted-foreground">Scans: <strong>{{ menu.scans }}</strong></span>
                  <span class="text-muted-foreground">Last: {{ formatDate(menu.lastScanned) }}</span>
                </div>

                <!-- Actions -->
                <div class="flex gap-2">
                  <Button size="sm" variant="outline" @click="downloadQR(menu)" class="flex-1">
                    <Download class="w-4 h-4 mr-2" />
                    Download
                  </Button>
                  <Button size="sm" variant="outline" @click="previewMenu(menu)" class="flex-1">
                    <Eye class="w-4 h-4 mr-2" />
                    Preview
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    @click="toggleMenuStatus(menu)"
                    :class="menu.isActive ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'"
                  >
                    <Settings class="w-4 h-4" />
                  </Button>
                  <Button size="sm" variant="outline" @click="deleteQRMenu(menu)" class="text-red-600 hover:text-red-700">
                    <Trash2 class="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
