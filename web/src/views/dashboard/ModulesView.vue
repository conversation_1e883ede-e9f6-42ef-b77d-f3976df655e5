<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { companyService } from '../../services/company.service'
import { Card, CardContent } from '../../components/ui/card'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import DuotoneIcon from '../../components/DuotoneIcon.vue'
import type { CompanySubscription } from '../../types/company'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Data
const subscriptions = ref<CompanySubscription[]>([])
const isLoading = ref(true)
const error = ref('')
const searchQuery = ref('')

// Module configuration with enhanced icon and app-like styling
const moduleConfig = {
  ERP: {
    name: 'ERP',
    fullName: 'Enterprise Resource Planning',
    description: 'Complete business management solution',
    icon: 'chart-line',
    color: 'bg-blue-500',
    textColor: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    category: 'Business'
  },
  ACCOUNTING: {
    name: 'Accounting',
    fullName: 'Financial Management',
    description: 'Complete accounting and finance tools',
    icon: 'calculator',
    color: 'bg-green-500',
    textColor: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    category: 'Business'
  },
  LOAN: {
    name: 'Loans',
    fullName: 'Loan Management',
    description: 'Comprehensive loan and credit system',
    icon: 'bank',
    color: 'bg-purple-500',
    textColor: 'text-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    category: 'Finance'
  },
  POS: {
    name: 'Point of Sale',
    fullName: 'POS System',
    description: 'Advanced retail and sales management',
    icon: 'cash-register',
    color: 'bg-emerald-500',
    textColor: 'text-emerald-600',
    bgColor: 'bg-emerald-50',
    borderColor: 'border-emerald-200',
    category: 'Sales'
  }
}

// POS business type configuration for specialized POS modules
const posBusinessTypeConfig = {
  RESTAURANT: {
    name: 'Restaurant',
    icon: 'scan-barcode',
    color: 'bg-orange-500',
    textColor: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200'
  },
  BAR: {
    name: 'Bar',
    icon: 'wine',
    color: 'bg-red-500',
    textColor: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200'
  },
  PHARMACY: {
    name: 'Pharmacy',
    icon: 'pills',
    color: 'bg-cyan-500',
    textColor: 'text-cyan-600',
    bgColor: 'bg-cyan-50',
    borderColor: 'border-cyan-200'
  },
  HOTEL: {
    name: 'Hotel',
    icon: 'bed',
    color: 'bg-indigo-500',
    textColor: 'text-indigo-600',
    bgColor: 'bg-indigo-50',
    borderColor: 'border-indigo-200'
  },
  RETAIL: {
    name: 'Retail',
    icon: 'store',
    color: 'bg-pink-500',
    textColor: 'text-pink-600',
    bgColor: 'bg-pink-50',
    borderColor: 'border-pink-200'
  },
  GROCERY: {
    name: 'Grocery',
    icon: 'shopping-cart',
    color: 'bg-lime-500',
    textColor: 'text-lime-600',
    bgColor: 'bg-lime-50',
    borderColor: 'border-lime-200'
  },
  FASHION: {
    name: 'Fashion',
    icon: 'shirt',
    color: 'bg-fuchsia-500',
    textColor: 'text-fuchsia-600',
    bgColor: 'bg-fuchsia-50',
    borderColor: 'border-fuchsia-200'
  },
  ELECTRONICS: {
    name: 'Electronics',
    icon: 'laptop',
    color: 'bg-slate-500',
    textColor: 'text-slate-600',
    bgColor: 'bg-slate-50',
    borderColor: 'border-slate-200'
  },
  AUTOMOTIVE: {
    name: 'Automotive',
    icon: 'car',
    color: 'bg-zinc-500',
    textColor: 'text-zinc-600',
    bgColor: 'bg-zinc-50',
    borderColor: 'border-zinc-200'
  },
  BEAUTY: {
    name: 'Beauty & Spa',
    icon: 'sparkles',
    color: 'bg-rose-500',
    textColor: 'text-rose-600',
    bgColor: 'bg-rose-50',
    borderColor: 'border-rose-200'
  },
  SPORTS: {
    name: 'Sports & Fitness',
    icon: 'dumbbell',
    color: 'bg-amber-500',
    textColor: 'text-amber-600',
    bgColor: 'bg-amber-50',
    borderColor: 'border-amber-200'
  },
  SERVICES: {
    name: 'Professional Services',
    icon: 'briefcase',
    color: 'bg-teal-500',
    textColor: 'text-teal-600',
    bgColor: 'bg-teal-50',
    borderColor: 'border-teal-200'
  },
  OTHER: {
    name: 'General Business',
    icon: 'building',
    color: 'bg-gray-500',
    textColor: 'text-gray-600',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200'
  }
}

const getDaysRemaining = (endDate: Date): number => {
  const now = new Date()
  const diffTime = endDate.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

const getModuleStatus = (subscription: CompanySubscription) => {
  const now = new Date()

  if (subscription.isTrialMode && subscription.trialEndDate) {
    const daysRemaining = getDaysRemaining(new Date(subscription.trialEndDate))
    return {
      type: 'trial',
      active: daysRemaining > 0,
      daysRemaining,
      label: daysRemaining > 0 ? `${daysRemaining}d trial` : 'Expired',
      badgeColor: daysRemaining > 0 ? 'bg-green-500' : 'bg-red-500'
    }
  }

  if (!subscription.isTrialMode && subscription.nextBillingDate) {
    const nextBilling = new Date(subscription.nextBillingDate)
    const isPastDue = nextBilling < now

    return {
      type: 'paid',
      active: !isPastDue,
      label: isPastDue ? 'Past Due' : 'Active',
      badgeColor: isPastDue ? 'bg-orange-500' : 'bg-blue-500'
    }
  }

  return {
    type: 'active',
    active: true,
    label: 'Active',
    badgeColor: 'bg-blue-500'
  }
}

// Transform subscriptions into app-like modules
const availableModules = computed(() => {
  const modules: Array<{
    id: string
    name: string
    fullName: string
    icon: string
    color: string
    textColor: string
    bgColor: string
    borderColor: string
    category: string
    status: any
    subscription: CompanySubscription
    route: string
  }> = []

  subscriptions.value.forEach(subscription => {
    const config = moduleConfig[subscription.module as keyof typeof moduleConfig]
    if (!config) return

    const status = getModuleStatus(subscription)

    // Handle POS modules with specific business types
    if (subscription.module === 'POS' && subscription.posType) {
      const posConfig = posBusinessTypeConfig[subscription.posType as keyof typeof posBusinessTypeConfig]
      if (posConfig) {
        modules.push({
          id: `${subscription.module}-${subscription.posType}`,
          name: posConfig.name,
          fullName: `${posConfig.name} POS`,
          icon: posConfig.icon,
          color: posConfig.color,
          textColor: posConfig.textColor,
          bgColor: posConfig.bgColor,
          borderColor: posConfig.borderColor,
          category: config.category,
          status,
          subscription,
          route: `/dashboard/pos/${subscription.posType.toLowerCase()}`
        })
        return
      }
    }

    // Regular modules
    modules.push({
      id: subscription.module,
      name: config.name,
      fullName: config.fullName,
      icon: config.icon,
      color: config.color,
      textColor: config.textColor,
      bgColor: config.bgColor,
      borderColor: config.borderColor,
      category: config.category,
      status,
      subscription,
      route: `/dashboard/${subscription.module.toLowerCase()}`
    })
  })

  return modules
})

// Filter modules based on search
const filteredModules = computed(() => {
  if (!searchQuery.value.trim()) {
    return availableModules.value
  }

  const query = searchQuery.value.toLowerCase()
  return availableModules.value.filter(module =>
    module.name.toLowerCase().includes(query) ||
    module.fullName.toLowerCase().includes(query) ||
    module.category.toLowerCase().includes(query)
  )
})

// Group modules by category
const modulesByCategory = computed(() => {
  const groups: Record<string, any[]> = {}

  filteredModules.value.forEach(module => {
    if (!groups[module.category]) {
      groups[module.category] = []
    }
    groups[module.category].push(module)
  })

  return groups
})

const hasModules = computed(() => availableModules.value.length > 0)

// Check restaurant POS subscription status
const restaurantSubscription = computed(() => {
  return subscriptions.value.find(sub =>
    sub.module === 'POS' && sub.posType === 'RESTAURANT'
  )
})

const restaurantAccess = computed(() => {
  if (!restaurantSubscription.value) {
    return {
      hasAccess: false,
      isExpired: false,
      needsSubscription: true,
      message: 'Restaurant features require a POS subscription'
    }
  }

  const status = getModuleStatus(restaurantSubscription.value)

  if (!status.active) {
    return {
      hasAccess: false,
      isExpired: status.type === 'trial',
      needsSubscription: false,
      message: status.type === 'trial'
        ? `Restaurant trial expired ${status.daysRemaining === 0 ? 'today' : `${Math.abs(status.daysRemaining || 0)} days ago`}. Upgrade to continue using restaurant features.`
        : 'Restaurant subscription payment is past due. Please update your payment to continue.'
    }
  }

  return {
    hasAccess: true,
    isExpired: false,
    needsSubscription: false,
    message: ''
  }
})

const handleModuleClick = (module: any) => {
  if (module.status.active) {
    // Navigate to module
    router.push(module.route)
  } else {
    // Redirect to payment/activation page
    router.push(`/dashboard/billing?module=${module.subscription.module}`)
  }
}

const handleRestaurantFeatureClick = (route: string) => {
  if (restaurantAccess.value.hasAccess) {
    router.push(route)
  } else {
    // Redirect to billing page with restaurant context
    router.push('/dashboard/billing?module=POS&type=RESTAURANT')
  }
}

// Handler for Add More Modules action
const handleAddMoreModules = () => {
  // Redirect to module marketplace or billing page
  router.push('/?explore=modules')
}

const loadSubscriptions = async () => {
  if (!authStore.currentUser?.currentCompanyId) {
    error.value = 'User not authenticated'
    return
  }

  try {
    isLoading.value = true
    error.value = ''

    const data = await companyService.getCompanySubscriptions(authStore.currentUser.currentCompanyId)
    subscriptions.value = data
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load modules'
    console.error('Failed to load subscriptions:', err)
  } finally {
    isLoading.value = false
  }
}

// Watch for company changes and reload subscriptions
watch(
  () => authStore.currentUser?.currentCompanyId,
  (newCompanyId, oldCompanyId) => {
    if (newCompanyId && newCompanyId !== oldCompanyId) {
      loadSubscriptions()
    }
  }
)

onMounted(() => {
  loadSubscriptions()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">

      <!-- Welcome message for new users -->
      <Alert v-if="route.query.trial === 'started'" class="mb-8 border-green-200 bg-green-50">
        <DuotoneIcon name="check-circle" class="h-4 w-4 text-green-600" />
        <AlertDescription class="text-green-800">
          🎉 Welcome! Your selected modules are now active and ready to use.
        </AlertDescription>
      </Alert>

      <!-- Error Alert -->
      <Alert v-if="error" variant="destructive" class="mb-8">
        <DuotoneIcon name="warning" class="h-4 w-4" />
        <AlertDescription>{{ error }}</AlertDescription>
      </Alert>

      <!-- Loading State -->
      <div v-if="isLoading" class="flex justify-center py-20">
        <div class="flex items-center gap-3">
          <svg class="animate-spin h-8 w-8 text-gray-600" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-lg text-gray-600 font-medium">Loading your modules...</span>
        </div>
      </div>

      <!-- No Modules State -->
      <div v-else-if="!hasModules" class="text-center py-20">
        <div class="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gray-100 mb-6">
          <DuotoneIcon name="apps" class="h-10 w-10 text-gray-400" />
        </div>
        <h3 class="text-2xl font-semibold text-gray-900 mb-3">No Active Modules</h3>
        <p class="text-lg text-gray-500 mb-8 max-w-md mx-auto">
          You don't have any active subscriptions or trials. Explore our business modules to get started.
        </p>
        <Button
          size="lg"
          @click="router.push('/')"
          class="bg-blue-600 hover:bg-blue-700 text-white"
        >
          <DuotoneIcon name="plus" class="h-5 w-5 mr-2" />
          Explore Modules
        </Button>
      </div>

      <!-- No Search Results -->
      <div v-else-if="searchQuery && filteredModules.length === 0" class="text-center py-16">
        <div class="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gray-100 mb-4">
          <DuotoneIcon name="search" class="h-8 w-8 text-gray-400" />
        </div>
        <h3 class="text-xl font-semibold text-gray-900 mb-2">No modules found</h3>
        <p class="text-gray-500 mb-4">
          No modules match your search for "{{ searchQuery }}"
        </p>
        <Button variant="outline" @click="searchQuery = ''">
          <DuotoneIcon name="x" class="h-4 w-4 mr-2" />
          Clear Search
        </Button>
      </div>

      <!-- Modules Grid -->
      <div v-else class="space-y-6">
        <!-- Header -->
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-semibold text-gray-900">
            {{ searchQuery ? `Search Results (${filteredModules.length})` : 'All Modules' }}
          </h2>
          <Badge variant="secondary" class="bg-gray-100 text-gray-600">
            {{ filteredModules.length }} module{{ filteredModules.length === 1 ? '' : 's' }}
          </Badge>
        </div>

        <!-- All modules and settings in one grid -->
        <div class="xcard grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
          <!-- Business Modules -->
          <Card
            v-for="module in filteredModules"
            :key="module.id"
            @click="handleModuleClick(module)"
            :class="[
              'group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 py-6',
              module.status.active ? 'hover:shadow-blue-500/25' : 'opacity-75',
              module.borderColor
            ]"
          >
            <CardContent class="p-6 flex flex-col items-center text-center">
              <!-- Status Badge -->
              <div
                v-if="module.status.type === 'trial' || !module.status.active"
                class="absolute -top-2 -right-2 z-10"
              >
                <Badge :class="[module.status.badgeColor, 'text-white text-xs px-2 py-1']">
                  {{ module.status.label }}
                </Badge>
              </div>

              <!-- Icon -->
              <div :class="[
                'w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-200 group-hover:scale-110',
                module.bgColor
              ]">
                <DuotoneIcon
                  :name="module.icon"
                  size="xl"
                  class="h-8 w-8"
                  :color="module.textColor.replace('text-', '')"
                />
              </div>

              <!-- Name -->
              <h3 class="font-semibold text-gray-900 text-sm leading-tight">
                {{ module.name }}
              </h3>

              <!-- Category Label (optional small indicator) -->
              <div class="text-xs text-gray-500 mt-1">
                {{ module.category }}
              </div>

              <!-- Inactive Overlay -->
              <div
                v-if="!module.status.active"
                class="absolute inset-0 bg-gray-900/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <div class="text-white text-sm font-medium px-3 py-1 bg-black/50 rounded-md">
                  {{ module.status.type === 'trial' ? 'Renew' : 'Activate' }}
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Categories Tile -->
          <Card
            @click="router.push('/dashboard/categories')"
            class="group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-blue-200 hover:shadow-blue-500/25"
          >
            <CardContent class="p-6 flex flex-col items-center text-center">
              <!-- Icon -->
              <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-200 group-hover:scale-110 bg-blue-50">
                <DuotoneIcon
                  name="category"
                  class="h-8 w-8 text-blue-600"
                  size="xl"
                />
              </div>

              <!-- Name -->
              <h3 class="font-semibold text-gray-900 text-sm leading-tight">
                Categories
              </h3>

              <!-- Category Label -->
              <div class="text-xs text-blue-600 mt-1">
                POS System
              </div>
            </CardContent>
          </Card>

          <!-- Price Levels Tile -->
          <Card
            @click="router.push('/dashboard/price-levels')"
            class="group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-emerald-200 hover:shadow-emerald-500/25"
          >
            <CardContent class="p-6 flex flex-col items-center text-center">
              <!-- Icon -->
              <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-200 group-hover:scale-110 bg-emerald-50">
                <DuotoneIcon
                  name="dollar"
                  class="h-8 w-8 text-emerald-600"
                  size="xl"
                />
              </div>

              <!-- Name -->
              <h3 class="font-semibold text-gray-900 text-sm leading-tight">
                Price Levels
              </h3>

              <!-- Category Label -->
              <div class="text-xs text-emerald-600 mt-1">
                POS System
              </div>
            </CardContent>
          </Card>

          <!-- Restaurant Features Section -->

          <!-- Restaurant Trial Expired Alert -->
          <div v-if="!restaurantAccess.hasAccess" class="col-span-full">
            <Alert class="border-orange-200 bg-orange-50">
              <DuotoneIcon name="warning" class="h-4 w-4 text-orange-600" />
              <AlertDescription class="text-orange-800">
                <div class="flex items-center justify-between">
                  <span>{{ restaurantAccess.message }}</span>
                  <Button
                    size="sm"
                    @click="router.push('/dashboard/billing?module=POS&type=RESTAURANT')"
                    class="bg-orange-600 hover:bg-orange-700 text-white ml-4"
                  >
                    {{ restaurantAccess.isExpired ? 'Renew Subscription' : 'Subscribe Now' }}
                  </Button>
                </div>
              </AlertDescription>
            </Alert>
          </div>

          <!-- Tables Management Tile -->
          <Card
            @click="handleRestaurantFeatureClick('/dashboard/pos/restaurant')"
            :class="[
              'group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-orange-200',
              restaurantAccess.hasAccess ? 'hover:shadow-orange-500/25' : 'opacity-60 hover:opacity-80'
            ]"
          >
            <CardContent class="p-6 flex flex-col items-center text-center">
              <!-- Trial Expired Badge -->
              <div
                v-if="!restaurantAccess.hasAccess"
                class="absolute -top-2 -right-2 z-10"
              >
                <Badge class="bg-red-500 text-white text-xs px-2 py-1">
                  {{ restaurantAccess.isExpired ? 'Trial Expired' : 'Locked' }}
                </Badge>
              </div>

              <!-- Icon -->
              <div :class="[
                'w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-200 group-hover:scale-110',
                restaurantAccess.hasAccess ? 'bg-orange-50' : 'bg-gray-100'
              ]">
                <DuotoneIcon
                  name="slider-horizontal"
                  :class="`h-8 w-8 ${restaurantAccess.hasAccess ? 'text-orange-600' : 'text-gray-400'}`"
                  size="xl"
                />
              </div>

              <!-- Name -->
              <h3 :class="['font-semibold text-sm leading-tight', restaurantAccess.hasAccess ? 'text-gray-900' : 'text-gray-500']">
                Tables
              </h3>

              <!-- Category Label -->
              <div :class="['text-xs mt-1', restaurantAccess.hasAccess ? 'text-orange-600' : 'text-gray-400']">
                Restaurant
              </div>

              <!-- Locked Overlay -->
              <div
                v-if="!restaurantAccess.hasAccess"
                class="absolute inset-0 bg-gray-900/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <div class="text-white text-sm font-medium px-3 py-1 bg-black/50 rounded-md">
                  {{ restaurantAccess.isExpired ? 'Renew' : 'Subscribe' }}
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- QR Menus Tile -->
          <Card
            @click="handleRestaurantFeatureClick('/dashboard/pos/qr-menus')"
            :class="[
              'group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-purple-200',
              restaurantAccess.hasAccess ? 'hover:shadow-purple-500/25' : 'opacity-60 hover:opacity-80'
            ]"
          >
            <CardContent class="p-6 flex flex-col items-center text-center">
              <!-- Trial Expired Badge -->
              <div
                v-if="!restaurantAccess.hasAccess"
                class="absolute -top-2 -right-2 z-10"
              >
                <Badge class="bg-red-500 text-white text-xs px-2 py-1">
                  {{ restaurantAccess.isExpired ? 'Trial Expired' : 'Locked' }}
                </Badge>
              </div>

              <!-- Icon -->
              <div :class="[
                'w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-200 group-hover:scale-110',
                restaurantAccess.hasAccess ? 'bg-purple-50' : 'bg-gray-100'
              ]">
                <DuotoneIcon
                  name="barcode"
                  :class="`h-8 w-8 ${restaurantAccess.hasAccess ? 'text-purple-600' : 'text-gray-400'}`"
                  size="xl"
                />
              </div>

              <!-- Name -->
              <h3 :class="['font-semibold text-sm leading-tight', restaurantAccess.hasAccess ? 'text-gray-900' : 'text-gray-500']">
                QR Menus
              </h3>

              <!-- Category Label -->
              <div :class="['text-xs mt-1', restaurantAccess.hasAccess ? 'text-purple-600' : 'text-gray-400']">
                Restaurant
              </div>

              <!-- Locked Overlay -->
              <div
                v-if="!restaurantAccess.hasAccess"
                class="absolute inset-0 bg-gray-900/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <div class="text-white text-sm font-medium px-3 py-1 bg-black/50 rounded-md">
                  {{ restaurantAccess.isExpired ? 'Renew' : 'Subscribe' }}
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Orders Management Tile -->
          <Card
            @click="handleRestaurantFeatureClick('/dashboard/pos/orders')"
            :class="[
              'group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-rose-200',
              restaurantAccess.hasAccess ? 'hover:shadow-rose-500/25' : 'opacity-60 hover:opacity-80'
            ]"
          >
            <CardContent class="p-6 flex flex-col items-center text-center">
              <!-- Trial Expired Badge -->
              <div
                v-if="!restaurantAccess.hasAccess"
                class="absolute -top-2 -right-2 z-10"
              >
                <Badge class="bg-red-500 text-white text-xs px-2 py-1">
                  {{ restaurantAccess.isExpired ? 'Trial Expired' : 'Locked' }}
                </Badge>
              </div>

              <!-- Icon -->
              <div :class="[
                'w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-200 group-hover:scale-110',
                restaurantAccess.hasAccess ? 'bg-rose-50' : 'bg-gray-100'
              ]">
                <DuotoneIcon
                  name="delivery-3"
                  :class="`h-8 w-8 ${restaurantAccess.hasAccess ? 'text-rose-600' : 'text-gray-400'}`"
                  size="xl"
                />

              </div>

              <!-- Name -->
              <h3 :class="['font-semibold text-sm leading-tight', restaurantAccess.hasAccess ? 'text-gray-900' : 'text-gray-500']">
                Orders
              </h3>

              <!-- Category Label -->
              <div :class="['text-xs mt-1', restaurantAccess.hasAccess ? 'text-rose-600' : 'text-gray-400']">
                Restaurant
              </div>

              <!-- Locked Overlay -->
              <div
                v-if="!restaurantAccess.hasAccess"
                class="absolute inset-0 bg-gray-900/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <div class="text-white text-sm font-medium px-3 py-1 bg-black/50 rounded-md">
                  {{ restaurantAccess.isExpired ? 'Renew' : 'Subscribe' }}
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Kitchen Display Tile -->
          <Card
            @click="handleRestaurantFeatureClick('/dashboard/pos/kitchen')"
            :class="[
              'group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-amber-200',
              restaurantAccess.hasAccess ? 'hover:shadow-amber-500/25' : 'opacity-60 hover:opacity-80'
            ]"
          >
            <CardContent class="p-6 flex flex-col items-center text-center">
              <!-- Trial Expired Badge -->
              <div
                v-if="!restaurantAccess.hasAccess"
                class="absolute -top-2 -right-2 z-10"
              >
                <Badge class="bg-red-500 text-white text-xs px-2 py-1">
                  {{ restaurantAccess.isExpired ? 'Trial Expired' : 'Locked' }}
                </Badge>
              </div>

              <!-- Icon -->
              <div :class="[
                'w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-200 group-hover:scale-110',
                restaurantAccess.hasAccess ? 'bg-amber-50' : 'bg-gray-100'
              ]">
                <DuotoneIcon
                  name="notification-on"
                  :class="`h-8 w-8 ${restaurantAccess.hasAccess ? 'text-amber-600' : 'text-gray-400'}`"
                  size="xl"
                />
              </div>

              <!-- Name -->
              <h3 :class="['font-semibold text-sm leading-tight', restaurantAccess.hasAccess ? 'text-gray-900' : 'text-gray-500']">
                Kitchen
              </h3>

              <!-- Category Label -->
              <div :class="['text-xs mt-1', restaurantAccess.hasAccess ? 'text-amber-600' : 'text-gray-400']">
                Restaurant
              </div>

              <!-- Locked Overlay -->
              <div
                v-if="!restaurantAccess.hasAccess"
                class="absolute inset-0 bg-gray-900/20 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              >
                <div class="text-white text-sm font-medium px-3 py-1 bg-black/50 rounded-md">
                  {{ restaurantAccess.isExpired ? 'Renew' : 'Subscribe' }}
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Settings Tile -->
          <Card
            @click="router.push('/dashboard/settings')"
            class="group relative cursor-pointer transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-2 border-gray-200 hover:shadow-gray-500/25"
          >
            <CardContent class="p-6 flex flex-col items-center text-center">
              <!-- Icon -->
              <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-transform duration-200 group-hover:scale-110 bg-gray-50">
                <DuotoneIcon
                  name="setting"
                  class="h-8 w-8"
                  size="xl"
                />
              </div>

              <!-- Name -->
              <h3 class="font-semibold text-gray-900 text-sm leading-tight">
                Settings
              </h3>

              <!-- Category Label -->
              <div class="text-xs text-gray-500 mt-1">
                Settings
              </div>
            </CardContent>
          </Card>

          <!-- Add More Modules Tile -->
          <Card
            @click="handleAddMoreModules"
            class="group relative cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-2 border-dashed border-blue-300 hover:border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100"
          >
            <CardContent class="p-6 flex flex-col items-center text-center relative overflow-hidden">
              <!-- Background decoration -->
              <!-- <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> -->

              <!-- New Badge -->
              <div class="absolute -top-1 -right-1 z-10">
                <Badge class="bg-blue-500 text-white text-xs px-2 py-1 animate-pulse">
                  New
                </Badge>
              </div>

              <!-- Icon with animated background -->
              <div class="w-16 h-16 rounded-2xl flex items-center justify-center mb-4 transition-all duration-300 group-hover:scale-110 bg-gradient-to-br from-blue-100 to-indigo-100 group-hover:from-blue-200 group-hover:to-indigo-200 relative z-10">
                <DuotoneIcon
                  name="plus"
                  size="xl"
                  class="h-8 w-8 text-blue-600 group-hover:text-blue-700 transition-colors duration-300"
                />
              </div>

              <!-- Name with enhanced styling -->
              <h3 class="font-semibold text-blue-900 text-sm leading-tight mb-1 relative z-10">
                Add More Modules
              </h3>

              <!-- Description -->
              <div class="text-xs text-blue-600/70 mt-1 relative z-10">
                Expand your business
              </div>

              <!-- Hover overlay with call-to-action -->
              <!-- <div class="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-indigo-600/10 rounded-lg flex items-end justify-center pb-4 opacity-0 group-hover:opacity-100 transition-all duration-300 z-20">
                <div class="text-blue-700 text-xs font-medium px-3 py-1 bg-white/80 rounded-full shadow-sm">
                  Browse Modules
                </div>
              </div> -->
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Quick Actions Footer -->
      <!-- <div v-if="hasModules" class="mt-16 pt-8 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500">
            {{ availableModules.length }} active module{{ availableModules.length === 1 ? '' : 's' }} + settings
          </div>
          <div class="flex gap-3">
            <Button variant="outline" @click="router.push('/')">
              <DuotoneIcon name="plus" class="h-4 w-4 mr-2" />
              Add More Modules
            </Button>
            <Button variant="outline" @click="router.push('/dashboard/billing')">
              <DuotoneIcon name="credit-card" class="h-4 w-4 mr-2" />
              Manage Billing
            </Button>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>
