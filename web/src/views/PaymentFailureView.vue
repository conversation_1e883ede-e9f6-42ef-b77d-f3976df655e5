<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="max-w-md w-full">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <!-- Error Icon -->
        <div class="w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <DuotoneIcon name="x-circle" class="w-12 h-12 text-red-600 dark:text-red-400" />
        </div>

        <!-- Error Message -->
        <h1 class="text-2xl font-bold text-red-900 dark:text-red-100 mb-2">
          {{ $t('payment.failure.title') }}
        </h1>
        <p class="text-red-700 dark:text-red-300 mb-6">
          {{ $t('payment.failure.description') }}
        </p>

        <!-- Payment Details -->
        <div v-if="paymentDetails" class="bg-red-50 dark:bg-red-900/10 rounded-lg p-4 mb-6">
          <div class="space-y-2 text-sm text-red-800 dark:text-red-200">
            <div class="flex justify-between">
              <span>{{ $t('payment.paymentId') }}:</span>
              <span class="font-mono">{{ paymentDetails.paymentId }}</span>
            </div>
            <div class="flex justify-between">
              <span>{{ $t('payment.amount') }}:</span>
              <span class="font-semibold">
                {{ formatCurrency(paymentDetails.amount, paymentDetails.currency) }}
              </span>
            </div>
            <div v-if="paymentDetails.errorMessage" class="flex justify-between">
              <span>{{ $t('payment.error') }}:</span>
              <span class="text-red-600 dark:text-red-400">{{ paymentDetails.errorMessage }}</span>
            </div>
          </div>
        </div>

        <!-- Error Codes -->
        <div v-if="errorCode" class="text-sm text-gray-600 dark:text-gray-400 mb-6">
          {{ $t('payment.errorCode') }}: {{ errorCode }}
        </div>

        <!-- Actions -->
        <div class="space-y-3">
          <Button
            @click="retryPayment"
            class="w-full bg-red-600 hover:bg-red-700 text-white"
            :disabled="isRetrying"
          >
            <div v-if="isRetrying" class="flex items-center gap-2">
              <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              {{ $t('payment.retrying') }}
            </div>
            <div v-else class="flex items-center gap-2">
              <DuotoneIcon name="arrow-path" class="w-4 h-4" />
              {{ $t('payment.retry') }}
            </div>
          </Button>

          <Button variant="outline" @click="goToSubscriptions" class="w-full">
            {{ $t('payment.viewSubscriptions') }}
          </Button>

          <Button variant="ghost" @click="contactSupport" class="w-full text-sm">
            {{ $t('payment.contactSupport') }}
          </Button>
        </div>

        <!-- Support Information -->
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <h3 class="text-sm font-semibold text-gray-900 dark:text-gray-100 mb-2">
            {{ $t('payment.needHelp') }}
          </h3>
          <p class="text-xs text-gray-600 dark:text-gray-400">
            {{ $t('payment.supportInfo') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Button } from '@/components/ui/button'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import { PaymentService } from '@/services/payment.service'
import { toast } from 'vue-sonner'
import type { Currency } from '@/types/payment'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()

const paymentService = new PaymentService()

// Data
const paymentDetails = ref<any>(null)
const isRetrying = ref(false)

// Get payment details from query params
const paymentId = route.query.paymentId as string
const errorCode = route.query.error_code as string
const errorMessage = route.query.error_message as string

// Methods
const formatCurrency = (amount: number, currency: Currency) => {
  if (currency === 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  } else {
    return new Intl.NumberFormat('km-KH', {
      style: 'currency',
      currency: 'KHR'
    }).format(amount)
  }
}

const retryPayment = async () => {
  if (!paymentDetails.value) return

  try {
    isRetrying.value = true

    // Create new payment with same details
    const newPayment = await paymentService.createPayment({
      amount: paymentDetails.value.amount,
      currency: paymentDetails.value.currency,
      description: paymentDetails.value.description,
      companyId: paymentDetails.value.companyId,
      module: paymentDetails.value.module,
      paymentMethod: 'ACLEDA_ECOMMERCE',
      customerInfo: paymentDetails.value.customerInfo,
      subscriptionId: paymentDetails.value.subscriptionId,
      trialConversion: paymentDetails.value.trialConversion
    })

    if (newPayment.redirectUrl) {
      // Redirect to ACLEDA payment page
      window.location.href = newPayment.redirectUrl
    } else {
      toast.error('Failed to create new payment session')
    }
  } catch (error) {
    console.error('Retry payment failed:', error)
    toast.error('Failed to retry payment. Please try again.')
  } finally {
    isRetrying.value = false
  }
}

const goToSubscriptions = () => {
  router.push('/dashboard/billing')
}

const contactSupport = () => {
  // Open email client or support page
  window.location.href = 'mailto:<EMAIL>?subject=Payment Failed - ' + paymentId
}

// Load payment details
onMounted(async () => {
  if (!paymentId) {
    // Redirect to billing if no payment ID
    router.replace('/dashboard/billing')
    return
  }

  try {
    const status = await paymentService.checkPaymentStatus(paymentId)
    paymentDetails.value = {
      ...status,
      errorMessage: errorMessage || (status as any).errorMessage
    }
  } catch (error) {
    console.error('Failed to load payment details:', error)
    paymentDetails.value = {
      paymentId,
      errorMessage: errorMessage || 'Unknown error occurred'
    }
  }
})
</script>
