<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { companyService } from '../services/company.service'
import { Button } from '../components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Alert, AlertDescription } from '../components/ui/alert'
import DuotoneIcon from '../components/DuotoneIcon.vue'
import type { CompanySubscription, TrialInfo, PosBusinessType } from '../types/company'

const router = useRouter()
const authStore = useAuthStore()

// State
const isLoading = ref(false)
const errorMessage = ref('')
const selectedModules = ref<string[]>([])
const availableModules = ref<any>({ modules: [], posTypes: [] })
const loadingModules = ref(true)
const showMobileMenu = ref(false)
const compactView = ref(false)
const expandedFeatures = ref<Set<string>>(new Set())
const activeSubscriptions = ref<CompanySubscription[]>([])
const loadingSubscriptions = ref(false)
const trialStatus = ref<any>(null)
const trialConfig = ref<{ trialDurationDays: number; maxTrials: number }>({
  trialDurationDays: 30,
  maxTrials: 3
})

// Module metadata for UI display
const moduleMetadata = ref({
  // Core modules
  ERP: {
    fullName: 'Enterprise Resource Planning',
    description: 'Complete business management solution with integrated modules',
    icon: 'chart-line',
    category: 'Core Business',
    popular: true,
    recommended: false,
    color: 'purple',
    features: [
      'Unified business dashboard',
      'Complete workflow automation',
      'Real-time analytics & reporting',
      'Multi-department integration',
      'Advanced financial management',
      'Customer relationship management'
    ]
  },
  ACCOUNTING: {
    fullName: 'Accounting & Finance',
    description: 'Professional accounting and financial management tools',
    icon: 'calculator',
    category: 'Core Business',
    popular: false,
    recommended: true,
    color: 'green',
    features: [
      'Double-entry bookkeeping',
      'Invoice & billing management',
      'Tax calculation & reporting',
      'Financial statements',
      'Budget planning & tracking',
      'Expense management'
    ]
  },
  LOAN: {
    fullName: 'Loan Processing System',
    description: 'Comprehensive loan processing and tracking system',
    icon: 'dollar-sign',
    category: 'Finance',
    popular: false,
    recommended: false,
    color: 'blue',
    features: [
      'Loan application processing',
      'Credit assessment tools',
      'Payment schedule management',
      'Interest calculations',
      'Risk analysis & reporting',
      'Automated notifications'
    ]
  }
})

// POS business type mappings - following company.ts PosBusinessType with specialized features
const posBusinessTypeConfig = ref<Record<PosBusinessType, {
  name: string;
  description: string;
  icon: string;
  color: string;
  features: string[];
  onlineFeatures: string[];
  specializations: string[];
}>>({
  RESTAURANT: {
    name: 'Restaurant',
    description: 'Full-service restaurant management with table service, kitchen operations, and online ordering',
    icon: 'utensils',
    color: 'orange',
    features: [
      'Table management system',
      'Kitchen display screens',
      'Menu categorization',
      'Staff order tracking',
      'Khmer food categories',
      'Multi-language support'
    ],
    onlineFeatures: [
      'QR code e-menu ordering',
      'BBQ unlimited order mode',
      'Online ordering platform',
      'Delivery & pickup options',
      'Customer review system',
      'Real-time order tracking'
    ],
    specializations: [
      'Cambodian cuisine optimization',
      'Traditional dish categorization',
      'Spice level customization',
      'Group ordering features'
    ]
  },
  BAR: {
    name: 'Bar & Lounge',
    description: 'Bar and beverage service with floor planning, drink recipes, and table ordering',
    icon: 'cocktail',
    color: 'purple',
    features: [
      'Interactive floor plan',
      'Tips management',
      'Drink recipe database',
      'Inventory tracking',
      'Age verification',
      'Happy hour pricing'
    ],
    onlineFeatures: [
      'QR code table ordering',
      'Online drink menu',
      'Table service requests',
      'Digital drink catalog',
      'Event booking system',
      'Loyalty program integration'
    ],
    specializations: [
      'Cocktail customization',
      'Bar inventory optimization',
      'Entertainment scheduling',
      'VIP table management'
    ]
  },
  BAKERY: {
    name: 'Bakery',
    description: 'Bakery operations with fresh item tracking, expiry management, and pre-order system',
    icon: 'cake',
    color: 'yellow',
    features: [
      'Fresh daily items tracking',
      'Expiry date management',
      'Production scheduling',
      'Ingredient inventory',
      'Traditional Khmer sweets',
      'Batch tracking system'
    ],
    onlineFeatures: [
      'Online pre-order system',
      'Pickup scheduling',
      'Custom cake orders',
      'Wedding cake gallery',
      'Delivery time slots',
      'Subscription boxes'
    ],
    specializations: [
      'Khmer dessert categories',
      'Seasonal specialties',
      'Wedding cake planning',
      'Festival order management'
    ]
  },
  RETAIL_SHOP: {
    name: 'Retail Shop',
    description: 'General retail operations with barcode scanning, inventory focus, and e-commerce integration',
    icon: 'shopping-bag',
    color: 'indigo',
    features: [
      'Barcode scanning system',
      'Advanced inventory management',
      'General merchandise tracking',
      'Supplier management',
      'Stock alerts',
      'Price management'
    ],
    onlineFeatures: [
      'E-commerce website',
      'Online product catalog',
      'Shopping cart system',
      'Payment integration',
      'Order fulfillment',
      'Customer accounts'
    ],
    specializations: [
      'Multi-category merchandise',
      'Bulk pricing options',
      'Seasonal promotions',
      'Customer loyalty tracking'
    ]
  },
  CLOTHING_STORE: {
    name: 'Clothing Store',
    description: 'Fashion retail with size/color variants, seasonal inventory, and virtual try-on features',
    icon: 'shirt',
    color: 'pink',
    features: [
      'Size & color variants',
      'Seasonal inventory management',
      'Fashion trend tracking',
      'Style categorization',
      'Brand management',
      'Collection organization'
    ],
    onlineFeatures: [
      'Online fashion catalog',
      'Virtual try-on technology',
      'Size guide integration',
      'Style recommendations',
      'Wishlist functionality',
      'Fashion blog integration'
    ],
    specializations: [
      'Seasonal collection planning',
      'Trend forecasting tools',
      'Fashion show management',
      'Style consultation booking'
    ]
  },
  FURNITURE_STORE: {
    name: 'Furniture Store',
    description: 'Furniture retail with custom orders, delivery tracking, and 3D room visualization',
    icon: 'chair',
    color: 'amber',
    features: [
      'Custom order management',
      'Delivery tracking system',
      'Room configurator',
      'Assembly scheduling',
      'Warranty tracking',
      'Installation services'
    ],
    onlineFeatures: [
      'Online showroom',
      '3D room visualization',
      'Virtual furniture placement',
      'Custom design tools',
      'Delivery scheduling',
      'Installation booking'
    ],
    specializations: [
      'Custom furniture design',
      'Room planning services',
      'Interior design consultation',
      'Bulk furniture solutions'
    ]
  },
  PHARMACY: {
    name: 'Pharmacy',
    description: 'Pharmaceutical operations with medicine tracking, prescription management, and online ordering',
    icon: 'pill',
    color: 'cyan',
    features: [
      'Medicine inventory tracking',
      'Prescription management',
      'Expiry date alerts',
      'Drug interaction checks',
      'Insurance processing',
      'Regulatory compliance'
    ],
    onlineFeatures: [
      'Online prescription ordering',
      'Medicine delivery service',
      'Prescription refill reminders',
      'Health consultation booking',
      'Medicine information portal',
      'Insurance verification'
    ],
    specializations: [
      'Prescription workflow optimization',
      'Drug safety protocols',
      'Patient consultation services',
      'Health screening programs'
    ]
  },
  ELECTRONICS_STORE: {
    name: 'Electronics Store',
    description: 'Electronics retail with warranty tracking, technical specifications, and product comparisons',
    icon: 'smartphone',
    color: 'gray',
    features: [
      'Warranty tracking system',
      'Technical specifications database',
      'Product comparison tools',
      'Repair service management',
      'Trade-in programs',
      'Extended warranty options'
    ],
    onlineFeatures: [
      'Online electronics store',
      'Product comparison engine',
      'Technical specification search',
      'Review and rating system',
      'Repair service booking',
      'Trade-in value calculator'
    ],
    specializations: [
      'Technology product expertise',
      'Repair service coordination',
      'Extended warranty management',
      'Trade-in program optimization'
    ]
  },
  GROCERY_STORE: {
    name: 'Grocery Store',
    description: 'Grocery operations with fresh produce tracking, bulk items, and scheduled delivery',
    icon: 'shopping-cart',
    color: 'green',
    features: [
      'Fresh produce management',
      'Bulk item pricing',
      'Local supplier network',
      'Expiry date tracking',
      'Nutritional information',
      'Organic product categorization'
    ],
    onlineFeatures: [
      'Online grocery shopping',
      'Scheduled delivery system',
      'Fresh produce guarantee',
      'Subscription groceries',
      'Recipe integration',
      'Nutritional meal planning'
    ],
    specializations: [
      'Fresh produce optimization',
      'Local supplier partnerships',
      'Meal planning services',
      'Healthy eating programs'
    ]
  },
  BEAUTY_SALON: {
    name: 'Beauty Salon',
    description: 'Beauty and wellness services with appointment booking, treatment packages, and online scheduling',
    icon: 'scissors',
    color: 'rose',
    features: [
      'Service appointment system',
      'Treatment package management',
      'Staff scheduling optimization',
      'Client history tracking',
      'Product inventory',
      'Service customization'
    ],
    onlineFeatures: [
      'Online booking system',
      'Service catalog with photos',
      'Stylist selection',
      'Beauty consultation booking',
      'Treatment package deals',
      'Client review system'
    ],
    specializations: [
      'Beauty treatment optimization',
      'Stylist expertise matching',
      'Seasonal beauty packages',
      'Bridal service planning'
    ]
  },
  SERVICE: {
    name: 'Service Business',
    description: 'General service operations with booking, scheduling, and online service requests',
    icon: 'tools',
    color: 'slate',
    features: [
      'Service booking system',
      'Advanced scheduling',
      'Service management tools',
      'Client communication',
      'Service history tracking',
      'Performance analytics'
    ],
    onlineFeatures: [
      'Online service requests',
      'Appointment booking portal',
      'Service quotation system',
      'Progress tracking',
      'Client feedback collection',
      'Service guarantee programs'
    ],
    specializations: [
      'Custom service workflows',
      'Multi-service coordination',
      'Quality assurance tracking',
      'Service optimization tools'
    ]
  },
  HOTEL: {
    name: 'Hotel & Hospitality',
    description: 'Hotel operations with room booking, guest management, housekeeping, and online services',
    icon: 'bed',
    color: 'teal',
    features: [
      'Room booking system',
      'Check-in/check-out management',
      'Guest profile management',
      'Housekeeping coordination',
      'Billing & invoicing',
      'Concierge services'
    ],
    onlineFeatures: [
      'Online room booking',
      'Guest services portal',
      'Virtual room tours',
      'Concierge service requests',
      'Local attraction booking',
      'Room service ordering'
    ],
    specializations: [
      'Guest experience optimization',
      'Tourism service integration',
      'Event planning coordination',
      'VIP guest management'
    ]
  },
  GENERIC: {
    name: 'Generic Business',
    description: 'Customizable system for any business type with flexible online ordering and management',
    icon: 'building',
    color: 'neutral',
    features: [
      'Customizable workflows',
      'Flexible product/service management',
      'Adaptable inventory system',
      'Multi-purpose scheduling',
      'Custom reporting',
      'Business-specific customization'
    ],
    onlineFeatures: [
      'Flexible online ordering',
      'Customizable customer portal',
      'Multi-purpose booking system',
      'Adaptable payment processing',
      'Custom business workflows',
      'Industry-specific integrations'
    ],
    specializations: [
      'Business model flexibility',
      'Custom feature development',
      'Industry-specific adaptations',
      'Scalable business solutions'
    ]
  }
})

// Computed properties for organized modules - built from backend data
const moduleCategories = computed(() => {
  if (!availableModules.value.modules || !availableModules.value.posTypes) {
    return []
  }

  // Get list of already subscribed modules to exclude from selection
  const subscribedModules = new Set(activeSubscriptions.value.map(sub => {
    if (sub.module === 'POS' && sub.posType) {
      return `POS_${sub.posType}`
    }
    return sub.module
  }))

  const categories = []

  // Core Business modules (ERP, ACCOUNTING)
  const coreBusinessModules = availableModules.value.modules
    .filter((m: any) => ['ERP', 'ACCOUNTING'].includes(m.name))
    .filter((m: any) => !subscribedModules.has(m.name)) // Exclude already subscribed modules
    .map((module: any) => ({
      id: module.name,
      name: module.name,
      fullName: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.fullName || module.name,
      description: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.description || '',
      price: module.price,
      icon: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.icon || 'box',
      color: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.color || 'blue',
      category: 'Core Business',
      popular: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.popular || false,
      recommended: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.recommended || false,
      features: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.features || []
    }))

  if (coreBusinessModules.length > 0) {
    categories.push({
      id: 'core',
      name: 'Core Business',
      description: 'Essential business management modules',
      icon: 'building',
      modules: coreBusinessModules
    })
  }

  // Finance modules (LOAN)
  const financeModules = availableModules.value.modules
    .filter((m: any) => ['LOAN'].includes(m.name))
    .filter((m: any) => !subscribedModules.has(m.name)) // Exclude already subscribed modules
    .map((module: any) => ({
      id: module.name,
      name: module.name,
      fullName: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.fullName || module.name,
      description: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.description || '',
      price: module.price,
      icon: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.icon || 'dollar-sign',
      color: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.color || 'blue',
      category: 'Finance',
      popular: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.popular || false,
      recommended: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.recommended || false,
      features: moduleMetadata.value[module.name as keyof typeof moduleMetadata.value]?.features || []
    }))

  if (financeModules.length > 0) {
    categories.push({
      id: 'finance',
      name: 'Finance',
      description: 'Financial services and loan management',
      icon: 'dollar-sign',
      modules: financeModules
    })
  }

  // Sales & POS modules - built from backend posTypes
  const posModules = availableModules.value.posTypes
    .filter((posType: any) => !subscribedModules.has(`POS_${posType.type}`)) // Exclude already subscribed POS types
    .map((posType: any) => ({
        id: `POS_${posType.type}`,
      name: `${posBusinessTypeConfig.value[posType.type as PosBusinessType]?.name || posType.type} POS`,
      fullName: `${posBusinessTypeConfig.value[posType.type as PosBusinessType]?.name || posType.type} Point of Sale`,
      description: posBusinessTypeConfig.value[posType.type as PosBusinessType]?.description || 'Point of sale system',
        price: posType.price,
        icon: posBusinessTypeConfig.value[posType.type as PosBusinessType]?.icon || 'cash-register',
      color: posBusinessTypeConfig.value[posType.type as PosBusinessType]?.color || 'blue',
        category: 'Sales & POS',
        posType: posType.type,
      popular: posType.type === 'RESTAURANT',
      recommended: posType.type === 'GENERIC',
        features: [
          'Industry-specific POS interface',
          'Inventory management',
          'Sales reporting & analytics',
          'Customer management',
          'Payment processing',
          'Receipt & invoice generation'
        ]
      }))

  if (posModules.length > 0) {
    categories.push({
      id: 'sales',
      name: 'Sales & POS',
      description: 'Point of sale and sales management',
      icon: 'cash-register',
      modules: posModules
    })
  }

  return categories
})

// Computed
const isAuthenticated = computed(() => authStore.isAuthenticated)
const currentUser = computed(() => authStore.currentUser)

const selectedModulesCount = computed(() => selectedModules.value.length)

const totalMonthlyPrice = computed(() => {
  let total = 0
  for (const category of moduleCategories.value) {
    for (const module of category.modules) {
      if (selectedModules.value.includes(module.id)) {
        total += module.price
      }
    }
  }
  return total
})

const selectedModuleDetails = computed(() => {
  const details: any[] = []
  for (const category of moduleCategories.value) {
    for (const module of category.modules) {
      if (selectedModules.value.includes(module.id)) {
        details.push({
          ...module,
          category: category.name
        })
      }
    }
  }
  return details
})

// Methods
const loadAvailableModules = async () => {
  try {
    loadingModules.value = true
    const modules = await companyService.getAvailableModules()
    availableModules.value = modules

    // Set trial configuration from backend constants (3 days trial as per backend TRIAL_CONFIG)
          trialConfig.value = {
      trialDurationDays: 3, // From backend TRIAL_CONFIG.TRIAL_DURATION_DAYS
      maxTrials: 1 // From backend TRIAL_CONFIG.MAX_TRIALS per module
        }

    console.log('Loaded modules from backend:', {
      modules: modules.modules,
      posTypes: modules.posTypes,
      trialConfig: trialConfig.value
    })
  } catch (error) {
    console.error('Failed to load modules:', error)
    errorMessage.value = 'Failed to load available modules'
  } finally {
    loadingModules.value = false
  }
}

const toggleModuleSelection = (moduleId: string) => {
  const index = selectedModules.value.indexOf(moduleId)
  if (index > -1) {
    selectedModules.value.splice(index, 1)
  } else {
    selectedModules.value.push(moduleId)
  }
}

const isModuleSelected = (moduleId: string) => {
  return selectedModules.value.includes(moduleId)
}

const getModuleById = (moduleId: string) => {
  for (const category of moduleCategories.value) {
    for (const module of category.modules) {
      if (module.id === moduleId) {
        return module
      }
    }
  }
  return null
}

const removeSelectedModule = (moduleId: string) => {
  const index = selectedModules.value.indexOf(moduleId)
  if (index > -1) {
    selectedModules.value.splice(index, 1)
  }
}

const handleContinue = async () => {
  if (selectedModulesCount.value === 0) {
    errorMessage.value = 'Please select at least one module to continue'
    return
  }

  if (!isAuthenticated.value) {
    // Redirect to registration with selected modules
    const moduleParams = selectedModules.value.join(',')
    const posTypesParams = selectedModuleDetails.value
      .filter(m => m.posType)
      .map(m => `${m.id}:${m.posType}`)
      .join(',')

    const queryParams = new URLSearchParams({
      modules: moduleParams,
      ...(posTypesParams && { posTypes: posTypesParams })
    })

    router.push(`/auth/register?${queryParams.toString()}`)
    return
  }

  if (!authStore.currentUser?.currentCompanyId) {
    errorMessage.value = 'No company found. Please complete your company setup first.'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    // Check if any selected modules are already subscribed (shouldn't happen with filtering, but safety check)
    const subscribedModuleIds = new Set(activeSubscriptions.value.map(sub => {
      if (sub.module === 'POS' && sub.posType) {
        return `POS_${sub.posType}`
      }
      return sub.module
    }))

    const availableModules = selectedModuleDetails.value.filter(module =>
      !subscribedModuleIds.has(module.id)
    )

    if (availableModules.length === 0) {
      errorMessage.value = 'All selected modules are already active. Please check your active subscriptions.'
      return
    }

    // Start trials for all selected modules that aren't already subscribed
    const trialPromises = availableModules.map(async (module) => {
      try {
      if (module.posType) {
        // POS module - use the posType from company.ts
        return await companyService.startPOSTrial(authStore.currentUser!.currentCompanyId!, {
          posType: module.posType as PosBusinessType
        })
      } else {
        // Regular module - use exact module names from company.ts
        return await companyService.startTrial(authStore.currentUser!.currentCompanyId!, {
          module: module.id as 'ERP' | 'LOAN' | 'ACCOUNTING' | 'POS'
        })
      }
      } catch (moduleError) {
        // Log module-specific errors but don't fail the entire process
        console.warn(`Failed to start trial for ${module.name}:`, moduleError)
        throw new Error(`Failed to start ${module.name} trial: ${moduleError instanceof Error ? moduleError.message : 'Unknown error'}`)
      }
    })

    const results = await Promise.allSettled(trialPromises)

    // Check results and handle partial failures
    const successful = results.filter(r => r.status === 'fulfilled')
    const failed = results.filter(r => r.status === 'rejected')

    if (successful.length === 0) {
      // All trials failed
      const errorMessages = failed.map(f => (f as PromiseRejectedResult).reason.message).join('; ')
      throw new Error(`All trials failed: ${errorMessages}`)
    }

    if (failed.length > 0) {
      // Some trials failed - show warning but continue
      const failedModules = failed.length
      console.warn(`${failedModules} trial(s) failed to start`)
    }

    // Reload active subscriptions to show new trials
    await loadActiveSubscriptions()

    // Clear selected modules
    selectedModules.value = []

    // Show success message
    const successCount = successful.length
    const moduleText = successCount === 1 ? 'trial' : 'trials'

    if (failed.length > 0) {
      errorMessage.value = `Started ${successCount} ${moduleText} successfully. ${failed.length} failed - they may already be active.`
    }

    // Navigate to dashboard with started modules
    const startedModuleIds = availableModules.slice(0, successful.length).map(m => m.id)
    const moduleParams = startedModuleIds.join(',')
    router.push(`/dashboard?modules=${moduleParams}&trial=started`)
  } catch (error) {
    console.error('Trial start error:', error)
    errorMessage.value = error instanceof Error ? error.message : 'Failed to start trials'
  } finally {
    isLoading.value = false
  }
}

const formatPrice = (price: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(price)
}

const scrollToModules = () => {
  document.getElementById('modules-section')?.scrollIntoView({ behavior: 'smooth' })
}

const scrollToActiveModules = () => {
  document.getElementById('active-modules-section')?.scrollIntoView({ behavior: 'smooth' })
}

const toggleCompactView = () => {
  compactView.value = !compactView.value
}

const toggleFeatures = (moduleId: string) => {
  if (expandedFeatures.value.has(moduleId)) {
    expandedFeatures.value.delete(moduleId)
  } else {
    expandedFeatures.value.add(moduleId)
  }
}

// Active Subscriptions Management
const loadActiveSubscriptions = async () => {
  if (!isAuthenticated.value || !authStore.currentUser?.currentCompanyId) {
    return
  }

  try {
    loadingSubscriptions.value = true
    const [subscriptions, trialStatusData] = await Promise.all([
      companyService.getCompanySubscriptions(authStore.currentUser.currentCompanyId),
      companyService.getTrialStatus(authStore.currentUser.currentCompanyId)
    ])

    activeSubscriptions.value = subscriptions
    trialStatus.value = trialStatusData

    console.log('Loaded active subscriptions:', {
      subscriptions: subscriptions.length,
      trialStatus: trialStatusData
    })
  } catch (error) {
    console.error('Failed to load active subscriptions:', error)
  } finally {
    loadingSubscriptions.value = false
  }
}

const accessModule = (subscription: CompanySubscription) => {
  console.log('Accessing module:', subscription.module, subscription.posType)

  // Navigate to the appropriate module based on type
  if (subscription.module === 'POS') {
    // Navigate to POS interface with business type
    router.push(`/dashboard/pos?type=${subscription.posType}`)
  } else {
    // Navigate to specific module dashboard
    router.push(`/dashboard/${subscription.module.toLowerCase()}`)
  }
}

const getDaysRemaining = (endDate: Date | string) => {
  const end = new Date(endDate)
  const now = new Date()
  const diffTime = end.getTime() - now.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

const getModuleDisplayName = (subscription: CompanySubscription) => {
  if (subscription.module === 'POS' && subscription.posType) {
    return `${posBusinessTypeConfig.value[subscription.posType as PosBusinessType]?.name || subscription.posType} POS`
  }
  return moduleMetadata.value[subscription.module as keyof typeof moduleMetadata.value]?.fullName || subscription.module
}

const getModuleIcon = (subscription: CompanySubscription) => {
  if (subscription.module === 'POS' && subscription.posType) {
    return posBusinessTypeConfig.value[subscription.posType as PosBusinessType]?.icon || 'cash-register'
  }
  return moduleMetadata.value[subscription.module as keyof typeof moduleMetadata.value]?.icon || 'box'
}

const getModuleColor = (subscription: CompanySubscription) => {
  if (subscription.module === 'POS' && subscription.posType) {
    return posBusinessTypeConfig.value[subscription.posType as PosBusinessType]?.color || 'blue'
  }
  return moduleMetadata.value[subscription.module as keyof typeof moduleMetadata.value]?.color || 'blue'
}

// Computed properties for active subscriptions
const activeModulesCount = computed(() => activeSubscriptions.value.length)

const trialSubscriptions = computed(() =>
  activeSubscriptions.value.filter(sub => sub.isTrialMode)
)

const paidSubscriptions = computed(() =>
  activeSubscriptions.value.filter(sub => !sub.isTrialMode)
)

const hasActiveModules = computed(() => activeModulesCount.value > 0)

// Computed property for modules that are excluded because they're already subscribed
const excludedModules = computed(() => {
  if (!availableModules.value.modules || !availableModules.value.posTypes || !hasActiveModules.value) {
    return []
  }

  const subscribedModules = activeSubscriptions.value.map(sub => {
    if (sub.module === 'POS' && sub.posType) {
      return {
        id: `POS_${sub.posType}`,
        name: `${posBusinessTypeConfig.value[sub.posType as PosBusinessType]?.name || sub.posType} POS`,
        module: sub.module,
        posType: sub.posType,
        isTrialMode: sub.isTrialMode,
        trialEndDate: sub.trialEndDate,
        subscription: sub
      }
    }
    return {
      id: sub.module,
      name: moduleMetadata.value[sub.module as keyof typeof moduleMetadata.value]?.fullName || sub.module,
      module: sub.module,
      posType: sub.posType,
      isTrialMode: sub.isTrialMode,
      trialEndDate: sub.trialEndDate,
      subscription: sub
    }
  })

  return subscribedModules
})

const totalAvailableModules = computed(() => {
  return moduleCategories.value.reduce((total, cat) => total + cat.modules.length, 0)
})

onMounted(async () => {
  await loadAvailableModules()
  // Load active subscriptions if user is authenticated
  if (isAuthenticated.value) {
    await loadActiveSubscriptions()
  }
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/40">
    <!-- Enhanced Hero Section -->
    <section class="relative min-h-screen flex items-center overflow-hidden">
      <!-- Dynamic Background Elements -->
      <div class="absolute inset-0">
        <!-- Gradient Mesh Background -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-indigo-50/30 to-purple-50/50"></div>

        <!-- Animated Orbs -->
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-blue-200/40 to-cyan-200/40 rounded-full mix-blend-multiply filter blur-3xl animate-float"></div>
        <div class="absolute top-3/4 right-1/4 w-80 h-80 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full mix-blend-multiply filter blur-3xl animate-float-delayed"></div>
        <div class="absolute bottom-1/4 left-1/2 w-72 h-72 bg-gradient-to-br from-indigo-200/40 to-blue-200/40 rounded-full mix-blend-multiply filter blur-3xl animate-float-slow"></div>

        <!-- Grid Pattern -->
        <div class="absolute inset-0 bg-grid-slate-100/50 bg-grid-16 opacity-30"></div>
      </div>

      <!-- Main Hero Content -->
      <div class="relative z-10 w-full max-w-7xl mx-auto px-6 py-20">
        <div class="grid lg:grid-cols-12 gap-8 items-center min-h-[80vh]">
          <!-- Left Column - Hero Content (8 columns) -->
          <div class="lg:col-span-7 space-y-8">
            <!-- Status Badge -->
            <div class="animate-fade-in-up">
              <div class="inline-flex items-center gap-3 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 text-green-700 px-5 py-3 rounded-full shadow-sm hover:shadow-md transition-all duration-300 group cursor-pointer">
                <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <DuotoneIcon name="sparkles" color="green" size="sm" class="group-hover:scale-110 transition-transform" />
                <span class="font-semibold">{{ trialConfig.trialDurationDays }}-Day Free Trial • No Credit Card Required</span>
                <DuotoneIcon name="arrow-right" color="green" size="sm" class="group-hover:translate-x-1 transition-transform" />
              </div>
            </div>

            <!-- Main Headlines -->
            <div class="space-y-6 animate-fade-in-up delay-100">
              <h1 class="text-5xl lg:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight">
                <span class="block">Transform Your</span>
                <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 relative">
                  Business Today
                  <div class="absolute -bottom-2 left-0 w-full h-2 bg-gradient-to-r from-blue-400/30 via-purple-400/30 to-indigo-400/30 rounded-full"></div>
                </span>
        </h1>

              <p class="text-xl lg:text-2xl text-gray-600 leading-relaxed max-w-2xl">
                Choose from our <span class="font-semibold text-gray-800">powerful suite of business applications</span>.
                Start your digital transformation journey with our <span class="font-semibold text-green-600">risk-free trial</span>.
              </p>
            </div>

            <!-- Enhanced Value Propositions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 animate-fade-in-up delay-200">
              <div class="group bg-white/90 backdrop-blur-sm p-5 rounded-2xl border border-gray-200/50 shadow-sm hover:shadow-xl hover:border-green-300 transition-all duration-300 transform hover:-translate-y-1">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                    <DuotoneIcon name="shield-check" color="green" size="md" />
                  </div>
                  <div>
                    <div class="font-bold text-gray-900">Risk-Free Trial</div>
                    <div class="text-sm text-gray-600">{{ trialConfig.trialDurationDays }} days, cancel anytime</div>
                  </div>
      </div>
    </div>

              <div class="group bg-white/90 backdrop-blur-sm p-5 rounded-2xl border border-gray-200/50 shadow-sm hover:shadow-xl hover:border-blue-300 transition-all duration-300 transform hover:-translate-y-1">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-blue-100 to-cyan-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                    <DuotoneIcon name="rocket" color="blue" size="md" />
                  </div>
                  <div>
                    <div class="font-bold text-gray-900">Instant Setup</div>
                    <div class="text-sm text-gray-600">Ready in under 2 minutes</div>
                  </div>
                </div>
              </div>

              <div class="group bg-white/90 backdrop-blur-sm p-5 rounded-2xl border border-gray-200/50 shadow-sm hover:shadow-xl hover:border-purple-300 transition-all duration-300 transform hover:-translate-y-1">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                    <DuotoneIcon name="headset" color="purple" size="md" />
                  </div>
                  <div>
                    <div class="font-bold text-gray-900">Expert Support</div>
                    <div class="text-sm text-gray-600">24/7 live assistance</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Primary CTAs -->
            <div class="flex flex-col sm:flex-row gap-4 animate-fade-in-up delay-300">
              <Button
                size="lg"
                class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95 px-8 py-4 text-lg font-bold rounded-xl"
                @click="scrollToModules"
              >
                <DuotoneIcon name="play-circle" color="white" size="md" class="mr-3" />
                Explore Business Apps
                <DuotoneIcon name="arrow-down" color="white" size="sm" class="ml-2" />
              </Button>

              <Button
                variant="outline"
                size="lg"
                class="border-2 border-gray-300 hover:border-blue-600 hover:bg-blue-50 text-gray-700 hover:text-blue-700 transition-all duration-300 px-8 py-4 text-lg font-semibold rounded-xl"
              >
                <DuotoneIcon name="video" color="blue" size="md" class="mr-3" />
                Watch Demo
              </Button>
            </div>

            <!-- Trust Indicators -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-6 pt-8 animate-fade-in-up delay-400">
              <div class="flex items-center gap-2 text-sm text-gray-600">
                <DuotoneIcon name="users" color="gray" size="sm" />
                <span><strong class="text-gray-900">1,000+</strong> businesses trust ElyPOS</span>
              </div>

              <div class="flex items-center gap-2 text-sm text-gray-600">
                <div class="flex -space-x-2">
                  <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full border-2 border-white"></div>
                  <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-full border-2 border-white"></div>
                  <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full border-2 border-white"></div>
                  <div class="w-8 h-8 bg-gradient-to-br from-pink-400 to-pink-600 rounded-full border-2 border-white flex items-center justify-center text-xs font-bold text-white">
                    +
                  </div>
                </div>
                <span>Join thousands of happy customers</span>
              </div>
            </div>
          </div>

          <!-- Right Column - Interactive Stats & Visual Elements (4 columns) -->
          <div class="lg:col-span-5 relative">
            <div class="space-y-6 animate-fade-in-up delay-500">
              <!-- Featured Stats Cards -->
              <div class="grid grid-cols-2 gap-4">
                <!-- Modules Count -->
                <Card class="bg-white/95 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group">
                  <CardContent class="p-6 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                      <DuotoneIcon name="layers" color="blue" size="lg" />
                    </div>
                    <div class="text-3xl font-black text-blue-600 mb-2">
                      {{ moduleCategories.length > 0 ? moduleCategories.reduce((total, cat) => total + cat.modules.length, 0) : '13' }}+
                    </div>
                    <div class="text-sm font-semibold text-gray-700">Business Apps</div>
                    <div class="text-xs text-gray-500">Ready to use</div>
                  </CardContent>
                </Card>

                <!-- Trial Period -->
                <Card class="bg-white/95 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group">
                  <CardContent class="p-6 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                      <DuotoneIcon name="clock" color="green" size="lg" />
                    </div>
                    <div class="text-3xl font-black text-green-600 mb-2">{{ trialConfig.trialDurationDays }}</div>
                    <div class="text-sm font-semibold text-gray-700">Days Free</div>
                    <div class="text-xs text-gray-500">Full access</div>
                  </CardContent>
                </Card>

                <!-- Success Rate -->
                <Card class="bg-white/95 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group">
                  <CardContent class="p-6 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                      <DuotoneIcon name="chart-line" color="purple" size="lg" />
                    </div>
                    <div class="text-3xl font-black text-purple-600 mb-2">98%</div>
                    <div class="text-sm font-semibold text-gray-700">Success Rate</div>
                    <div class="text-xs text-gray-500">Customer satisfaction</div>
                  </CardContent>
                </Card>

                <!-- Support -->
                <Card class="bg-white/95 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1 group">
                  <CardContent class="p-6 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-200 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                      <DuotoneIcon name="heart" color="orange" size="lg" />
                    </div>
                    <div class="text-3xl font-black text-orange-600 mb-2">24/7</div>
                    <div class="text-sm font-semibold text-gray-700">Support</div>
                    <div class="text-xs text-gray-500">Always available</div>
                  </CardContent>
                </Card>
              </div>

              <!-- Feature Highlight Card -->
              <Card class="bg-gradient-to-br from-indigo-600 to-purple-700 border-0 shadow-2xl text-white overflow-hidden relative">
                <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>

                <CardContent class="p-8 relative z-10">
                  <div class="flex items-center gap-4 mb-4">
                    <div class="w-14 h-14 bg-white/20 rounded-2xl flex items-center justify-center">
                      <DuotoneIcon name="gift" color="white" size="lg" />
                    </div>
                    <div>
                      <h3 class="text-xl font-bold">Special Launch Offer</h3>
                      <p class="text-indigo-100 text-sm">Limited time opportunity</p>
                    </div>
                  </div>

                  <div class="space-y-3">
                    <div class="flex items-center gap-3 text-sm">
                      <DuotoneIcon name="check-circle" color="white" size="sm" />
                      <span>Extended {{ trialConfig.trialDurationDays }}-day trial period</span>
                    </div>
                    <div class="flex items-center gap-3 text-sm">
                      <DuotoneIcon name="check-circle" color="white" size="sm" />
                      <span>Priority customer support</span>
                    </div>
                    <div class="flex items-center gap-3 text-sm">
                      <DuotoneIcon name="check-circle" color="white" size="sm" />
                      <span>Free migration assistance</span>
                    </div>
                  </div>

                  <Button
                    variant="secondary"
                    class="w-full mt-6 bg-white text-indigo-700 hover:bg-indigo-50 font-semibold"
                    @click="scrollToModules"
                  >
                    <DuotoneIcon name="arrow-right" color="indigo" size="sm" class="mr-2" />
                    Claim Your Trial
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <!-- Scroll Indicator -->
      <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <Button
          variant="ghost"
          size="lg"
          @click="scrollToModules"
          class="rounded-full w-12 h-12 p-0 hover:bg-white/20 transition-all duration-300"
        >
          <DuotoneIcon name="chevron-down" color="gray" size="md" />
        </Button>
      </div>
    </section>

    <!-- Error Message -->
    <div v-if="errorMessage" class="max-w-7xl mx-auto px-6 mb-8">
      <Alert variant="destructive" class="shadow-lg border-red-200 bg-red-50">
        <DuotoneIcon name="warning" color="red" size="sm" />
        <AlertDescription class="text-red-800">{{ errorMessage }}</AlertDescription>
      </Alert>
    </div>

    <!-- Main Selection Section -->
    <section id="modules-section" class="py-16 bg-white/50">
      <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-16">
          <div class="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <DuotoneIcon name="layers" color="blue" size="sm" />
            <span>Business Applications</span>
          </div>
          <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Choose Your Business Apps
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Select the applications that match your business needs. Mix and match different modules to create your perfect business solution.
          </p>
        </div>

        <div class="grid lg:grid-cols-4 gap-8">
          <!-- Main Content Area -->
          <div class="lg:col-span-3">
          <!-- Loading State -->
            <div v-if="loadingModules" class="space-y-8">
              <div v-for="n in 3" :key="n" class="space-y-6">
                <div class="flex items-center gap-4">
                  <div class="w-12 h-12 bg-gray-200 rounded-xl animate-pulse"></div>
                  <div class="space-y-2">
                    <div class="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
                    <div class="h-4 bg-gray-100 rounded w-64 animate-pulse"></div>
                  </div>
                </div>
                <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div v-for="i in 3" :key="i" class="h-64 bg-gray-100 rounded-xl animate-pulse"></div>
              </div>
            </div>
          </div>

            <!-- Module Categories -->
            <div v-else class="space-y-12">
              <div v-for="(category, categoryIndex) in moduleCategories" :key="category.id">
              <!-- Category Header -->
                <div class="flex items-center gap-4 mb-8 animate-fade-in-up" :style="{ animationDelay: `${categoryIndex * 100}ms` }">
                  <div class="w-14 h-14 bg-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <DuotoneIcon :name="category.icon" color="white" size="lg" />
                  </div>
                  <div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-1">{{ category.name }}</h3>
                    <p class="text-gray-600">{{ category.description }}</p>
                  </div>
                  <div class="ml-auto bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                    {{ category.modules.length }} apps
                  </div>
                </div>

                <!-- Module Cards Grid -->
                <div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card
                    v-for="(module, moduleIndex) in category.modules"
                  :key="module.id"
                  :class="[
                      'group relative cursor-pointer transition-all duration-300 overflow-hidden border-2',
                      'animate-fade-in-up transform hover:scale-105 hover:shadow-2xl',
                    isModuleSelected(module.id)
                        ? 'border-blue-500 shadow-blue-500/25 shadow-xl bg-blue-50 ring-2 ring-blue-200'
                        : 'border-gray-200 hover:border-blue-300 bg-white hover:bg-blue-50/30'
                  ]"
                    :style="{ animationDelay: `${categoryIndex * 100 + moduleIndex * 50}ms` }"
                  @click="toggleModuleSelection(module.id)"
                >
                  <!-- Selection Indicator -->
                    <div v-if="isModuleSelected(module.id)" class="absolute top-4 right-4 z-10">
                      <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                        <DuotoneIcon name="check" color="white" size="sm" />
                    </div>
                  </div>

                    <!-- Status Badges -->
                    <div class="absolute top-4 left-4 z-10 space-y-2">
                      <div v-if="module.popular" class="inline-block">
                        <Badge class="bg-orange-500 text-white text-xs font-semibold shadow-lg border-0">
                          🔥 Popular
                        </Badge>
                  </div>
                      <div v-if="module.recommended" class="inline-block">
                        <Badge class="bg-green-500 text-white text-xs font-semibold shadow-lg border-0">
                          ⭐ Recommended
                        </Badge>
                      </div>
                  </div>

                    <CardHeader class="pb-4">
                      <!-- Module Icon & Info -->
                      <div class="flex items-start gap-4 mb-6">
                        <div :class="[
                          'w-14 h-14 rounded-xl shadow-lg flex items-center justify-center transform transition-transform group-hover:scale-110',
                          `bg-${module.color}-500`
                        ]">
                          <DuotoneIcon :name="module.icon" color="white" size="lg" />
                      </div>
                        <div class="flex-1 min-w-0">
                          <CardTitle class="text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                            {{ module.name }}
                          </CardTitle>
                          <CardDescription class="text-sm text-gray-600 leading-relaxed line-clamp-2">
                            {{ module.description }}
                          </CardDescription>
                      </div>
                      </div>

                      <!-- Pricing Section -->
                      <div class="flex items-center justify-between p-4 bg-gray-50 rounded-xl mb-4">
                        <div>
                          <div class="text-2xl font-bold text-gray-900">{{ formatPrice(module.price) }}</div>
                          <div class="text-sm text-gray-500">per month</div>
                    </div>
                    <div class="text-right">
                          <div class="text-lg font-bold text-green-600">FREE</div>
                          <div class="text-xs text-green-500">{{ trialConfig.trialDurationDays }}-day trial</div>
                    </div>
                      </div>

                      <!-- Enhanced POS Type Features -->
                      <div v-if="module.posType && posBusinessTypeConfig[module.posType]" class="space-y-3">
                        <!-- Core Features -->
                        <div class="bg-gray-50 rounded-lg p-3">
                          <div class="text-xs font-semibold text-gray-700 uppercase tracking-wide mb-2">Core Features</div>
                          <div class="space-y-1">
                            <div v-for="feature in posBusinessTypeConfig[module.posType]?.features.slice(0, 2)" :key="feature"
                                 class="flex items-center gap-2 text-sm text-gray-600">
                              <div :class="`w-1.5 h-1.5 rounded-full bg-${module.color}-500 flex-shrink-0`"></div>
                              <span class="truncate">{{ feature }}</span>
                    </div>
                          </div>
                        </div>

                        <!-- Online Features Highlight -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-3 border border-blue-200">
                          <div class="flex items-center gap-2 mb-2">
                            <DuotoneIcon name="globe" color="blue" size="sm" />
                            <div class="text-xs font-semibold text-blue-800 uppercase tracking-wide">Online Features</div>
                            <Badge variant="secondary" class="bg-blue-100 text-blue-700 border-blue-200 text-xs">NEW</Badge>
                          </div>
                          <div class="space-y-1">
                            <div v-for="feature in posBusinessTypeConfig[module.posType]?.onlineFeatures.slice(0, 2)" :key="feature"
                                 class="flex items-center gap-2 text-sm text-blue-800">
                              <DuotoneIcon name="check-circle" color="blue" size="sm" />
                              <span class="truncate font-medium">{{ feature }}</span>
                            </div>
                          </div>
                        </div>

                        <!-- Feature Count Summary -->
                        <div class="flex items-center justify-between text-xs">
                          <span class="text-gray-500">
                            {{ posBusinessTypeConfig[module.posType]?.features.length }} core +
                            {{ posBusinessTypeConfig[module.posType]?.onlineFeatures.length }} online features
                          </span>
                          <span class="text-blue-600 font-medium">
                            +{{ posBusinessTypeConfig[module.posType]?.specializations.length }} specializations
                          </span>
                        </div>
                      </div>

                      <!-- Regular Module Features (for non-POS modules) -->
                      <div v-else-if="module.features && module.features.length > 0" class="space-y-2">
                        <div class="text-xs font-semibold text-gray-700 uppercase tracking-wide">Key Features</div>
                        <div class="space-y-1">
                          <div v-for="feature in module.features.slice(0, 2)" :key="feature"
                               class="flex items-center gap-2 text-sm text-gray-600">
                            <DuotoneIcon name="check" color="green" size="sm" class="flex-shrink-0" />
                            <span class="truncate">{{ feature }}</span>
                          </div>
                          <div v-if="module.features.length > 2" class="text-xs text-blue-600 font-medium">
                            +{{ module.features.length - 2 }} more features
                          </div>
                        </div>
                      </div>
                    </CardHeader>

                    <!-- Selection Action -->
                    <CardContent class="pt-0">
                      <Button
                        :variant="isModuleSelected(module.id) ? 'default' : 'outline'"
                        class="w-full transition-all duration-200"
                        :class="isModuleSelected(module.id) ? 'bg-blue-600 hover:bg-blue-700 text-white' : 'border-blue-200 hover:bg-blue-50'"
                      >
                        <DuotoneIcon
                          :name="isModuleSelected(module.id) ? 'check-circle' : 'plus-circle'"
                          :color="isModuleSelected(module.id) ? 'white' : 'blue'"
                          size="sm"
                          class="mr-2"
                        />
                        {{ isModuleSelected(module.id) ? 'Selected' : 'Select App' }}
                      </Button>
                  </CardContent>

                    <!-- Hover Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-t from-blue-900/80 via-blue-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-6 pointer-events-none">
                      <div class="text-white">
                        <div class="font-semibold mb-1">{{ module.fullName }}</div>
                        <div class="text-sm opacity-90">Click to add to your selection</div>
                      </div>
                    </div>
                </Card>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-if="!loadingModules && moduleCategories.length === 0" class="text-center py-20">
              <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <DuotoneIcon name="layers" color="muted" size="xl" />
            </div>
              <h3 class="text-2xl font-semibold text-gray-900 mb-3">No modules available</h3>
              <p class="text-gray-500 mb-6 max-w-md mx-auto">We're working on bringing you amazing business tools. Please try again in a moment.</p>
              <Button variant="outline" @click="loadAvailableModules" class="bg-white">
                <DuotoneIcon name="refresh" color="primary" size="sm" class="mr-2" />
                Retry Loading
              </Button>
          </div>
        </div>

          <!-- Enhanced Selection Sidebar -->
          <div class="lg:col-span-1">
            <div class="sticky top-6 space-y-6">
              <!-- Quick Action Bar (Mobile/Tablet) -->
              <div class="lg:hidden bg-white rounded-2xl p-4 shadow-lg border border-gray-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                      <span class="text-white font-bold text-sm">{{ selectedModulesCount }}</span>
                    </div>
                    <div>
                      <div class="font-semibold text-gray-900">{{ selectedModulesCount }} Selected</div>
                      <div class="text-sm text-gray-500">{{ formatPrice(totalMonthlyPrice) }}/month value</div>
                    </div>
                  </div>
                  <Button
                    v-if="selectedModulesCount > 0"
                    class="bg-blue-600 hover:bg-blue-700 text-white"
                    @click="handleContinue"
                    :disabled="isLoading"
                  >
                    <DuotoneIcon name="rocket" color="white" size="sm" class="mr-2" />
                    Start Trial
                  </Button>
                </div>
              </div>

              <!-- Main Selection Card -->
              <Card class="shadow-2xl bg-white border-gray-200 overflow-hidden">
                <!-- Dynamic Header -->
                <CardHeader :class="[
                  'transition-all duration-300',
                  selectedModulesCount > 0
                    ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white'
                    : 'bg-gray-50 text-gray-700'
                ]">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                      <div :class="[
                        'w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300',
                        selectedModulesCount > 0
                          ? 'bg-white/20 text-white animate-pulse'
                          : 'bg-gray-200 text-gray-600'
                      ]">
                        {{ selectedModulesCount }}
                      </div>
                      <div>
                        <CardTitle class="text-lg font-semibold">Your Selection</CardTitle>
                        <p :class="[
                          'text-sm mt-1 transition-colors duration-300',
                          selectedModulesCount > 0 ? 'text-blue-100' : 'text-gray-500'
                        ]">
                          {{ selectedModulesCount === 0 ? 'Choose your business apps' : `${selectedModulesCount} app${selectedModulesCount === 1 ? '' : 's'} ready to try` }}
                        </p>
                      </div>
                    </div>

                    <!-- Progress Indicator -->
                    <div v-if="selectedModulesCount > 0" class="text-right">
                      <div class="text-xs text-blue-100 mb-1">Ready to start</div>
                      <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                        <DuotoneIcon name="check" color="white" size="sm" />
                      </div>
                    </div>
                  </div>
            </CardHeader>

                <CardContent class="p-0">
                  <!-- Loading State -->
                  <div v-if="loadingModules" class="p-6 space-y-4">
                    <div class="flex items-center gap-3">
                      <div class="w-12 h-12 bg-gray-200 rounded-xl animate-pulse"></div>
                      <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 rounded animate-pulse"></div>
                        <div class="h-3 bg-gray-100 rounded animate-pulse w-2/3"></div>
                      </div>
                    </div>
                    <div class="h-16 bg-gray-100 rounded-lg animate-pulse"></div>
              </div>

              <!-- Selected Modules List -->
                  <div v-else-if="selectedModulesCount > 0" class="max-h-64 overflow-y-auto custom-scrollbar">
                    <!-- Compact Summary Header -->
                    <div class="p-4 bg-blue-50 border-b border-blue-100">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                          <DuotoneIcon name="check-circle" color="blue" size="sm" />
                          <span class="text-sm font-semibold text-blue-800">{{ selectedModulesCount }} Selected</span>
                        </div>
                        <div class="text-right">
                          <div class="text-sm font-bold text-blue-800">{{ formatPrice(totalMonthlyPrice) }}</div>
                          <div class="text-xs text-blue-600">total value</div>
                        </div>
                      </div>
                    </div>

                    <!-- Compact Module List -->
                    <div class="p-3 space-y-2">
                <div
                        v-for="(module, index) in selectedModuleDetails"
                  :key="module.id"
                        class="group relative bg-white hover:bg-blue-50 rounded-lg p-3 border border-gray-100 hover:border-blue-200 transition-all duration-200"
                        :style="{ animationDelay: `${index * 50}ms` }"
                      >
                        <div class="flex items-center gap-3">
                          <!-- Compact Icon -->
                          <div :class="[
                            'w-8 h-8 rounded-lg shadow-sm flex items-center justify-center flex-shrink-0',
                            `bg-${module.color}-500`
                          ]">
                            <DuotoneIcon :name="module.icon" color="white" size="sm" />
                  </div>

                          <!-- Module Info -->
                  <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                              <div class="flex-1">
                                <h4 class="font-medium text-gray-900 text-sm truncate">{{ module.name }}</h4>
                                <div class="flex items-center gap-2 mt-1">
                                  <span class="text-xs text-gray-500">{{ module.category }}</span>
                                  <span class="text-xs text-green-600 font-medium">FREE {{ trialConfig.trialDurationDays }}d</span>
                  </div>
                              </div>

                              <!-- Compact Pricing -->
                              <div class="text-right ml-2">
                                <div class="text-sm font-semibold text-gray-900">{{ formatPrice(module.price) }}</div>
                                <div class="text-xs text-gray-500">/month</div>
                              </div>

                              <!-- Compact Remove Button -->
                              <Button
                                variant="ghost"
                                size="sm"
                                @click.stop="removeSelectedModule(module.id)"
                                class="opacity-0 group-hover:opacity-100 text-gray-400 hover:text-red-500 hover:bg-red-50 transition-all duration-200 h-6 w-6 p-0 ml-2 flex-shrink-0"
                              >
                                <DuotoneIcon name="x" size="sm" />
                              </Button>
                            </div>
                          </div>
                        </div>

                        <!-- Expandable Features (only for first 2 modules, or on hover) -->
                        <div
                          v-if="(index < 2 || expandedFeatures.has(module.id)) && module.features && module.features.length > 0"
                          class="mt-2 pt-2 border-t border-gray-100"
                        >
                          <div class="flex flex-wrap gap-1">
                            <span
                              v-for="feature in module.features.slice(0, 3)"
                              :key="feature"
                              class="inline-flex items-center gap-1 text-xs bg-gray-100 text-gray-600 rounded-md px-2 py-1"
                            >
                              <DuotoneIcon name="check" color="green" size="sm" />
                              {{ feature.split(' ').slice(0, 3).join(' ') }}
                            </span>
                  <button
                              v-if="module.features.length > 3"
                              @click="toggleFeatures(module.id)"
                              class="text-xs text-blue-600 hover:text-blue-800 px-2 py-1"
                  >
                              {{ expandedFeatures.has(module.id) ? 'Less' : `+${module.features.length - 3} more` }}
                  </button>
                          </div>
                        </div>
                </div>
              </div>

                    <!-- Show More/Less for Large Lists -->
                    <div v-if="selectedModulesCount > 5" class="p-3 border-t border-gray-200 bg-gray-50">
                      <Button
                        variant="ghost"
                        size="sm"
                        class="w-full text-sm text-gray-600 hover:text-blue-600"
                        @click="toggleCompactView"
                      >
                        <DuotoneIcon :name="compactView ? 'eye' : 'eye-slash'" color="gray" size="sm" class="mr-2" />
                        {{ compactView ? 'Show Details' : 'Compact View' }}
                      </Button>
                    </div>
              </div>

                  <!-- Enhanced Empty State -->
                  <div v-else class="p-8 text-center">
                    <div class="relative">
                      <!-- Animated Background -->
                      <div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl transform rotate-3 opacity-50"></div>
                      <div class="absolute inset-0 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl transform -rotate-2 opacity-30"></div>

                      <!-- Content -->
                      <div class="relative">
                        <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl flex items-center justify-center mx-auto mb-4 transform hover:scale-110 transition-transform duration-300">
                          <DuotoneIcon name="layers" color="blue" size="xl" />
                  </div>
                        <h3 class="font-bold text-gray-900 mb-2">Ready to Get Started?</h3>
                        <p class="text-sm text-gray-600 mb-4 leading-relaxed">
                          Select business apps above to see them here.<br/>
                          All apps include a <strong>{{ trialConfig.trialDurationDays }}-day free trial</strong>.
                        </p>

                        <!-- Quick Selection Suggestions -->
                        <div class="space-y-2">
                          <div class="text-xs font-semibold text-gray-700 uppercase tracking-wide">Popular Combinations</div>
                          <div class="flex flex-wrap gap-2 justify-center">
                            <span class="inline-flex items-center gap-1 text-xs bg-blue-100 text-blue-700 rounded-full px-3 py-1">
                              <DuotoneIcon name="chart-line" color="blue" size="sm" />
                              ERP + Accounting
                            </span>
                            <span class="inline-flex items-center gap-1 text-xs bg-green-100 text-green-700 rounded-full px-3 py-1">
                              <DuotoneIcon name="cash-register" color="green" size="sm" />
                              POS + Accounting
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Pricing & Trial Section -->
                  <div v-if="selectedModulesCount > 0 && !loadingModules" class="border-t border-gray-200">
                    <!-- Summary Stats -->
                    <div class="p-4 bg-gray-50">
                      <div class="grid grid-cols-2 gap-4">
                        <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                          <div class="text-lg font-bold text-gray-900">{{ formatPrice(totalMonthlyPrice) }}</div>
                          <div class="text-xs text-gray-500">Monthly Value</div>
                    </div>
                        <div class="text-center p-3 bg-green-50 rounded-lg border border-green-200">
                          <div class="text-lg font-bold text-green-600">FREE</div>
                          <div class="text-xs text-green-600">For {{ trialConfig.trialDurationDays }} Days</div>
                        </div>
                      </div>
                  </div>

                    <!-- Trial Benefits -->
                    <div class="p-4">
                      <div class="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-200">
                        <div class="flex items-center gap-3 mb-3">
                          <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
                            <DuotoneIcon name="gift" color="white" size="sm" />
                          </div>
                          <div>
                            <div class="font-bold text-green-800">{{ trialConfig.trialDurationDays }}-Day Free Trial</div>
                            <div class="text-xs text-green-600">Full access, no commitments</div>
                          </div>
                        </div>

                        <div class="grid grid-cols-1 gap-2">
                          <div class="flex items-center gap-2 text-sm text-green-700">
                            <DuotoneIcon name="check" color="green" size="sm" />
                            <span>No credit card required</span>
                          </div>
                          <div class="flex items-center gap-2 text-sm text-green-700">
                            <DuotoneIcon name="check" color="green" size="sm" />
                            <span>Cancel anytime during trial</span>
                          </div>
                          <div class="flex items-center gap-2 text-sm text-green-700">
                            <DuotoneIcon name="check" color="green" size="sm" />
                            <span>Full feature access included</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Enhanced CTA Section -->
                    <div class="p-4 pt-0">
                  <Button
                        class="w-full py-4 text-lg font-bold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98]"
                    :disabled="isLoading || selectedModulesCount === 0 || loadingModules"
                    @click="handleContinue"
                  >
                        <div class="flex items-center justify-center gap-2">
                          <DuotoneIcon
                            v-if="isLoading"
                            name="loading"
                            color="white"
                            size="sm"
                            class="animate-spin"
                          />
                          <DuotoneIcon
                            v-else
                            name="rocket"
                            color="white"
                            size="sm"
                          />

                          <span v-if="isLoading">
                      Starting Trials...
                          </span>
                          <span v-else-if="!isAuthenticated">
                            Start {{ selectedModulesCount }} Free Trial{{ selectedModulesCount === 1 ? '' : 's' }}
                          </span>
                          <span v-else>
                            Launch Selected Apps
                          </span>
                        </div>
                  </Button>

                      <!-- Trust Indicators -->
                      <div class="mt-4 text-center">
                        <div class="flex items-center justify-center gap-4 text-xs text-gray-500">
                          <div class="flex items-center gap-1">
                            <DuotoneIcon name="shield-check" color="green" size="sm" />
                            <span>Secure</span>
                          </div>
                          <div class="flex items-center gap-1">
                            <DuotoneIcon name="clock" color="blue" size="sm" />
                            <span>Instant Setup</span>
                          </div>
                          <div class="flex items-center gap-1">
                            <DuotoneIcon name="headset" color="purple" size="sm" />
                            <span>24/7 Support</span>
                          </div>
                        </div>

                        <p class="text-xs text-gray-500 mt-3 leading-relaxed">
                          <span v-if="!isAuthenticated">
                            By continuing, you'll create a free account and start your trials instantly
                          </span>
                          <span v-else>
                            Your trials will begin immediately with full access to all features
                          </span>
                  </p>
                      </div>
                </div>
              </div>

                  <!-- Loading CTA -->
                  <div v-if="loadingModules" class="p-4 border-t border-gray-200">
                    <Button class="w-full py-4 bg-gray-100 text-gray-500" disabled>
                      <DuotoneIcon name="loading" color="muted" size="sm" class="mr-2 animate-spin" />
                      Loading Available Apps...
                </Button>
              </div>
            </CardContent>
          </Card>

              <!-- Enhanced Help Card -->
              <Card class="bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 border-blue-200 shadow-lg overflow-hidden">
                <CardContent class="p-0">
                  <!-- Decorative Background -->
                  <div class="relative">
                    <div class="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full transform translate-x-8 -translate-y-8"></div>
                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-br from-indigo-400/20 to-pink-400/20 rounded-full transform -translate-x-4 translate-y-4"></div>

                    <div class="relative p-6 text-center">
                      <div class="w-14 h-14 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg transform hover:scale-110 transition-transform duration-300">
                        <DuotoneIcon name="headset" color="white" size="lg" />
                      </div>

                      <h3 class="font-bold text-blue-900 mb-2">Need Guidance?</h3>
                      <p class="text-sm text-blue-700 mb-4 leading-relaxed">
                        Our experts can help you choose the perfect apps for your business type and goals.
                </p>

                      <div class="space-y-3">
                        <Button variant="outline" size="sm" class="w-full border-blue-300 text-blue-700 hover:bg-blue-100 font-semibold">
                          <DuotoneIcon name="phone" color="blue" size="sm" class="mr-2" />
                          Schedule Free Consultation
                </Button>

                        <div class="flex items-center justify-center gap-4 text-xs text-blue-600">
                          <div class="flex items-center gap-1">
                            <DuotoneIcon name="clock" color="blue" size="sm" />
                            <span>15-min call</span>
                          </div>
                          <div class="flex items-center gap-1">
                            <DuotoneIcon name="star" color="blue" size="sm" />
                            <span>Expert advice</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
              </CardContent>
            </Card>

              <!-- Progress Indicator (Desktop) -->
              <div v-if="selectedModulesCount > 0" class="hidden lg:block">
                <Card class="bg-gradient-to-r from-blue-600 to-blue-700 text-white border-0 shadow-lg">
                  <CardContent class="p-4">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                          <DuotoneIcon name="check" color="white" size="sm" />
          </div>
                        <div>
                          <div class="font-semibold">Almost Ready!</div>
                          <div class="text-xs text-blue-100">{{ selectedModulesCount }} app{{ selectedModulesCount === 1 ? '' : 's' }} selected</div>
        </div>
      </div>
                      <div class="text-right">
                        <div class="text-sm font-semibold">{{ formatPrice(totalMonthlyPrice) }}</div>
                        <div class="text-xs text-blue-100">total value</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Active Modules Section (shown only for authenticated users with active subscriptions) -->
    <section v-if="isAuthenticated && hasActiveModules" id="active-modules-section" class="py-16 bg-white border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-6">
        <!-- Section Header -->
        <div class="text-center mb-12">
          <div class="inline-flex items-center gap-2 bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <DuotoneIcon name="check-circle" color="green" size="sm" />
            <span>Your Active Modules</span>
          </div>
          <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Ready to Use Business Apps
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Access your subscribed modules and continue building your business with our powerful tools.
          </p>
        </div>

        <!-- Active Modules Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <!-- Loading State -->
          <template v-if="loadingSubscriptions">
            <div v-for="i in 3" :key="i" class="bg-gray-100 rounded-2xl p-6 animate-pulse">
              <div class="flex items-center gap-4 mb-4">
                <div class="w-16 h-16 bg-gray-200 rounded-xl"></div>
                <div class="flex-1">
                  <div class="h-5 bg-gray-200 rounded mb-2"></div>
                  <div class="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
              <div class="h-12 bg-gray-200 rounded-lg"></div>
            </div>
          </template>

          <!-- Active Module Cards -->
          <div
            v-for="subscription in activeSubscriptions"
            :key="subscription._id"
            class="group bg-white rounded-2xl p-6 border border-gray-200 shadow-sm hover:shadow-xl hover:border-blue-300 transition-all duration-300 transform hover:-translate-y-1"
          >
            <!-- Module Header -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center gap-4">
                <div :class="[
                  'w-16 h-16 rounded-xl shadow-lg flex items-center justify-center group-hover:scale-105 transition-transform',
                  `bg-${getModuleColor(subscription)}-500`
                ]">
                  <DuotoneIcon :name="getModuleIcon(subscription)" color="white" size="lg" />
                </div>
                <div>
                  <h3 class="text-lg font-bold text-gray-900 group-hover:text-blue-700 transition-colors">
                    {{ getModuleDisplayName(subscription) }}
                  </h3>
                  <div class="flex items-center gap-2 mt-1">
                    <!-- Trial Badge -->
                    <Badge v-if="subscription.isTrialMode" variant="secondary" class="bg-orange-100 text-orange-700 border-orange-200">
                      <DuotoneIcon name="clock" color="orange" size="sm" class="mr-1" />
                      Trial
                    </Badge>
                    <!-- Paid Badge -->
                    <Badge v-else variant="secondary" class="bg-green-100 text-green-700 border-green-200">
                      <DuotoneIcon name="check-circle" color="green" size="sm" class="mr-1" />
                      Active
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <!-- Module Status -->
            <div class="space-y-3 mb-6">
              <!-- Trial Information -->
              <div v-if="subscription.isTrialMode && subscription.trialEndDate" class="bg-orange-50 rounded-lg p-3 border border-orange-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2">
                    <DuotoneIcon name="clock" color="orange" size="sm" />
                    <span class="text-sm font-medium text-orange-800">Trial Period</span>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-orange-700">{{ getDaysRemaining(subscription.trialEndDate) }}</div>
                    <div class="text-xs text-orange-600">days remaining</div>
                  </div>
                </div>
              </div>

              <!-- Paid Subscription Information -->
              <div v-else class="bg-green-50 rounded-lg p-3 border border-green-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-2">
                    <DuotoneIcon name="credit-card" color="green" size="sm" />
                    <span class="text-sm font-medium text-green-800">Monthly Subscription</span>
                  </div>
                  <div class="text-right">
                    <div class="text-lg font-bold text-green-700">{{ formatPrice(subscription.price) }}</div>
                    <div class="text-xs text-green-600">per month</div>
                  </div>
                </div>
              </div>

              <!-- Next Billing Date -->
              <div v-if="subscription.nextBillingDate" class="flex items-center gap-2 text-sm text-gray-600">
                <DuotoneIcon name="calendar" color="gray" size="sm" />
                <span>
                  {{ subscription.isTrialMode ? 'Trial ends:' : 'Next billing:' }}
                  {{ new Date(subscription.nextBillingDate).toLocaleDateString() }}
                </span>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
              <!-- Primary Access Button -->
              <Button
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 shadow-md hover:shadow-lg transition-all duration-300"
                @click="accessModule(subscription)"
              >
                <DuotoneIcon name="external-link" color="white" size="sm" class="mr-2" />
                Access {{ subscription.module === 'POS' ? 'POS System' : subscription.module }}
              </Button>

              <!-- Secondary Actions -->
              <div class="flex gap-2">
                <!-- Convert Trial Button -->
                <Button
                  v-if="subscription.isTrialMode"
                  variant="outline"
                  size="sm"
                  class="flex-1 border-green-300 text-green-700 hover:bg-green-50"
                  @click="router.push(`/dashboard/billing?upgrade=${subscription.module}`)"
                >
                  <DuotoneIcon name="upgrade" color="green" size="sm" class="mr-1" />
                  Upgrade
                </Button>

                <!-- Settings Button -->
                <Button
                  variant="outline"
                  size="sm"
                  class="flex-1"
                  @click="router.push(`/dashboard/settings?module=${subscription.module}`)"
                >
                  <DuotoneIcon name="settings" color="gray" size="sm" class="mr-1" />
                  Settings
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div v-if="!loadingSubscriptions" class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center p-6 bg-blue-50 rounded-2xl border border-blue-200">
            <div class="text-3xl font-bold text-blue-600 mb-2">{{ activeModulesCount }}</div>
            <div class="text-sm font-medium text-blue-800">Active Modules</div>
          </div>
          <div class="text-center p-6 bg-orange-50 rounded-2xl border border-orange-200">
            <div class="text-3xl font-bold text-orange-600 mb-2">{{ trialSubscriptions.length }}</div>
            <div class="text-sm font-medium text-orange-800">Free Trials</div>
          </div>
          <div class="text-center p-6 bg-green-50 rounded-2xl border border-green-200">
            <div class="text-3xl font-bold text-green-600 mb-2">{{ paidSubscriptions.length }}</div>
            <div class="text-sm font-medium text-green-800">Paid Subscriptions</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20 bg-gray-900">
      <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-16">
          <div class="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <DuotoneIcon name="star" color="blue" size="sm" />
            <span>Why Choose ElyPOS</span>
          </div>
          <h2 class="text-3xl lg:text-4xl font-bold text-white mb-4">
            Built for Modern Businesses
          </h2>
          <p class="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
            Powerful features and seamless integration designed to scale with your business
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="group text-center p-8 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105">
            <div class="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <DuotoneIcon name="cloud" color="white" size="lg" />
            </div>
            <h3 class="text-xl font-bold text-white mb-3">Cloud-Based Platform</h3>
            <p class="text-gray-300 leading-relaxed">Access your business data anywhere, anytime with our secure, scalable cloud infrastructure.</p>
          </div>

          <div class="group text-center p-8 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105">
            <div class="w-16 h-16 bg-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <DuotoneIcon name="bolt" color="white" size="lg" />
            </div>
            <h3 class="text-xl font-bold text-white mb-3">Lightning Fast Performance</h3>
            <p class="text-gray-300 leading-relaxed">Optimized for speed and efficiency to ensure your business operations run smoothly.</p>
          </div>

          <div class="group text-center p-8 rounded-2xl bg-white/5 backdrop-blur-sm border border-white/10 hover:bg-white/10 transition-all duration-300 hover:scale-105">
            <div class="w-16 h-16 bg-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
              <DuotoneIcon name="shield-check" color="white" size="lg" />
            </div>
            <h3 class="text-xl font-bold text-white mb-3">Enterprise Security</h3>
            <p class="text-gray-300 leading-relaxed">Bank-level security protocols keep your business data safe and compliant.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Already Active Modules Information -->
    <div v-if="isAuthenticated && excludedModules.length > 0" class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-6">
        <div class="text-center mb-8">
          <div class="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            <DuotoneIcon name="info" color="blue" size="sm" />
            <span>Already Active</span>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-3">
            Your Current Subscriptions
          </h3>
          <p class="text-gray-600 max-w-2xl mx-auto">
            The following modules are already active in your account and are not shown in the selection above.
            You can access them directly from the "Your Active Modules" section.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div
            v-for="excluded in excludedModules"
            :key="excluded.id"
            class="bg-gray-50 rounded-xl p-4 border border-gray-200"
          >
            <div class="flex items-center gap-3">
              <div :class="[
                'w-10 h-10 rounded-lg flex items-center justify-center',
                `bg-${getModuleColor(excluded.subscription)}-500`
              ]">
                <DuotoneIcon :name="getModuleIcon(excluded.subscription)" color="white" size="sm" />
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-gray-900 text-sm">{{ excluded.name }}</h4>
                <div class="flex items-center gap-2 mt-1">
                  <Badge v-if="excluded.isTrialMode" variant="secondary" class="bg-orange-100 text-orange-700 border-orange-200 text-xs">
                    Trial
                  </Badge>
                  <Badge v-else variant="secondary" class="bg-green-100 text-green-700 border-green-200 text-xs">
                    Active
                  </Badge>
                  <span v-if="excluded.isTrialMode && excluded.trialEndDate" class="text-xs text-gray-500">
                    {{ getDaysRemaining(excluded.trialEndDate) }}d left
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center mt-6">
          <Button
            variant="outline"
            @click="scrollToActiveModules"
            class="mx-auto"
          >
            <DuotoneIcon name="arrow-up" color="blue" size="sm" class="mr-2" />
            View Active Modules
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(2rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-20px) rotate(1deg);
  }
  50% {
    transform: translateY(-10px) rotate(-1deg);
  }
  75% {
    transform: translateY(-30px) rotate(0.5deg);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  25% {
    transform: translateY(-30px) rotate(-1deg);
  }
  50% {
    transform: translateY(-5px) rotate(1deg);
  }
  75% {
    transform: translateY(-20px) rotate(-0.5deg);
  }
}

@keyframes float-slow {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  33% {
    transform: translateY(-15px) rotate(0.5deg);
  }
  66% {
    transform: translateY(-25px) rotate(-0.5deg);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite;
  animation-delay: 2s;
}

.animate-float-slow {
  animation: float-slow 10s ease-in-out infinite;
  animation-delay: 4s;
}

.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Grid pattern background */
.bg-grid-slate-100\/50 {
  background-image:
    linear-gradient(to right, rgba(148, 163, 184, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(148, 163, 184, 0.1) 1px, transparent 1px);
}

.bg-grid-16 {
  background-size: 16px 16px;
}

/* Gradient text animation */
.bg-clip-text {
  background-clip: text;
  -webkit-background-clip: text;
}

/* Enhanced hover effects */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.group:hover .group-hover\:translate-x-1 {
  transform: translateX(0.25rem);
}

/* Card shadow enhancements */
.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-2xl {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.hover\:shadow-2xl:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Backdrop blur fallback */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Custom scrollbar for smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Enhanced button animations */
.transform {
  transform: translateZ(0);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.active\:scale-95:active {
  transform: scale(0.95);
}

/* Improved focus states */
.focus\:ring-2:focus {
  ring-width: 2px;
  ring-color: rgba(59, 130, 246, 0.5);
}

/* Text gradient animation */
.text-transparent {
  color: transparent;
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* Custom border gradients */
.border-gradient {
  border: 1px solid;
  border-image: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(168, 85, 247, 0.3)) 1;
}

/* Enhanced spacing for mobile */
@media (max-width: 768px) {
  .min-h-\[80vh\] {
    min-height: 70vh;
  }

  .text-5xl {
    font-size: 2.5rem;
    line-height: 1;
  }

  .text-7xl {
    font-size: 3.5rem;
    line-height: 1;
  }
}

/* Custom scrollbar for selected modules */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}
</style>
