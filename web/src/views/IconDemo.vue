<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import DuotoneIcon from '../components/DuotoneIcon.vue'
import { iconNames, iconCategories, copyToClipboard } from '@/lib/utils'

// UI state
const searchTerm = ref('')
const copyFeedback = ref('')
const selectedSize = ref<'sm' | 'md' | 'lg' | 'xl'>('lg')
const selectedColor = ref<'primary' | 'secondary' | 'muted'>('primary')
const selectedCategory = ref('all')

// Filter icons based on search and category
const filteredIcons = computed(() => {
  const icons =
    selectedCategory.value === 'all'
      ? iconNames
      : iconCategories[selectedCategory.value as keyof typeof iconCategories]

  return icons.filter((name) => name.toLowerCase().includes(searchTerm.value.toLowerCase()))
})

// Copy component code to clipboard
async function copyIconComponent(iconName: string) {
  const componentCode = `<DuotoneIcon name="${iconName}" size="${selectedSize.value}" color="${selectedColor.value}" />`

  try {
    await copyToClipboard(componentCode)
    copyFeedback.value = `Copied: ${iconName}`
    setTimeout(() => (copyFeedback.value = ''), 2000)
  } catch (err) {
    console.error('Failed to copy: ', err)
    copyFeedback.value = 'Failed to copy'
    setTimeout(() => (copyFeedback.value = ''), 2000)
  }
}

// Copy icon name to clipboard
async function copyIconName(iconName: string) {
  try {
    await copyToClipboard(iconName)
    copyFeedback.value = `Copied name: ${iconName}`
    setTimeout(() => (copyFeedback.value = ''), 2000)
  } catch (err) {
    console.error('Failed to copy name: ', err)
  }
}

// Keyboard shortcuts
function handleKeydown(event: KeyboardEvent) {
  // Ctrl/Cmd + K to focus search
  if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
    event.preventDefault()
    const searchInput = document.querySelector('input[type="text"]') as HTMLInputElement
    searchInput?.focus()
  }
  // Escape to clear search
  if (event.key === 'Escape') {
    searchTerm.value = ''
  }
}

// Register keyboard listeners
onMounted(() => {
  window.addEventListener('keydown', handleKeydown)
  return () => {
    window.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-background to-muted">
    <div class="container mx-auto px-4 py-20">
      <!-- Header -->
      <div class="text-center mb-16">
        <h1 class="text-4xl md:text-6xl font-bold tracking-tight mb-6">KeeIcons Duotone Font</h1>
        <p class="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
          Complete collection of beautiful duotone icons. Search, preview, and copy code for fast
          development.
        </p>

        <!-- Search and Controls -->
        <div class="max-w-4xl mx-auto space-y-6">
          <!-- Search Bar -->
          <div class="relative">
            <DuotoneIcon
              name="search"
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
              size="sm"
            />
            <input
              type="text"
              placeholder="Search icons... (e.g. arrow, user, heart)"
              v-model="searchTerm"
              class="w-full pl-10 text-center text-lg py-3 rounded-md border border-input bg-transparent"
            />
          </div>

          <!-- Copy Settings -->
          <div class="flex flex-wrap items-center justify-center gap-4 p-4 bg-muted/50 rounded-lg">
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium">Category:</span>
              <select v-model="selectedCategory" class="px-3 py-1 rounded border bg-background">
                <option value="all">All Icons</option>
                <option value="arrows">Arrows & Navigation</option>
                <option value="ui">UI & Controls</option>
                <option value="files">Files & Documents</option>
                <option value="communication">Communication</option>
                <option value="social">Social Media</option>
                <option value="business">Business</option>
                <option value="technology">Technology</option>
                <option value="media">Media</option>
              </select>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium">Size:</span>
              <select v-model="selectedSize" class="px-3 py-1 rounded border bg-background">
                <option value="sm">Small</option>
                <option value="md">Medium</option>
                <option value="lg">Large</option>
                <option value="xl">X-Large</option>
              </select>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-sm font-medium">Color:</span>
              <select v-model="selectedColor" class="px-3 py-1 rounded border bg-background">
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
                <option value="muted">Muted</option>
              </select>
            </div>
            <div
              v-if="copyFeedback"
              class="animate-pulse bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm flex items-center"
            >
              <DuotoneIcon name="check" size="sm" class="mr-1" />
              {{ copyFeedback }}
            </div>
          </div>

          <!-- Stats -->
          <div class="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
            <span
              >Showing <strong>{{ filteredIcons.length }}</strong> of
              <strong>{{ iconNames.length }}</strong> icons</span
            >
            <span>•</span>
            <span>Click icon to copy component</span>
            <span>•</span>
            <span><kbd class="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+K</kbd> to search</span>
            <span>•</span>
            <span><kbd class="px-1 py-0.5 bg-muted rounded text-xs">Esc</kbd> to clear</span>
          </div>
        </div>
      </div>

      <!-- Quick Start Guide -->
      <div class="grid md:grid-cols-4 gap-6 mb-16">
        <div class="bg-card text-card-foreground rounded-lg border shadow-sm">
          <div class="flex flex-col space-y-1.5 p-6">
            <h3 class="text-lg font-semibold flex items-center gap-2">
              <DuotoneIcon name="search" size="lg" color="primary" />
              1. Search
            </h3>
          </div>
          <div class="p-6 pt-0">
            <p class="text-sm text-muted-foreground mb-3">Type keywords to find the perfect icon</p>
            <div class="flex justify-center">
              <DuotoneIcon name="magnifier" size="lg" color="primary" />
            </div>
          </div>
        </div>

        <div class="bg-card text-card-foreground rounded-lg border shadow-sm">
          <div class="flex flex-col space-y-1.5 p-6">
            <h3 class="text-lg font-semibold flex items-center gap-2">
              <DuotoneIcon name="setting" size="md" color="primary" />
              2. Customize
            </h3>
          </div>
          <div class="p-6 pt-0">
            <p class="text-sm text-muted-foreground mb-3">Adjust size and color settings above</p>
            <div class="flex justify-center">
              <DuotoneIcon name="color-swatch" size="lg" color="secondary" />
            </div>
          </div>
        </div>

        <div class="bg-card text-card-foreground rounded-lg border shadow-sm">
          <div class="flex flex-col space-y-1.5 p-6">
            <h3 class="text-lg font-semibold flex items-center gap-2">
              <DuotoneIcon name="copy" size="md" color="primary" />
              3. Copy Code
            </h3>
          </div>
          <div class="p-6 pt-0">
            <p class="text-sm text-muted-foreground mb-3">Click any icon to copy component code</p>
            <div class="flex justify-center">
              <DuotoneIcon name="clipboard" size="lg" color="muted" />
            </div>
          </div>
        </div>

        <div class="bg-card text-card-foreground rounded-lg border shadow-sm">
          <div class="flex flex-col space-y-1.5 p-6">
            <h3 class="text-lg font-semibold flex items-center gap-2">
              <DuotoneIcon name="rocket" size="md" color="primary" />
              4. Use It!
            </h3>
          </div>
          <div class="p-6 pt-0">
            <p class="text-sm text-muted-foreground mb-3">Paste in your Vue component</p>
            <div class="flex justify-center">
              <DuotoneIcon name="check-circle" size="lg" color="primary" />
            </div>
          </div>
        </div>
      </div>

      <!-- Icons Grid -->
      <div
        class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4"
      >
        <div
          v-for="iconName in filteredIcons"
          :key="iconName"
          class="bg-card text-card-foreground rounded-lg border shadow-sm hover:shadow-lg transition-all hover:scale-105 cursor-pointer group relative overflow-hidden"
        >
          <div class="p-4 text-center">
            <!-- Icon Display -->
            <div
              class="mb-3 flex justify-center relative"
              role="button"
              tabindex="0"
              @click="copyIconComponent(iconName)"
              @keydown.enter="copyIconComponent(iconName)"
              title="Click to copy component code"
            >
              <DuotoneIcon
                :name="iconName"
                :size="selectedSize"
                :color="selectedColor"
                class="group-hover:scale-110 transition-transform"
              />
              <!-- Copy overlay -->
              <div
                class="absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center"
              >
                <DuotoneIcon name="copy" size="lg" class="text-primary" />
              </div>
            </div>

            <!-- Icon Name -->
            <div class="space-y-2">
              <button
                class="text-xs text-muted-foreground font-mono break-all hover:text-foreground transition-colors cursor-pointer bg-transparent border-0 p-0"
                @click.stop="copyIconName(iconName)"
                title="Click to copy icon name"
              >
                {{ iconName }}
              </button>

              <!-- Quick Action Buttons -->
              <div
                class="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity justify-center"
              >
                <button
                  class="h-6 px-2 text-xs rounded-md inline-flex items-center justify-center whitespace-nowrap transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground"
                  @click.stop="copyIconComponent(iconName)"
                  title="Copy component"
                >
                  <DuotoneIcon name="copy" size="sm" class="mr-1" />
                  Copy
                </button>
                <button
                  class="h-6 px-2 text-xs rounded-md inline-flex items-center justify-center whitespace-nowrap font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground"
                  @click.stop="copyIconName(iconName)"
                  title="Copy name only"
                >
                  <DuotoneIcon name="tag" size="sm" class="mr-1" />
                  Name
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="filteredIcons.length === 0" class="text-center py-16">
        <DuotoneIcon name="search" size="xl" color="muted" class="mb-4" />
        <p class="text-muted-foreground">No icons found matching "{{ searchTerm }}"</p>
      </div>

      <!-- Footer -->
      <div class="text-center mt-16 pt-8 border-t">
        <p class="text-muted-foreground mb-4">KeeIcons Duotone Font integrated into Vue</p>
        <div class="flex justify-center gap-4">
          <router-link to="/" class="text-primary hover:underline">← Back to Home</router-link>
        </div>
      </div>
    </div>
  </div>
</template>
