<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div class="max-w-md w-full">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <!-- Success Icon -->
        <div class="w-20 h-20 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
          <DuotoneIcon name="check-circle" class="w-12 h-12 text-green-600 dark:text-green-400" />
        </div>

        <!-- Success Message -->
        <h1 class="text-2xl font-bold text-green-900 dark:text-green-100 mb-2">
          {{ $t('payment.success.title') }}
        </h1>
        <p class="text-green-700 dark:text-green-300 mb-6">
          {{ $t('payment.success.description') }}
        </p>

        <!-- Payment Details -->
        <div v-if="paymentDetails" class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg mb-6">
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-green-700 dark:text-green-300">{{ $t('payment.paymentId') }}:</span>
              <span class="font-mono text-green-900 dark:text-green-100">{{ paymentDetails.paymentId }}</span>
            </div>
            <div v-if="paymentDetails.transactionId" class="flex justify-between">
              <span class="text-green-700 dark:text-green-300">{{ $t('payment.transactionId') }}:</span>
              <span class="font-mono text-green-900 dark:text-green-100">{{ paymentDetails.transactionId }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-green-700 dark:text-green-300">{{ $t('payment.amount') }}:</span>
              <span class="font-medium text-green-900 dark:text-green-100">
                {{ formatCurrency(paymentDetails.amount, paymentDetails.currency) }}
              </span>
            </div>
            <div v-if="paymentDetails.paidAt" class="flex justify-between">
              <span class="text-green-700 dark:text-green-300">{{ $t('payment.paidAt') }}:</span>
              <span class="text-green-900 dark:text-green-100">{{ formatDate(paymentDetails.paidAt) }}</span>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div v-else-if="loading" class="mb-6">
          <div class="animate-spin w-8 h-8 border-4 border-green-600 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p class="text-gray-600 dark:text-gray-400">{{ $t('payment.verifying') }}</p>
        </div>

        <!-- Actions -->
        <div class="space-y-3">
          <Button
            @click="redirectToDashboard"
            class="w-full"
          >
            <DuotoneIcon name="arrow-left" class="w-4 h-4 mr-2" />
            {{ $t('payment.backToDashboard') }}
          </Button>

          <Button
            @click="viewPaymentHistory"
            variant="outline"
            class="w-full"
          >
            <DuotoneIcon name="receipt" class="w-4 h-4 mr-2" />
            {{ $t('payment.viewHistory') }}
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { paymentService } from '@/services/payment.service'
import { Button } from '@/components/ui/button'
import DuotoneIcon from '@/components/DuotoneIcon.vue'
import type { Currency } from '@/types/payment'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()

// State
const loading = ref(true)
const paymentDetails = ref<{
  paymentId: string
  status: string
  amount: number
  currency: Currency
  paidAt?: Date
  transactionId?: string
  bankReference?: string
} | null>(null)

// Methods
const formatCurrency = (amount: number, currency: Currency = 'USD') => {
  return paymentService.formatCurrency(amount, currency)
}

const formatDate = (date?: Date | string) => {
  if (!date) return ''
  return new Intl.DateTimeFormat('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

const redirectToDashboard = () => {
  router.push('/dashboard')
}

const viewPaymentHistory = () => {
  router.push('/dashboard/payment-history')
}

// Load payment details on mount
onMounted(async () => {
  const paymentId = route.query.paymentId as string

  if (!paymentId) {
    // No payment ID, redirect to dashboard
    setTimeout(() => redirectToDashboard(), 3000)
    return
  }

  try {
    // Clear any stored payment session
    paymentService.clearPaymentSession()

    // Check payment status
    const status = await paymentService.checkPaymentStatus(paymentId)
    paymentDetails.value = {
      ...status,
      currency: status.currency as Currency
    }

    // If payment is still pending, poll for updates
    if (status.status === 'pending') {
      paymentService.startStatusPolling(paymentId, (updatedStatus) => {
        paymentDetails.value = updatedStatus
      })
    }
  } catch (error) {
    console.error('Failed to load payment details:', error)
    // Redirect to dashboard on error
    setTimeout(() => redirectToDashboard(), 3000)
  } finally {
    loading.value = false
  }
})
</script>

<style scoped>
/* Custom styles if needed */
</style>
