<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { companyService } from '../../services/company.service'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { Separator } from '../../components/ui/separator'
import { Badge } from '../../components/ui/badge'
import DuotoneIcon from '../../components/DuotoneIcon.vue'
import type { PosBusinessType } from '../../types/company'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

// Form fields
const firstName = ref('')
const lastName = ref('')
const companyName = ref('')
const email = ref('')
const password = ref('')
const confirmPassword = ref('')

// Module selection from URL
const selectedModules = ref<string[]>([])
const selectedPosTypes = ref<Array<{ moduleId: string; posType: PosBusinessType }>>([])
const moduleDetails = ref<any[]>([])

// Form state
const isLoading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')

// Parse URL parameters for module selection
const parseSelectedModules = async () => {
  const moduleParams = route.query.modules as string
  const posTypeParams = route.query.posTypes as string

  if (moduleParams) {
    selectedModules.value = moduleParams.split(',').filter(Boolean)
  }

  if (posTypeParams) {
    const posTypePairs = posTypeParams.split(',').filter(Boolean)
    selectedPosTypes.value = posTypePairs.map(pair => {
      const [moduleId, posType] = pair.split(':')
      return { moduleId, posType: posType as PosBusinessType }
    })
  }

  // Load module details for display
  if (selectedModules.value.length > 0) {
    try {
      const availableModules = await companyService.getAvailableModules()

      moduleDetails.value = selectedModules.value.map(moduleId => {
        if (moduleId.startsWith('POS_')) {
          const posType = selectedPosTypes.value.find(p => p.moduleId === moduleId)?.posType
          const posPricing = availableModules.posTypes.find((p: any) => p.type === posType)

          return {
            id: moduleId,
            name: `${posType} POS`,
            price: posPricing?.price || 0,
            icon: 'cash-register',
            posType
          }
        } else {
          const modulePricing = availableModules.modules.find((m: any) => m.name === moduleId)
          return {
            id: moduleId,
            name: moduleId,
            price: modulePricing?.price || 0,
            icon: moduleId === 'ERP' ? 'chart-line' : moduleId === 'LOAN' ? 'bank' : 'calculator'
          }
        }
      })
    } catch (error) {
      console.warn('Failed to load module details:', error)
    }
  }
}

// Form validation
const isFormValid = computed(() => {
  return (
    firstName.value.trim() &&
    lastName.value.trim() &&
    companyName.value.trim() &&
    email.value.trim() &&
    password.value.length >= 6 &&
    password.value === confirmPassword.value.trim()
  )
})

const passwordsMatch = computed(() => {
  return !confirmPassword.value || password.value === confirmPassword.value
})

const isEmailValid = computed(() => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return !email.value || emailRegex.test(email.value)
})

const totalMonthlyPrice = computed(() => {
  return moduleDetails.value.reduce((sum, module) => sum + module.price, 0)
})

const formatPrice = (price: number, currency: string = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(price)
}

// Auto-generate user with timestamp
const generateTestUser = () => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')
  const dateStr = timestamp[0]
  const timeStr = timestamp[1].split('.')[0]

  firstName.value = 'Test'
  lastName.value = 'User'
  companyName.value = `Test Company ${dateStr} ${timeStr.replace(/-/g, ':')}`
  email.value = `testuser_${dateStr}_${timeStr}@example.com`
  password.value = 'password123'
  confirmPassword.value = 'password123'

  successMessage.value = `Auto-generated test user and company data for ${dateStr} ${timeStr.replace(/-/g, ':')}`
  errorMessage.value = ''
}

const handleSubmit = async () => {
  if (!isFormValid.value) {
    errorMessage.value = 'Please fill in all required fields correctly'
    return
  }

  isLoading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    // Register user and company
    await authStore.register({
      firstName: firstName.value.trim(),
      lastName: lastName.value.trim(),
      companyName: companyName.value.trim(),
      email: email.value.trim(),
      password: password.value,
    })

    successMessage.value = 'Account created successfully!'

    // Start trials for selected modules if any
    if (selectedModules.value.length > 0 && authStore.currentUser?.currentCompanyId) {
      successMessage.value += ' Starting free trials for selected modules...'

      try {
        const trialPromises = moduleDetails.value.map(async (module) => {
          if (module.posType) {
            return await companyService.startPOSTrial(authStore.currentUser!.currentCompanyId!, {
              posType: module.posType as PosBusinessType
            })
          } else {
            return await companyService.startTrial(authStore.currentUser!.currentCompanyId!, {
              module: module.id as 'ERP' | 'LOAN' | 'ACCOUNTING'
            })
          }
        })

        await Promise.all(trialPromises)
        successMessage.value = `Account created and ${moduleDetails.value.length} free trials started! Redirecting to dashboard...`
      } catch (trialError) {
        console.warn('Failed to start some trials:', trialError)
        successMessage.value = 'Account created successfully! Some trials may need to be started manually.'
      }
    } else {
      successMessage.value += ' Redirecting to dashboard...'
    }

    setTimeout(() => {
      const moduleParams = selectedModules.value.join(',')
      const modulesUrl = moduleParams ? `/dashboard/modules?modules=${moduleParams}&trial=started` : '/dashboard/modules'
      router.push(modulesUrl)
    }, 2000)
  } catch (error) {
    console.error('Registration error:', error)
    errorMessage.value = error instanceof Error ? error.message : 'Registration failed. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const clearForm = () => {
  firstName.value = ''
  lastName.value = ''
  companyName.value = ''
  email.value = ''
  password.value = ''
  confirmPassword.value = ''
  errorMessage.value = ''
  successMessage.value = ''
}

onMounted(() => {
  parseSelectedModules()
})
</script>

<template>
  <div class="flex min-h-[calc(100vh-4rem)] items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-lg space-y-8">
      <!-- Header -->
      <div class="text-center">
        <h2 class="text-3xl font-bold tracking-tight text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Or
          <RouterLink to="/auth/login" class="font-medium text-[#1C1C1C] hover:text-black">
            sign in to your existing account
          </RouterLink>
        </p>
      </div>

      <!-- Selected Modules Preview -->
      <Card v-if="moduleDetails.length > 0" class="border-blue-200 bg-blue-50">
        <CardHeader class="pb-3">
          <CardTitle class="flex items-center gap-2 text-blue-900">
            <DuotoneIcon name="check-circle" class="w-5 h-5" />
            Selected Apps for Your Business
          </CardTitle>
          <CardDescription class="text-blue-700">
            These apps will automatically start free trials after account creation
          </CardDescription>
        </CardHeader>
        <CardContent class="pt-0">
          <div class="space-y-3">
            <!-- Module List -->
            <div class="space-y-2">
              <div
                v-for="module in moduleDetails"
                :key="module.id"
                class="flex items-center justify-between p-3 bg-white rounded-lg border border-blue-200"
              >
                <div class="flex items-center gap-3">
                  <div class="p-2 rounded-lg bg-blue-500">
                    <DuotoneIcon :name="module.icon" class="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div class="font-medium text-gray-900">{{ module.name }}</div>
                    <div class="text-xs text-gray-500">Free 3-day trial</div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="font-semibold text-gray-900">{{ formatPrice(module.price) }}</div>
                  <div class="text-xs text-gray-500">/month after trial</div>
                </div>
              </div>
            </div>

            <!-- Total Price -->
            <div class="border-t border-blue-200 pt-3">
              <div class="flex justify-between items-center">
                <span class="font-medium text-blue-900">Total after trial</span>
                <span class="text-lg font-bold text-blue-900">{{ formatPrice(totalMonthlyPrice) }}/month</span>
              </div>
              <p class="text-xs text-blue-600 mt-1">
                Cancel anytime during trial • No credit card required
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Auto-generate User Card -->
      <Card class="border-dashed border-2 border-gray-300">
        <CardHeader class="pb-3">
          <CardTitle class="flex items-center gap-2 text-sm font-medium">
            <DuotoneIcon name="rocket" class="w-4 h-4" />
            Quick Test Registration
          </CardTitle>
          <CardDescription class="text-xs">
            Generate test user data with timestamp for development/testing
          </CardDescription>
        </CardHeader>
        <CardContent class="pt-0">
          <div class="flex gap-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              @click="generateTestUser"
              class="flex-1"
            >
              <DuotoneIcon name="magic-wand" class="w-3 h-3 mr-1" />
              Auto-Generate User
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              @click="clearForm"
              class="px-3"
            >
              <DuotoneIcon name="trash" class="w-3 h-3" />
            </Button>
          </div>
        </CardContent>
      </Card>

      <!-- Success Message -->
      <Alert v-if="successMessage" class="border-green-200 bg-green-50">
        <DuotoneIcon name="check-circle" class="w-4 h-4 text-green-600" />
        <AlertDescription class="text-green-800">
          {{ successMessage }}
        </AlertDescription>
      </Alert>

      <!-- Error Message -->
      <Alert v-if="errorMessage" variant="destructive">
        <DuotoneIcon name="warning" class="w-4 h-4" />
        <AlertDescription>
          {{ errorMessage }}
        </AlertDescription>
      </Alert>

      <!-- Registration Form -->
      <Card>
        <CardHeader>
          <CardTitle>Account Information</CardTitle>
          <CardDescription>
            Fill in your details to create a new account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form class="space-y-4" @submit.prevent="handleSubmit">
                        <!-- Name Fields -->
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <Input
                  id="firstName"
                  v-model="firstName"
                  type="text"
                  required
                  placeholder="John"
                  :disabled="isLoading"
                />
              </div>

              <div>
                <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <Input
                  id="lastName"
                  v-model="lastName"
                  type="text"
                  required
                  placeholder="Doe"
                  :disabled="isLoading"
                />
              </div>
            </div>

            <!-- Company Name -->
            <div>
              <label for="companyName" class="block text-sm font-medium text-gray-700 mb-1">
                Company Name *
              </label>
              <Input
                id="companyName"
                v-model="companyName"
                type="text"
                required
                placeholder="Your Company Name"
                :disabled="isLoading"
              />
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                Email Address *
              </label>
              <Input
                id="email"
                v-model="email"
                type="email"
                required
                placeholder="<EMAIL>"
                :class="{ 'border-red-500': email && !isEmailValid }"
                :disabled="isLoading"
              />
              <p v-if="email && !isEmailValid" class="text-sm text-red-500 mt-1">
                Please enter a valid email address
              </p>
            </div>

            <Separator />

            <!-- Password Fields -->
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                Password *
              </label>
              <Input
                id="password"
                v-model="password"
                type="password"
                required
                placeholder="Create a secure password"
                :disabled="isLoading"
              />
              <p v-if="password && password.length < 6" class="text-sm text-red-500 mt-1">
                Password must be at least 6 characters long
              </p>
            </div>

            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">
                Confirm Password *
              </label>
              <Input
                id="confirmPassword"
                v-model="confirmPassword"
                type="password"
                required
                placeholder="Confirm your password"
                :class="{ 'border-red-500': confirmPassword && !passwordsMatch }"
                :disabled="isLoading"
              />
              <p v-if="confirmPassword && !passwordsMatch" class="text-sm text-red-500 mt-1">
                Passwords do not match
              </p>
            </div>

            <!-- Submit Button -->
            <Button
              type="submit"
              class="w-full"
              :disabled="!isFormValid || isLoading"
            >
              <span v-if="isLoading" class="mr-2">
                <svg class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
              {{ isLoading ? 'Creating Account...' : 'Create Account' }}
            </Button>

            <!-- Form Status -->
            <div class="flex justify-center">
              <Badge v-if="!isFormValid" variant="secondary" class="text-xs">
                Please complete all required fields
              </Badge>
              <Badge v-else variant="default" class="text-xs bg-green-600">
                Ready to create account
              </Badge>
            </div>
          </form>
        </CardContent>
      </Card>

      <!-- Login Link -->
      <div class="text-center">
        <p class="text-sm text-gray-600">
          Already have an account?
          <RouterLink
            to="/auth/login"
            class="font-medium text-[#1C1C1C] hover:text-black underline"
          >
            Sign in here
          </RouterLink>
        </p>
      </div>
    </div>
  </div>
</template>
