import { apiClient } from '../lib/api-client';

export interface RestaurantTable {
  _id: string;
  companyId: string;
  tableNumber: string;
  floorId?: string;
  floorName?: string;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'cleaning' | 'maintenance';
  position: {
    x: number;
    y: number;
  };
  shape: 'square' | 'round' | 'rectangle';
  size: 'small' | 'medium' | 'large';
  isVipTable: boolean;
  currentOrderId?: string;
  reservationInfo?: {
    customerName: string;
    customerPhone: string;
    reservationTime: Date;
    partySize: number;
    notes?: string;
  };
  lastCleanedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface QRMenu {
  _id: string;
  companyId: string;
  tableId: string;
  tableNumber: string;
  menuType: 'standard' | 'bbq_unlimited' | 'special_event';
  qrCode: string;
  isActive: boolean;
  sessionId?: string;
  activeOrder?: {
    orderId: string;
    startTime: Date;
    endTime?: Date;
    totalAmount: number;
    itemCount: number;
  };
  bbqMode?: {
    isUnlimitedMode: boolean;
    timeLimit: number;
    pricePerPerson: number;
    maxOrdersPerSession: number;
    allowedCategories: string[];
  };
  customization: {
    theme: 'light' | 'dark' | 'cambodian';
    language: 'en' | 'km';
    showPrices: boolean;
    showImages: boolean;
    allowSpecialRequests: boolean;
  };
  analytics: {
    totalScans: number;
    uniqueCustomers: number;
    averageOrderValue: number;
    lastScannedAt?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface RestaurantOrder {
  _id: string;
  companyId: string;
  qrMenuId: string;
  tableId: string;
  sessionId: string;
  orderType: 'standard' | 'bbq_unlimited';
  customerInfo?: {
    name?: string;
    phone?: string;
    email?: string;
    specialRequests?: string;
  };
  items: Array<{
    productId: string;
    name: string;
    quantity: number;
    price: number;
    notes?: string;
    isUnlimitedItem?: boolean;
  }>;
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'cancelled';
  bbqSession?: {
    startTime: Date;
    endTime: Date;
    partySize: number;
    timeRemaining: number;
    orderCount: number;
  };
  totalAmount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface RestaurantFloor {
  _id: string;
  companyId: string;
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  displayOrder: number;
  layout?: {
    width: number;
    height: number;
    backgroundImage?: string;
  };
  settings?: {
    gridSize: number;
    snapToGrid: boolean;
    showGridLines: boolean;
  };
  tableCount?: number;
  createdAt: Date;
  updatedAt: Date;
}

interface ApiResponseData<T> {
  success: boolean;
  data: T;
  message?: string;
}

class RestaurantService {
  private readonly baseUrl = '/api/pos/restaurant';

  // Tables
  async getTables(companyId: string, floorId?: string): Promise<RestaurantTable[]> {
    const url = floorId
      ? `${this.baseUrl}/tables?companyId=${companyId}&floorId=${floorId}`
      : `${this.baseUrl}/tables?companyId=${companyId}`;

    const response = await apiClient.get<ApiResponseData<RestaurantTable[]>>(url);
    const tables = response.data.data || [];
    // Filter out any invalid table entries
    return tables.filter(table => table && table._id && table.tableNumber && table.status);
  }

  async createTable(companyId: string, tableData: Partial<RestaurantTable>): Promise<RestaurantTable> {
    const response = await apiClient.post<ApiResponseData<RestaurantTable>>(`${this.baseUrl}/tables`, {
      companyId,
      ...tableData
    });
    return response.data.data;
  }

  async updateTableStatus(companyId: string, tableId: string, status: RestaurantTable['status']): Promise<RestaurantTable> {
    const response = await apiClient.patch<ApiResponseData<RestaurantTable>>(`${this.baseUrl}/tables/${tableId}/status`, {
      companyId,
      status
    });
    return response.data.data;
  }

  async updateTablePosition(companyId: string, tableId: string, position: { x: number; y: number }): Promise<RestaurantTable> {
    const response = await apiClient.patch<ApiResponseData<RestaurantTable>>(`${this.baseUrl}/tables/${tableId}/position`, {
      companyId,
      position
    });
    return response.data.data;
  }

  async updateMultipleTablePositions(companyId: string, updates: Array<{ tableId: string; position: { x: number; y: number } }>): Promise<boolean> {
    const response = await apiClient.patch<ApiResponseData<{ message: string }>>(`${this.baseUrl}/tables/positions/bulk`, {
      companyId,
      updates
    });
    return response.data.success;
  }

  async reserveTable(companyId: string, tableId: string, reservationInfo: RestaurantTable['reservationInfo']): Promise<RestaurantTable> {
    const response = await apiClient.post<ApiResponseData<RestaurantTable>>(`${this.baseUrl}/tables/${tableId}/reserve`, {
      companyId,
      reservationInfo
    });
    return response.data.data;
  }

  async initializeDefaultTables(companyId: string): Promise<RestaurantTable[]> {
    const response = await apiClient.post<ApiResponseData<RestaurantTable[]>>(`${this.baseUrl}/tables/initialize-defaults`, {
      companyId
    });
    return response.data.data || [];
  }

  async checkTableNumberAvailability(companyId: string, tableNumber: string): Promise<boolean> {
    const response = await apiClient.get<ApiResponseData<{ tableNumber: string; isAvailable: boolean }>>(`${this.baseUrl}/tables/check-availability/${encodeURIComponent(tableNumber)}?companyId=${companyId}`);
    return response.data.data?.isAvailable || false;
  }

  async getNextTableNumber(companyId: string): Promise<string> {
    const response = await apiClient.get<ApiResponseData<{ nextTableNumber: string }>>(`${this.baseUrl}/tables/next-number?companyId=${companyId}`);
    return response.data.data?.nextTableNumber || '1';
  }

  async moveTableToFloor(companyId: string, tableId: string, targetFloorId: string): Promise<RestaurantTable> {
    const response = await apiClient.patch<ApiResponseData<RestaurantTable>>(`${this.baseUrl}/tables/${tableId}/move-floor`, {
      companyId,
      targetFloorId
    });
    return response.data.data;
  }

  async deleteTable(companyId: string, tableId: string): Promise<boolean> {
    const response = await apiClient.delete<ApiResponseData<{ message: string }>>(`${this.baseUrl}/tables/${tableId}?companyId=${companyId}`);
    return response.data.success;
  }

  // QR Menus
  async createQRMenu(companyId: string, tableId: string, tableNumber: string, menuType: QRMenu['menuType']): Promise<QRMenu> {
    const response = await apiClient.post<ApiResponseData<QRMenu>>(`${this.baseUrl}/qr-menu`, {
      companyId,
      tableId,
      tableNumber,
      menuType
    });
    return response.data.data;
  }

  async getQRMenuByTable(companyId: string, tableId: string): Promise<QRMenu | null> {
    try {
      const response = await apiClient.get<ApiResponseData<QRMenu>>(`${this.baseUrl}/qr-menu/table/${tableId}?companyId=${companyId}`);
      return response.data.data;
    } catch (error) {
      return null;
    }
  }

  async scanQRMenu(companyId: string, qrMenuId: string): Promise<QRMenu> {
    const response = await apiClient.post<ApiResponseData<QRMenu>>(`${this.baseUrl}/qr-menu/${qrMenuId}/scan`, {
      companyId
    });
    return response.data.data;
  }

  // BBQ Sessions
  async startBBQSession(companyId: string, qrMenuId: string, partySize: number): Promise<RestaurantOrder> {
    const response = await apiClient.post<ApiResponseData<RestaurantOrder>>(`${this.baseUrl}/qr-menu/${qrMenuId}/bbq/start`, {
      companyId,
      partySize
    });
    return response.data.data;
  }

  async addBBQOrder(companyId: string, sessionId: string, items: any[]): Promise<RestaurantOrder> {
    const response = await apiClient.post<ApiResponseData<RestaurantOrder>>(`${this.baseUrl}/qr-menu/bbq/${sessionId}/order`, {
      companyId,
      items
    });
    return response.data.data;
  }

  // Orders
  async createOrder(companyId: string, qrMenuId: string, items: any[]): Promise<RestaurantOrder> {
    const response = await apiClient.post<ApiResponseData<RestaurantOrder>>(`${this.baseUrl}/qr-menu/${qrMenuId}/order`, {
      companyId,
      items
    });
    return response.data.data;
  }

  async getTableOrders(companyId: string, tableId: string, activeOnly: boolean = false): Promise<RestaurantOrder[]> {
    const response = await apiClient.get<ApiResponseData<RestaurantOrder[]>>(`${this.baseUrl}/orders/table/${tableId}?companyId=${companyId}&activeOnly=${activeOnly}`);
    const orders = response.data.data || [];
    // Filter out any invalid order entries
    return orders.filter(order => order && order._id && order.status);
  }

  async updateOrderStatus(companyId: string, orderId: string, status: RestaurantOrder['status']): Promise<RestaurantOrder> {
    const response = await apiClient.patch<ApiResponseData<RestaurantOrder>>(`${this.baseUrl}/orders/${orderId}/status`, {
      companyId,
      status
    });
    return response.data.data;
  }

  // Categories
  async initializeKhmerCategories(companyId: string): Promise<void> {
    await apiClient.post<ApiResponseData<void>>(`${this.baseUrl}/qr-menu/khmer-categories/initialize`, {
      companyId
    });
  }

  // Analytics
  async getRestaurantAnalytics(companyId: string, timeframe: 'today' | 'week' | 'month' = 'today'): Promise<any> {
    const response = await apiClient.get<ApiResponseData<any>>(`${this.baseUrl}/analytics?companyId=${companyId}&timeframe=${timeframe}`);
    return response.data.data;
  }

  // Health check
  async checkHealth(): Promise<boolean> {
    try {
      const response = await apiClient.get<ApiResponseData<any>>(`${this.baseUrl}/health`);
      return response.data.success === true;
    } catch (error) {
      return false;
    }
  }

  // Floor Management
  async getFloors(companyId: string): Promise<RestaurantFloor[]> {
    const response = await apiClient.get<ApiResponseData<RestaurantFloor[]>>(`${this.baseUrl}/floors?companyId=${companyId}`);
    return response.data.data || [];
  }

  async getFloorById(companyId: string, floorId: string): Promise<RestaurantFloor> {
    const response = await apiClient.get<ApiResponseData<RestaurantFloor>>(`${this.baseUrl}/floors/${floorId}?companyId=${companyId}`);
    return response.data.data;
  }

  async createFloor(companyId: string, floorData: Partial<RestaurantFloor>): Promise<RestaurantFloor> {
    const response = await apiClient.post<ApiResponseData<RestaurantFloor>>(`${this.baseUrl}/floors`, {
      companyId,
      ...floorData
    });
    return response.data.data;
  }

  async updateFloor(companyId: string, floorId: string, updateData: Partial<RestaurantFloor>): Promise<RestaurantFloor> {
    const response = await apiClient.put<ApiResponseData<RestaurantFloor>>(`${this.baseUrl}/floors/${floorId}`, {
      companyId,
      ...updateData
    });
    return response.data.data;
  }

  async deleteFloor(companyId: string, floorId: string): Promise<boolean> {
    const response = await apiClient.delete<ApiResponseData<{ message: string }>>(`${this.baseUrl}/floors/${floorId}?companyId=${companyId}`);
    return response.data.success;
  }

  async reorderFloors(companyId: string, floorOrders: Array<{ floorId: string; displayOrder: number }>): Promise<boolean> {
    const response = await apiClient.patch<ApiResponseData<{ message: string }>>(`${this.baseUrl}/floors/reorder`, {
      companyId,
      floorOrders
    });
    return response.data.success;
  }
}

export const restaurantService = new RestaurantService();
