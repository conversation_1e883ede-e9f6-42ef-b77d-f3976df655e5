import apiClient from '@/lib/api-client'
import type {
  AuthResponse,
  LoginRequest,
  RegisterRequest,
  RefreshTokenRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  ChangePasswordRequest,
  UpdateProfileRequest,
  User
} from '@/types/auth'

// Backend response wrapper interface
interface BackendResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

interface BackendAuthData {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export class AuthService {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<BackendResponse<BackendAuthData>>('/auth/login', credentials)

      // Transform backend response to frontend format
      return {
        user: response.data.data.user,
        tokens: {
          accessToken: response.data.data.accessToken,
          refreshToken: response.data.data.refreshToken,
          expiresIn: 3600 // Default 1 hour, backend should provide this
        }
      }
    } catch (error) {
      // Always treat login failures as authentication errors for better UX
      console.warn('Lo<PERSON> failed:', error instanceof Error ? error.message : 'Unknown error')
      throw new Error('Invalid email or password')
    }
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<BackendResponse<BackendAuthData>>('/auth/register', userData)

    // Transform backend response to frontend format
    return {
      user: response.data.data.user,
      tokens: {
        accessToken: response.data.data.accessToken,
        refreshToken: response.data.data.refreshToken,
        expiresIn: 3600 // Default 1 hour, backend should provide this
      }
    }
  }

  async refreshToken(refreshTokenData: RefreshTokenRequest): Promise<AuthResponse> {
    const response = await apiClient.post<BackendResponse<BackendAuthData>>('/auth/refresh', refreshTokenData)

    // Transform backend response to frontend format
    return {
      user: response.data.data.user,
      tokens: {
        accessToken: response.data.data.accessToken,
        refreshToken: response.data.data.refreshToken,
        expiresIn: 3600 // Default 1 hour, backend should provide this
      }
    }
  }

  async logout(token?: string): Promise<void> {
    if (token) {
      await apiClient.post('/auth/logout', { token })
    }
  }

  async forgotPassword(email: ForgotPasswordRequest): Promise<{ message: string }> {
    const response = await apiClient.post<BackendResponse<{ message: string }>>('/auth/forgot-password', email)
    return { message: response.data.message }
  }

  async resetPassword(resetData: ResetPasswordRequest): Promise<{ message: string }> {
    const response = await apiClient.post<BackendResponse<{ message: string }>>('/auth/reset-password', resetData)
    return { message: response.data.message }
  }

  async changePassword(passwordData: ChangePasswordRequest): Promise<{ message: string }> {
    const response = await apiClient.post<BackendResponse<{ message: string }>>('/auth/change-password', passwordData)
    return { message: response.data.message }
  }

  async validateToken(token: string): Promise<{ valid: boolean; user?: User }> {
    const response = await apiClient.get<BackendResponse<{ valid: boolean; user?: User }>>(`/auth/validate/${token}`)
    return response.data.data
  }

  async getUserProfile(userId: string): Promise<User> {
    const response = await apiClient.get<BackendResponse<User>>(`/auth/users/${userId}`)
    return response.data.data
  }

  async updateProfile(userId: string, profileData: UpdateProfileRequest): Promise<User> {
    const response = await apiClient.patch<BackendResponse<User>>(`/auth/users/${userId}`, profileData)
    return response.data.data
  }

  async switchCompany(userId: string, companyId: string): Promise<{ message: string; user: User }> {
    const response = await apiClient.post<BackendResponse<{ message: string; user: User }>>('/auth/switch-company', { userId, companyId })
    return response.data.data
  }

  async getUserCompanies(userId: string): Promise<any[]> {
    const response = await apiClient.get<BackendResponse<any[]>>(`/auth/users/${userId}/companies`)
    return response.data.data
  }
}

export const authService = new AuthService()
