import { apiClient } from '@/lib/api-client'
import type {
  PaymentRequest,
  PaymentResponse,
  PaymentHistory,
  PaymentSummary,
  PaymentAnalytics,
  PaymentMethod,
  Currency
} from '@/types/payment'

export interface CreatePaymentRequest {
  amount: number
  currency: Currency
  description: string
  companyId: string
  module: string
  paymentMethod: PaymentMethod
  customerInfo: {
    name: string
    email: string
    phone?: string
  }
  subscriptionId?: string
  trialConversion?: boolean
  userId?: string
}

export interface PaymentHistoryQuery {
  page?: number
  limit?: number
  status?: string
  module?: string
  dateFrom?: string
  dateTo?: string
}

export interface PaymentHistoryResponse {
  payments: PaymentHistory[]
  totalCount: number
  totalPages: number
  currentPage: number
}

export class PaymentService {
  async createPayment(request: CreatePaymentRequest): Promise<PaymentResponse> {
    console.log('Creating payment:', request)

    const payload = {
      ...request,
      userAgent: navigator.userAgent,
      ipAddress: await this.getClientIP()
    }

    const response = await apiClient.post<{ success: boolean; data: PaymentResponse }>('/payments', payload)

    if (!response.data.success) {
      throw new Error('Failed to create payment')
    }

    return response.data.data
  }

  async getPayment(paymentId: string): Promise<PaymentHistory> {
    console.log('Getting payment:', paymentId)

    const response = await apiClient.get<{ success: boolean; data: PaymentHistory }>(`/payments/${paymentId}`)

    if (!response.data.success) {
      throw new Error('Failed to get payment')
    }

    return response.data.data
  }

  async getPaymentHistory(companyId: string, query: PaymentHistoryQuery = {}): Promise<PaymentHistoryResponse> {
    console.log('Getting payment history for company:', companyId, query)

    const params = new URLSearchParams()
    if (query.page) params.append('page', query.page.toString())
    if (query.limit) params.append('limit', query.limit.toString())
    if (query.status) params.append('status', query.status)
    if (query.module) params.append('module', query.module)
    if (query.dateFrom) params.append('dateFrom', query.dateFrom)
    if (query.dateTo) params.append('dateTo', query.dateTo)

    const response = await apiClient.get<{ success: boolean; data: PaymentHistoryResponse }>(
      `/payments/company/${companyId}/history?${params.toString()}`
    )

    if (!response.data.success) {
      throw new Error('Failed to get payment history')
    }

    return response.data.data
  }

  async checkPaymentStatus(paymentId: string): Promise<{
    paymentId: string
    status: string
    amount: number
    currency: string
    paidAt?: Date
    transactionId?: string
    bankReference?: string
  }> {
    console.log('Checking payment status:', paymentId)

    const response = await apiClient.get<{
      success: boolean
      data: {
        paymentId: string
        status: string
        amount: number
        currency: string
        paidAt?: Date
        transactionId?: string
        bankReference?: string
      }
    }>(`/payments/${paymentId}/status`)

    if (!response.data.success) {
      throw new Error('Failed to check payment status')
    }

    return response.data.data
  }

  async simulatePaymentSuccess(paymentId: string): Promise<void> {
    console.log('Simulating payment success:', paymentId)

    const response = await apiClient.post<{ success: boolean; message: string }>(
      `/payments/${paymentId}/simulate-success`
    )

    if (!response.data.success) {
      throw new Error('Failed to simulate payment success')
    }

    console.log('Payment simulation successful:', response.data.message)
  }

  async redirectToACLEDA(paymentResponse: PaymentResponse): Promise<void> {
    if (!paymentResponse.redirectUrl) {
      throw new Error('No redirect URL provided for ACLEDA payment')
    }

    console.log('Redirecting to ACLEDA Bank:', paymentResponse.redirectUrl)

    // Store payment details for when user returns
    this.storePaymentSession(paymentResponse)

    // Redirect to ACLEDA Bank payment page
    window.location.href = paymentResponse.redirectUrl
  }

  private storePaymentSession(paymentResponse: PaymentResponse): void {
    const sessionData = {
      paymentId: paymentResponse.paymentId,
      amount: paymentResponse.amount,
      currency: paymentResponse.currency,
      sessionId: paymentResponse.sessionId,
      paymentTokenId: paymentResponse.paymentTokenId,
      expiresAt: paymentResponse.expiresAt,
      createdAt: new Date().toISOString()
    }

    sessionStorage.setItem('acleda_payment_session', JSON.stringify(sessionData))
  }

  getStoredPaymentSession(): {
    paymentId: string
    amount: number
    currency: string
    sessionId?: string
    paymentTokenId?: string
    expiresAt: Date
    createdAt: string
  } | null {
    const stored = sessionStorage.getItem('acleda_payment_session')
    if (!stored) return null

    try {
      return JSON.parse(stored)
    } catch {
      return null
    }
  }

  clearPaymentSession(): void {
    sessionStorage.removeItem('acleda_payment_session')
  }

  private async getClientIP(): Promise<string | undefined> {
    try {
      // This is a simple IP detection - in production you might want to use a more reliable service
      const response = await fetch('https://api.ipify.org?format=json')
      const data = await response.json()
      return data.ip
    } catch {
      // IP detection failed, return undefined
      return undefined
    }
  }

  // Utility methods for payment status polling
  async startStatusPolling(paymentId: string, onStatusChange: (status: any) => void, intervalMs = 5000): Promise<void> {
    const poll = async () => {
      try {
        const status = await this.checkPaymentStatus(paymentId)
        onStatusChange(status)

        // Stop polling if payment is completed or failed
        if (status.status === 'completed' || status.status === 'failed' || status.status === 'cancelled') {
          return
        }

        // Continue polling
        setTimeout(poll, intervalMs)
      } catch (error) {
        console.error('Payment status polling error:', error)
        // Continue polling on error
        setTimeout(poll, intervalMs)
      }
    }

    poll()
  }

  formatPaymentMethod(method: PaymentMethod): string {
    const methods: Record<PaymentMethod, string> = {
      'ACLEDA_ECOMMERCE': 'ACLEDA E-commerce',
      'ACLEDA_REDIRECT': 'ACLEDA Redirect',
      'BANK_TRANSFER': 'Bank Transfer',
      'KHQR': 'KHQR',
      'ACLEDA_POS': 'ACLEDA POS',
      'ACLEDA_MOBILE': 'ACLEDA Mobile'
    }
    return methods[method] || method
  }

  formatCurrency(amount: number, currency: Currency = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount)
  }
}

export const paymentService = new PaymentService()
