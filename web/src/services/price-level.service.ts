import type {
  PriceLevel,
  CreatePriceLevelRequest,
  UpdatePriceLevelRequest,
  ProductPrice,
  SetProductPriceRequest
} from '../../../shared/types/pos';
import { apiClient } from '@/lib/api-client';

interface ServerResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export class PriceLevelService {
  private readonly baseUrl = '/api/pos/price-levels';

      // Price Level Management
      async getPriceLevels(companyId: string): Promise<PriceLevel[]> {
    try {
      const response = await apiClient.get(`${this.baseUrl}?companyId=${companyId}`);
      const serverResponse = response.data as ServerResponse<PriceLevel[]>;

      if (!serverResponse.success) {
        throw new Error(serverResponse.error || 'Failed to fetch price levels');
      }

      return serverResponse.data || [];
    } catch (error) {
      console.error('Error fetching price levels:', error);
      throw error;
    }
  }

  async getPriceLevelById(companyId: string, priceLevelId: string): Promise<PriceLevel> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/${priceLevelId}?companyId=${companyId}`
      );
      const serverResponse = response.data as ServerResponse<PriceLevel>;

      if (!serverResponse.success) {
        throw new Error(serverResponse.error || 'Failed to fetch price level');
      }

      return serverResponse.data!;
    } catch (error) {
      console.error('Error fetching price level:', error);
      throw error;
    }
  }

  async createPriceLevel(companyId: string, data: CreatePriceLevelRequest): Promise<PriceLevel> {
    try {
      const response = await apiClient.post(this.baseUrl, {
        companyId,
        ...data
      });
      const serverResponse = response.data as ServerResponse<PriceLevel>;

      if (!serverResponse.success) {
        throw new Error(serverResponse.error || 'Failed to create price level');
      }

      return serverResponse.data!;
    } catch (error) {
      console.error('Error creating price level:', error);
      throw error;
    }
  }

  async updatePriceLevel(
    companyId: string,
    priceLevelId: string,
    data: UpdatePriceLevelRequest
  ): Promise<PriceLevel> {
    try {
      const response = await apiClient.put(`${this.baseUrl}/${priceLevelId}`, {
        companyId,
        ...data
      });
      const serverResponse = response.data as ServerResponse<PriceLevel>;

      if (!serverResponse.success) {
        throw new Error(serverResponse.error || 'Failed to update price level');
      }

      return serverResponse.data!;
    } catch (error) {
      console.error('Error updating price level:', error);
      throw error;
    }
  }

  async deletePriceLevel(companyId: string, priceLevelId: string): Promise<void> {
    try {
      const response = await apiClient.delete(
        `${this.baseUrl}/${priceLevelId}?companyId=${companyId}`
      );
      const serverResponse = response.data as ServerResponse;

      if (!serverResponse.success) {
        throw new Error(serverResponse.error || 'Failed to delete price level');
      }
    } catch (error) {
      console.error('Error deleting price level:', error);
      throw error;
    }
  }

  async reorderPriceLevels(companyId: string, priceLevelIds: string[]): Promise<void> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/reorder`, {
        companyId,
        priceLevelIds
      });
      const serverResponse = response.data as ServerResponse;

      if (!serverResponse.success) {
        throw new Error(serverResponse.error || 'Failed to reorder price levels');
      }
    } catch (error) {
      console.error('Error reordering price levels:', error);
      throw error;
    }
  }

  async getDefaultPriceLevel(companyId: string): Promise<PriceLevel | null> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/default/info?companyId=${companyId}`
      );
      const serverResponse = response.data as ServerResponse<PriceLevel>;

      if (!serverResponse.success) {
        throw new Error(serverResponse.error || 'Failed to fetch default price level');
      }

      return serverResponse.data || null;
    } catch (error) {
      console.error('Error fetching default price level:', error);
      throw error;
    }
  }

  async initializeDefaultPriceLevels(companyId: string): Promise<PriceLevel[]> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/initialize-defaults`, {
        companyId
      });
      const serverResponse = response.data as ServerResponse<PriceLevel[]>;

      if (!serverResponse.success) {
        throw new Error(serverResponse.error || 'Failed to initialize default price levels');
      }

      return serverResponse.data || [];
    } catch (error) {
      console.error('Error initializing default price levels:', error);
      throw error;
    }
  }

  // Product Price Management
  async getProductPrices(companyId: string, productId: string): Promise<ProductPrice[]> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/products/${productId}/prices?companyId=${companyId}`
      );

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch product prices');
      }

      return response.data || [];
    } catch (error) {
      console.error('Error fetching product prices:', error);
      throw error;
    }
  }

  async setProductPrice(companyId: string, data: SetProductPriceRequest): Promise<ProductPrice> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/products/prices`, {
        companyId,
        ...data
      });

      if (!response.success) {
        throw new Error(response.error || 'Failed to set product price');
      }

      return response.data;
    } catch (error) {
      console.error('Error setting product price:', error);
      throw error;
    }
  }

  async removeProductPrice(
    companyId: string,
    productId: string,
    priceLevelId: string
  ): Promise<void> {
    try {
      const response = await apiClient.delete(
        `${this.baseUrl}/products/${productId}/prices/${priceLevelId}?companyId=${companyId}`
      );

      if (!response.success) {
        throw new Error(response.error || 'Failed to remove product price');
      }
    } catch (error) {
      console.error('Error removing product price:', error);
      throw error;
    }
  }

  async getProductPriceByLevel(
    companyId: string,
    productId: string,
    priceLevelId: string
  ): Promise<ProductPrice | null> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/products/${productId}/prices/${priceLevelId}?companyId=${companyId}`
      );

      if (!response.success) {
        throw new Error(response.error || 'Failed to fetch product price');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching product price:', error);
      throw error;
    }
  }

  // Utility methods
  getPriceInCurrency(price: ProductPrice, currency: 'KHR' | 'USD'): number {
    return currency === 'USD' ? price.priceUSD : price.priceKHR;
  }

  formatPrice(amount: number, currency: 'KHR' | 'USD'): string {
    if (currency === 'USD') {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
      }).format(amount);
    } else {
      return new Intl.NumberFormat('km-KH', {
        style: 'currency',
        currency: 'KHR',
        minimumFractionDigits: 0,
      }).format(amount);
    }
  }

  calculateDiscountedPrice(basePrice: number, discountPercentage: number): number {
    if (!discountPercentage || discountPercentage <= 0) return basePrice;
    return basePrice * (1 - discountPercentage / 100);
  }
}

// Create and export a singleton instance
export const priceLevelService = new PriceLevelService();
