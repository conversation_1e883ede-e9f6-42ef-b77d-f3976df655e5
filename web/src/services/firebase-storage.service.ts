import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage'
import { storage } from '@/lib/firebase'
import { useAuthStore } from '@/stores/auth'

export interface UploadProgress {
  progress: number
  url?: string
  error?: string
}

export interface UploadResult {
  url: string
  path: string
  name: string
  size: number
  type: string
}

class FirebaseStorageService {
  private readonly basePath = 'products'
  
  /**
   * Upload a single file to Firebase Storage
   */
  async uploadFile(
    file: File,
    folder: string = 'general',
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    try {
      // Get current user for organizing files
      const authStore = useAuthStore()
      const companyId = authStore.currentUser?.currentCompanyId || 'default'
      
      // Create unique filename
      const timestamp = Date.now()
      const randomString = Math.random().toString(36).substring(2, 15)
      const fileExtension = file.name.split('.').pop()
      const fileName = `${timestamp}_${randomString}.${fileExtension}`
      
      // Create storage reference
      const storagePath = `${this.basePath}/${companyId}/${folder}/${fileName}`
      const storageRef = ref(storage, storagePath)
      
      // Start upload
      onProgress?.({ progress: 0 })
      
      // Upload file
      const snapshot = await uploadBytes(storageRef, file)
      
      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref)
      
      onProgress?.({ progress: 100, url: downloadURL })
      
      return {
        url: downloadURL,
        path: storagePath,
        name: fileName,
        size: file.size,
        type: file.type
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      onProgress?.({ progress: 0, error: errorMessage })
      throw new Error(errorMessage)
    }
  }
  
  /**
   * Upload multiple files
   */
  async uploadFiles(
    files: File[],
    folder: string = 'general',
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = []
    const totalFiles = files.length
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      
      try {
        const result = await this.uploadFile(file, folder, (fileProgress) => {
          // Calculate overall progress
          const overallProgress = ((i / totalFiles) * 100) + (fileProgress.progress / totalFiles)
          onProgress?.({ 
            progress: Math.round(overallProgress),
            url: fileProgress.url
          })
        })
        
        results.push(result)
      } catch (error) {
        console.error(`Failed to upload file ${file.name}:`, error)
        // Continue with other files even if one fails
      }
    }
    
    return results
  }
  
  /**
   * Delete a file from Firebase Storage
   */
  async deleteFile(path: string): Promise<void> {
    try {
      const fileRef = ref(storage, path)
      await deleteObject(fileRef)
    } catch (error) {
      console.error('Error deleting file:', error)
      throw new Error('Failed to delete file')
    }
  }
  
  /**
   * Delete multiple files
   */
  async deleteFiles(paths: string[]): Promise<void> {
    const deletePromises = paths.map(path => this.deleteFile(path))
    await Promise.all(deletePromises)
  }
  
  /**
   * Get optimized image URL with transformations
   */
  getOptimizedImageUrl(url: string, options?: {
    width?: number
    height?: number
    quality?: number
  }): string {
    if (!options) return url
    
    // For Firebase Storage, we would need to implement this with a cloud function
    // or use a service like Cloudinary. For now, return the original URL
    return url
  }
  
  /**
   * Validate file before upload
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { valid: false, error: 'Only image files are allowed' }
    }
    
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      return { valid: false, error: 'File size must be less than 10MB' }
    }
    
    // Check file extensions
    const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']
    const fileExtension = file.name.split('.').pop()?.toLowerCase()
    if (!fileExtension || !allowedExtensions.includes(fileExtension)) {
      return { valid: false, error: 'Invalid file type. Allowed: JPG, PNG, GIF, WebP' }
    }
    
    return { valid: true }
  }
  
  /**
   * Validate multiple files
   */
  validateFiles(files: File[]): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    for (const file of files) {
      const validation = this.validateFile(file)
      if (!validation.valid && validation.error) {
        errors.push(`${file.name}: ${validation.error}`)
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
}

// Export singleton instance
export const firebaseStorageService = new FirebaseStorageService()
export default firebaseStorageService 