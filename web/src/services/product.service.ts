import { apiClient } from '../lib/api-client'
import type { Product, Category, ProductModifier, RestaurantProductFeatures } from '../../../shared/types/pos'

export interface CreateProductRequest {
  name: string;
  nameKhmer?: string;
  description?: string;
  descriptionKhmer?: string;
  sku: string;
  barcode?: string;
  price: number;
  priceUSD?: number;
  cost: number;
  costUSD?: number;
  stock: number;
  minStock: number;
  categoryId: string;
  unit: string;
  supplier?: string;
  expiryDate?: Date;
  batchNumber?: string;
  restaurantFeatures?: RestaurantProductFeatures;
  images?: string[];
  thumbnail?: string;
  isAvailable?: boolean;
  availableFrom?: string;
  availableUntil?: string;
  daysAvailable?: string[];
  modifiers?: ProductModifier[];
}

export interface UpdateProductRequest {
  name?: string;
  nameKhmer?: string;
  description?: string;
  descriptionKhmer?: string;
  sku?: string;
  barcode?: string;
  price?: number;
  priceUSD?: number;
  cost?: number;
  costUSD?: number;
  stock?: number;
  minStock?: number;
  categoryId?: string;
  unit?: string;
  supplier?: string;
  expiryDate?: Date;
  batchNumber?: string;
  restaurantFeatures?: RestaurantProductFeatures;
  images?: string[];
  thumbnail?: string;
  isAvailable?: boolean;
  availableFrom?: string;
  availableUntil?: string;
  daysAvailable?: string[];
  modifiers?: ProductModifier[];
  isActive?: boolean;
}

export interface ProductListOptions {
  page?: number;
  limit?: number;
  search?: string;
  categoryId?: string;
  isAvailable?: boolean;
  spiceLevel?: string;
  mealTime?: string;
  dietaryRestriction?: string;
  isBBQItem?: boolean;
  sortBy?: 'name' | 'price' | 'stock' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

export interface ProductListResponse {
  products: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

class ProductService {
  private baseUrl = '/api/pos/products';

  async getProducts(companyId: string, options: ProductListOptions = {}): Promise<ProductListResponse> {
    const params = new URLSearchParams({
      companyId,
      ...Object.entries(options).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null) {
          acc[key] = value.toString();
        }
        return acc;
      }, {} as Record<string, string>)
    });

    const response = await apiClient.get(`${this.baseUrl}?${params}`);
    
    // Handle wrapped response format {success: true, data: {...}}
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      return (response.data as any).data as ProductListResponse;
    }
    
    return response.data as ProductListResponse;
  }

  async getProduct(id: string): Promise<Product> {
    const response = await apiClient.get(`${this.baseUrl}/${id}`);
    return response.data as Product;
  }

  async createProduct(companyId: string, product: CreateProductRequest): Promise<Product> {
    const response = await apiClient.post(this.baseUrl, {
      ...product,
      companyId,
    });
    return response.data as Product;
  }

  async updateProduct(id: string, product: UpdateProductRequest): Promise<Product> {
    const response = await apiClient.put(`${this.baseUrl}/${id}`, product);
    return response.data as Product;
  }

  async deleteProduct(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}`);
  }

  async updateStock(id: string, stock: number): Promise<Product> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/stock`, { stock });
    return response.data as Product;
  }

  async bulkUpdateStock(updates: { productId: string; stock: number }[]): Promise<void> {
    await apiClient.patch(`${this.baseUrl}/bulk-stock`, { updates });
  }

  async getProductsByCategory(categoryId: string, companyId: string): Promise<Product[]> {
    const params = new URLSearchParams({ companyId });
    const response = await apiClient.get(`${this.baseUrl}/category/${categoryId}?${params}`);
    return response.data as Product[];
  }

  async getQRMenuProducts(companyId: string): Promise<Product[]> {
    const params = new URLSearchParams({ companyId });
    const response = await apiClient.get(`${this.baseUrl}/qr-menu?${params}`);
    return response.data as Product[];
  }

  async getBBQProducts(companyId: string): Promise<Product[]> {
    const params = new URLSearchParams({ companyId });
    const response = await apiClient.get(`${this.baseUrl}/bbq?${params}`);
    return response.data as Product[];
  }

  async getProductsByMealTime(mealTime: string, companyId: string): Promise<Product[]> {
    const params = new URLSearchParams({ companyId });
    const response = await apiClient.get(`${this.baseUrl}/meal-time/${mealTime}?${params}`);
    return response.data as Product[];
  }

  async searchProducts(query: string, companyId: string): Promise<Product[]> {
    const params = new URLSearchParams({ query, companyId });
    const response = await apiClient.get(`${this.baseUrl}/search?${params}`);
    return response.data as Product[];
  }

  async uploadProductImage(productId: string, file: File): Promise<string> {
    const formData = new FormData();
    formData.append('image', file);
    
    const response = await apiClient.post(`${this.baseUrl}/${productId}/image`, formData);
    return (response.data as any).url;
  }

  async toggleAvailability(id: string, isAvailable: boolean): Promise<Product> {
    const response = await apiClient.patch(`${this.baseUrl}/${id}/availability`, { isAvailable });
    return response.data as Product;
  }

  async duplicateProduct(id: string): Promise<Product> {
    const response = await apiClient.post(`${this.baseUrl}/${id}/duplicate`);
    return response.data as Product;
  }

  // Restaurant-specific methods
  async getKhmerTraditionalProducts(companyId: string): Promise<Product[]> {
    const params = new URLSearchParams({ companyId });
    const response = await apiClient.get(`${this.baseUrl}/khmer-traditional?${params}`);
    return response.data as Product[];
  }

  async getProductsBySpiceLevel(spiceLevel: string, companyId: string): Promise<Product[]> {
    const params = new URLSearchParams({ companyId });
    const response = await apiClient.get(`${this.baseUrl}/spice-level/${spiceLevel}?${params}`);
    return response.data as Product[];
  }

  async getProductsByRegion(region: string, companyId: string): Promise<Product[]> {
    const params = new URLSearchParams({ companyId });
    const response = await apiClient.get(`${this.baseUrl}/region/${region}?${params}`);
    return response.data as Product[];
  }
}

export const productService = new ProductService(); 