import apiClient from '../lib/api-client'
import type {
  Category,
  CategoryTree,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CategoryMoveRequest
} from '../../../shared/types/pos'

export class CategoryService {
  private baseUrl = '/api/pos/categories'

  async getCategoryTree(companyId: string): Promise<CategoryTree> {
    const response = await apiClient.get<{success: boolean, data: CategoryTree}>(`${this.baseUrl}/tree?companyId=${companyId}`)
    if (response.data.success) {
      return response.data.data
    }
    throw new Error('Failed to fetch category tree')
  }

  async createCategory(companyId: string, data: CreateCategoryRequest): Promise<Category> {
    const response = await apiClient.post<{success: boolean, data: Category}>(this.baseUrl, {
      ...data,
      companyId
    })
    if (response.data.success) {
      return response.data.data
    }
    throw new Error('Failed to create category')
  }

  async updateCategory(companyId: string, categoryId: string, data: UpdateCategoryRequest): Promise<Category> {
    const response = await apiClient.put<{success: boolean, data: Category}>(`${this.baseUrl}/${categoryId}`, {
      ...data,
      companyId
    })
    if (response.data.success) {
      return response.data.data
    }
    throw new Error('Failed to update category')
  }

  async deleteCategory(companyId: string, categoryId: string): Promise<boolean> {
    const response = await apiClient.delete<{success: boolean}>(`${this.baseUrl}/${categoryId}?companyId=${companyId}`)
    return response.data.success
  }

  async moveCategory(companyId: string, categoryId: string, data: CategoryMoveRequest): Promise<Category> {
    const response = await apiClient.post<Category>(`${this.baseUrl}/${categoryId}/move`, {
      ...data,
      companyId
    })
    return response.data
  }

  async getCategoriesByLevel(companyId: string, level: number): Promise<Category[]> {
    const response = await apiClient.get<Category[]>(`${this.baseUrl}/level/${level}?companyId=${companyId}`)
    return response.data
  }

  async getCategories(companyId: string): Promise<Category[]> {
    const response = await apiClient.get<{success: boolean, data: Category[]}>(`${this.baseUrl}?companyId=${companyId}`)
    if (response.data.success) {
      return response.data.data
    }
    throw new Error('Failed to fetch categories')
  }

}

export const categoryService = new CategoryService()
