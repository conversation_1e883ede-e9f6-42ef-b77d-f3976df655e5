import apiClient from '@/lib/api-client'
import type {
  Company,
  CreateCompanyRequest,
  UpdateCompanyRequest,
  CompanySubscription,
  SubscribeRequest,
  StartTrialRequest,
  TrialInfo,
  BillingInfo
} from '@/types/company'

// Backend response wrapper interface
interface BackendResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

export class CompanyService {
  // Company Management
  async createCompany(companyData: CreateCompanyRequest): Promise<Company> {
    const response = await apiClient.post<BackendResponse<Company>>('/companies', companyData)
    return response.data.data
  }

  async getCompany(companyId: string): Promise<Company> {
    const response = await apiClient.get<BackendResponse<Company>>(`/companies/detail/${companyId}`)
    return response.data.data
  }

  async updateCompany(companyId: string, companyData: UpdateCompanyRequest): Promise<Company> {
    const response = await apiClient.patch<BackendResponse<Company>>(`/companies/detail/${companyId}`, companyData)
    return response.data.data
  }

  async deleteCompany(companyId: string): Promise<void> {
    await apiClient.delete(`/companies/detail/${companyId}`)
  }

  async getUserCompanies(userId: string): Promise<Company[]> {
    const response = await apiClient.get<BackendResponse<Company[]>>(`/companies/user/${userId}`)
    return response.data.data
  }

  // Subscription Management
  async getCompanySubscriptions(companyId: string): Promise<CompanySubscription[]> {
    const response = await apiClient.get<BackendResponse<CompanySubscription[]>>(`/subscriptions/company/${companyId}`)
    return response.data.data
  }

  async getAllCompanySubscriptions(companyId: string): Promise<CompanySubscription[]> {
    const response = await apiClient.get<BackendResponse<CompanySubscription[]>>(`/subscriptions/company/${companyId}/all`)
    return response.data.data
  }

  async subscribeToModule(companyId: string, subscriptionData: SubscribeRequest): Promise<CompanySubscription> {
    const response = await apiClient.post<BackendResponse<CompanySubscription>>(
      `/subscriptions/company/${companyId}/subscribe`,
      subscriptionData
    )
    return response.data.data
  }

  async subscribeToPOS(companyId: string, subscriptionData: Omit<SubscribeRequest, 'module'>): Promise<CompanySubscription> {
    const response = await apiClient.post<BackendResponse<CompanySubscription>>(
      `/subscriptions/company/${companyId}/subscribe-pos`,
      subscriptionData
    )
    return response.data.data
  }

  async unsubscribeFromModule(companyId: string, module: string): Promise<{ success: boolean }> {
    const response = await apiClient.delete<BackendResponse<{ success: boolean }>>(
      `/subscriptions/company/${companyId}/unsubscribe/${module}`
    )
    return response.data.data
  }

  // Trial Management
  async startTrial(companyId: string, trialData: StartTrialRequest): Promise<CompanySubscription> {
    const response = await apiClient.post<BackendResponse<CompanySubscription>>(
      `/subscriptions/company/${companyId}/start-trial`,
      trialData
    )
    return response.data.data
  }

  async startPOSTrial(companyId: string, trialData: Omit<StartTrialRequest, 'module'>): Promise<CompanySubscription> {
    const response = await apiClient.post<BackendResponse<CompanySubscription>>(
      `/subscriptions/company/${companyId}/start-trial-pos`,
      trialData
    )
    return response.data.data
  }

  async getTrialInfo(companyId: string, module: string): Promise<TrialInfo> {
    const response = await apiClient.get<BackendResponse<TrialInfo>>(`/subscriptions/company/${companyId}/trial/${module}`)
    return response.data.data
  }

  async getTrialStatus(companyId: string): Promise<any> {
    const response = await apiClient.get<BackendResponse<any>>(`/subscriptions/company/${companyId}/trial-status`)
    return response.data.data
  }

  async getCompanyTrialStatus(companyId: string): Promise<any> {
    const response = await apiClient.get<BackendResponse<any>>(`/subscriptions/company/${companyId}/trial-status`)
    return response.data.data
  }

  async convertTrialToPaid(companyId: string, module: string): Promise<CompanySubscription> {
    const response = await apiClient.post<BackendResponse<CompanySubscription>>(
      `/subscriptions/company/${companyId}/convert-trial/${module}`
    )
    return response.data.data
  }

  // Billing Management
  async getBillingInfo(companyId: string): Promise<BillingInfo> {
    const response = await apiClient.get<BackendResponse<BillingInfo>>(`/subscriptions/company/${companyId}/billing`)
    return response.data.data
  }

  async processMonthlyBilling(companyId: string): Promise<BillingInfo> {
    const response = await apiClient.post<BackendResponse<BillingInfo>>(`/subscriptions/company/${companyId}/process-billing`)
    return response.data.data
  }

  async updateSubscriptionPrice(subscriptionId: string, newPrice: number): Promise<CompanySubscription> {
    const response = await apiClient.patch<BackendResponse<CompanySubscription>>(
      `/subscriptions/subscription/${subscriptionId}/price`,
      { price: newPrice }
    )
    return response.data.data
  }

  // Module Status
  async getModuleStatus(companyId: string, module: string): Promise<{ active: boolean; subscription?: CompanySubscription }> {
    const response = await apiClient.get<BackendResponse<{ active: boolean; subscription?: CompanySubscription }>>(`/subscriptions/company/${companyId}/module/${module}/status`)
    return response.data.data
  }

  async getPOSType(companyId: string): Promise<{ posType: string | null }> {
    const response = await apiClient.get<BackendResponse<{ posType: string | null }>>(`/subscriptions/company/${companyId}/pos-type`)
    return response.data.data
  }

  async getAvailableModules(): Promise<{
    modules: Array<{ name: string; price: number }>;
    posTypes: Array<{ type: string; price: number }>;
  }> {
    const response = await apiClient.get<BackendResponse<{
      modules: Array<{ name: string; price: number }>;
      posTypes: Array<{ type: string; price: number }>;
    }>>('/subscriptions/modules')
    return response.data.data
  }

  // Company Users
  async addUserToCompany(companyId: string, userData: { email: string; role: string }): Promise<void> {
    await apiClient.post(`/companies/${companyId}/users`, userData)
  }

  async removeUserFromCompany(companyId: string, userId: string): Promise<void> {
    await apiClient.delete(`/companies/${companyId}/users/${userId}`)
  }

  async getCompanyUsers(companyId: string): Promise<any[]> {
    const response = await apiClient.get<BackendResponse<any[]>>(`/auth/users/company/${companyId}`)
    return response.data.data
  }
}

export const companyService = new CompanyService()
