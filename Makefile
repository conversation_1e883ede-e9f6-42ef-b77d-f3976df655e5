dev:
	@echo "🚀 Starting all ElyPOS services with hot reload..."
	@tmux kill-session -t elypos-dev 2>/dev/null || true
	@tmux new-session -d -s elypos-dev -n "core" "cd services/core && bun run dev"
	@tmux new-window -t elypos-dev -n "modules" "cd services/loan && bun run dev" \; \
		split-window -v "cd services/pos && bun run dev" \; \
		select-layout even-vertical
	@tmux new-window -t elypos-dev -n "gateway" "cd gateway && bun run dev"
	@tmux select-window -t elypos-dev:core
	@echo "✅ All ElyPOS services started with hot reload in tmux session 'elypos-dev'"
	@echo "📱 Access the services:"
	@echo "   - Core Service Health: http://localhost:3001/health"
	@echo "   - Loan Service Health: http://localhost:3003/health"
	@echo "   - POS Service Health: http://localhost:3002/health"
	@echo "   - API Gateway: http://localhost:3000/health"
	@echo "   - Gateway Dashboard: http://localhost:3000/swagger"
	@echo ""
	@echo "💡 Tmux windows:"
	@echo "   - core: Core authentication and company services"
	@echo "   - modules: Loan (top) and POS (bottom) services"
	@echo "   - gateway: API Gateway with service routing"
	@echo ""
	@echo "🔗 Attach to session: tmux attach -t elypos-dev"
	@echo "🛑 Stop all services: make stop"
	@tmux attach-session -t elypos-dev

stop:
	@echo "🛑 Stopping all ElyPOS services..."
	@tmux kill-session -t elypos-dev 2>/dev/null || true
	@echo "✅ All services stopped"

logs:
	@echo "📋 Viewing logs for all services..."
	@tmux attach-session -t elypos-dev

status:
	@echo "📊 Checking service status..."
	@echo "Tmux session:"
	@tmux list-sessions | grep elypos-dev || echo "❌ elypos-dev session not running"
	@echo ""
	@echo "Service health checks:"
	@curl -s http://localhost:3000/health 2>/dev/null && echo "✅ Gateway: Running" || echo "❌ Gateway: Down"
	@curl -s http://localhost:3001/health 2>/dev/null && echo "✅ Core: Running" || echo "❌ Core: Down"
	@curl -s http://localhost:3002/health 2>/dev/null && echo "✅ POS: Running" || echo "❌ POS: Down"
	@curl -s http://localhost:3003/health 2>/dev/null && echo "✅ Loan: Running" || echo "❌ Loan: Down"

restart:
	@echo "🔄 Restarting all ElyPOS services..."
	@make stop
	@sleep 2
	@make dev

install:
	@echo "📦 Installing dependencies for all services..."
	@echo "Installing root dependencies..."
	@bun install
	@echo "Installing gateway dependencies..."
	@cd gateway && bun install
	@echo "Installing core service dependencies..."
	@cd services/core && bun install
	@echo "Installing POS service dependencies..."
	@cd services/pos && bun install
	@echo "Installing loan service dependencies..."
	@cd services/loan && bun install
	@echo "✅ All dependencies installed"

clean:
	@echo "🧹 Cleaning build artifacts..."
	@rm -rf gateway/dist
	@rm -rf services/core/dist
	@rm -rf services/pos/dist
	@rm -rf services/loan/dist
	@echo "✅ Clean complete"

build:
	@echo "🏗️ Building all services..."
	@cd gateway && bun run build
	@cd services/core && bun run build
	@cd services/pos && bun run build
	@cd services/loan && bun run build
	@echo "✅ All services built"

test:
	@echo "🧪 Running API tests..."
	@echo "Testing Gateway..."
	@curl -s http://localhost:3000/ | grep "elypos-gateway" && echo "✅ Gateway: OK" || echo "❌ Gateway: Failed"
	@echo "Testing Core Service..."
	@curl -s http://localhost:3001/health | grep "elypos-core" && echo "✅ Core: OK" || echo "❌ Core: Failed"
	@echo "Testing POS Service..."
	@curl -s http://localhost:3002/health | grep "elypos-pos" && echo "✅ POS: OK" || echo "❌ POS: Failed"
	@echo "Testing Loan Service..."
	@curl -s http://localhost:3003/health | grep "elypos-loan" && echo "✅ Loan: OK" || echo "❌ Loan: Failed"
	@echo ""
	@echo "Testing user registration..."
	@TIMESTAMP=$$(date +%s) && \
	curl -s -X POST http://localhost:3000/api/core/auth/register \
		-H "Content-Type: application/json" \
		-d "{\"email\":\"test$$<EMAIL>\",\"password\":\"password123\",\"firstName\":\"Test\",\"lastName\":\"User\",\"companyId\":\"507f1f77bcf86cd799439011\"}" \
		| grep "success" && echo "✅ Registration: OK" || echo "❌ Registration: Failed"

docker-dev:
	@echo "🐳 Starting ElyPOS with Docker Compose..."
	@docker-compose up -d
	@echo "✅ ElyPOS started with Docker"
	@echo "📱 Services available at:"
	@echo "   - Gateway: http://localhost:3000"
	@echo "   - Core: http://localhost:3001"
	@echo "   - POS: http://localhost:3002"
	@echo "   - Loan: http://localhost:3003"

docker-stop:
	@echo "🐳 Stopping Docker services..."
	@docker-compose down
	@echo "✅ Docker services stopped"

help:
	@echo "🔧 ElyPOS Development Commands:"
	@echo ""
	@echo "Development:"
	@echo "  make dev          - Start all services with tmux and hot reload"
	@echo "  make stop         - Stop all running services"
	@echo "  make restart      - Restart all services"
	@echo "  make logs         - Attach to tmux session to view logs"
	@echo "  make status       - Check service status"
	@echo ""
	@echo "Building & Testing:"
	@echo "  make install      - Install all dependencies"
	@echo "  make build        - Build all services"
	@echo "  make clean        - Clean build artifacts"
	@echo "  make test         - Run API health tests"
	@echo ""
	@echo "Docker:"
	@echo "  make docker-dev   - Start with Docker Compose"
	@echo "  make docker-stop  - Stop Docker services"
	@echo ""
	@echo "Utilities:"
	@echo "  make help         - Show this help message"

.PHONY: dev stop logs status restart install clean build test docker-dev docker-stop help